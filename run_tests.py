#!/usr/bin/env python3
"""
Test runner script for LangChainAgent project.
Provides easy commands to run different types of tests.
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"\n✅ {description} completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ {description} failed with exit code {e.returncode}")
        return False


def main():
    parser = argparse.ArgumentParser(description='Run LangChainAgent tests')
    parser.add_argument(
        '--type', 
        choices=['unit', 'integration', 'llm', 'agent', 'all'],
        default='all',
        help='Type of tests to run'
    )
    parser.add_argument(
        '--coverage', 
        action='store_true',
        help='Generate coverage report'
    )
    parser.add_argument(
        '--verbose', 
        action='store_true',
        help='Verbose output'
    )
    parser.add_argument(
        '--fast', 
        action='store_true',
        help='Run only fast tests (skip integration)'
    )
    
    args = parser.parse_args()
    
    # Base pytest command
    base_cmd = ['python', '-m', 'pytest']
    
    if args.verbose:
        base_cmd.append('-v')
    
    if args.coverage:
        base_cmd.extend(['--cov=core', '--cov-report=term-missing', '--cov-report=html'])
    
    # Determine test path
    test_path = Path(__file__).parent / 'core' / 'tests'
    
    if args.type == 'all':
        if args.fast:
            # Run only unit tests
            cmd = base_cmd + [str(test_path), '-m', 'unit']
            success = run_command(cmd, "Unit tests")
        else:
            # Run all tests
            cmd = base_cmd + [str(test_path)]
            success = run_command(cmd, "All tests")
    
    elif args.type == 'unit':
        cmd = base_cmd + [str(test_path), '-m', 'unit']
        success = run_command(cmd, "Unit tests")
    
    elif args.type == 'integration':
        cmd = base_cmd + [str(test_path), '-m', 'integration']
        success = run_command(cmd, "Integration tests")
    
    elif args.type == 'llm':
        cmd = base_cmd + [str(test_path), '-m', 'llm']
        success = run_command(cmd, "LLM-related tests")
    
    elif args.type == 'agent':
        cmd = base_cmd + [str(test_path), '-m', 'agent']
        success = run_command(cmd, "Agent-related tests")
    
    # Summary
    print(f"\n{'='*60}")
    if success:
        print("🎉 All tests completed successfully!")
    else:
        print("💥 Some tests failed!")
        sys.exit(1)
    print(f"{'='*60}")


if __name__ == '__main__':
    main() 