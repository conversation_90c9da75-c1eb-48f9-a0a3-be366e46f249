from uuid import uuid4

from api.database import Base, engine
from api.db_session import get_db_session
from core.agents.product_specialist import ProductSpecialistAgent
from core.utils.config_loader import ConfigLoader
from core.utils.llm_manager import LlmManager
from core.agents.orchestrator import OrchestratorAgent
import os
from dotenv import load_dotenv

load_dotenv("config/.env")


class ChatBotAPIWrapper:
    def __init__(self):

        # Cria banco de dados
        # Base.metadata.create_all(bind=engine)
        #
        # with get_db_session() as session:
        #     novo_agente = OrchestratorAgentModel(
        #         id=str(uuid4()),
        #         type="orchestrator_agent",
        #         role="coordenador",
        #         system_prompt="Você é um agente orquestrador.",
        #         max_tokens=200,
        #         temperature=0.3,
        #         query_agent_id=None
        #     )
        #     session.add(novo_agente)
        #     session.commit()

        # Load configuration
        config_loader = ConfigLoader()
        llm_config = config_loader.load_config("config/llm_config.yaml")["llm"]
        # Expand env vars in primary_llm and fallback_llm
        for key in ["primary_llm", "fallback_llm"]:
            val = llm_config.get(key)
            if isinstance(val, str) and val.startswith("${") and val.endswith("}"):
                env_key = val[2:-1]
                llm_config[key] = os.environ.get(env_key, val)
        agents_config = config_loader.load_config("config/agents.yaml")["agents"]

        # Initialize LLM
        llm_factory = LlmManager(llm_config)
        llm = llm_factory.get_llm_with_fallback()

        # Initialize orchestrator
        orchestrator_config = agents_config.get('orchestrator', {})
        self.orchestrator = OrchestratorAgent(orchestrator_config, llm)

        product_specialist = ProductSpecialistAgent(llm)
        self.orchestrator.append_specialist(product_specialist)

        provider = getattr(llm, 'provider', None)
        model = getattr(llm, 'model', None) or getattr(llm, 'model_name', None)
        print(f"🔍 Using LLM: provider={provider or type(llm).__name__}, model={model}")

    def get_response(self, customer_id: str, query: str) -> str:
        input_data = {"customer_id": customer_id, "query": query}
        result = self.orchestrator.execute(input_data)
        if result.get('success', False):
            return result.get('final_response', '')
        else:
            return result.get('error', 'Erro desconhecido')


# Example usage
if __name__ == "__main__":
    bot = ChatBotAPIWrapper()
    customer_id = "customer_001"
    query = "Quais smartphones vocês têm disponíveis?"
    print(bot.get_response(customer_id, query))
