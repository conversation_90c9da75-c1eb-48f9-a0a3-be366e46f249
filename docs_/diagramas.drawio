<mxfile host="Electron" modified="2025-07-29T23:41:04.194Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.2.5 Chrome/120.0.6099.109 Electron/28.1.0 Safari/537.36" etag="-T4NygLeDPlUGcF_6pSF" version="24.2.5" type="device" pages="2">
  <diagram name="UML" id="LTj1gPiyagm7XH6kqlLW">
    <mxGraphModel dx="1290" dy="-206" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="w4Hvd4hT7apZMpLXSnyB-14" style="edgeStyle=elbowEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;elbow=vertical;" parent="1" source="NR3NLs1SwqtUjtMI9eED-14" target="NR3NLs1SwqtUjtMI9eED-40" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-14" value="ManagerAgent" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="520" y="1280" width="240" height="86" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-19" value="- specialistsAgents: List&amp;lt;SpecialistAgent&amp;gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="NR3NLs1SwqtUjtMI9eED-14" vertex="1">
          <mxGeometry y="26" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-16" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="NR3NLs1SwqtUjtMI9eED-14" vertex="1">
          <mxGeometry y="52" width="240" height="8" as="geometry" />
        </mxCell>
        <mxCell id="w4Hvd4hT7apZMpLXSnyB-16" value="+ executarFluxo(): Resultado" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="NR3NLs1SwqtUjtMI9eED-14" vertex="1">
          <mxGeometry y="60" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="2ZoE_nwwO2nTcwCi95nZ-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="NR3NLs1SwqtUjtMI9eED-20" target="2ZoE_nwwO2nTcwCi95nZ-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-11" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2ZoE_nwwO2nTcwCi95nZ-10" vertex="1" connectable="0">
          <mxGeometry x="-0.7111" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-12" value="N" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="2ZoE_nwwO2nTcwCi95nZ-10" vertex="1" connectable="0">
          <mxGeometry x="0.5778" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-20" value="BaseAgent" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="720" y="920" width="260" height="242" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-21" value="- id: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="NR3NLs1SwqtUjtMI9eED-20" vertex="1">
          <mxGeometry y="26" width="260" height="26" as="geometry" />
        </mxCell>
        <mxCell id="24T6QsIyFG16VPnvyuSI-1" value="- name: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="NR3NLs1SwqtUjtMI9eED-20" vertex="1">
          <mxGeometry y="52" width="260" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-26" value="- description: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="NR3NLs1SwqtUjtMI9eED-20" vertex="1">
          <mxGeometry y="78" width="260" height="26" as="geometry" />
        </mxCell>
        <mxCell id="24T6QsIyFG16VPnvyuSI-2" value="- goals: List&amp;lt;String&amp;gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="NR3NLs1SwqtUjtMI9eED-20" vertex="1">
          <mxGeometry y="104" width="260" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-48" value="- promptTemplate: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="NR3NLs1SwqtUjtMI9eED-20" vertex="1">
          <mxGeometry y="130" width="260" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-28" value="- llms: List&amp;lt;Llm&amp;gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="NR3NLs1SwqtUjtMI9eED-20" vertex="1">
          <mxGeometry y="156" width="260" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-24" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="NR3NLs1SwqtUjtMI9eED-20" vertex="1">
          <mxGeometry y="182" width="260" height="8" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-25" value="+ build_prompt(base_agent, task): string" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="NR3NLs1SwqtUjtMI9eED-20" vertex="1">
          <mxGeometry y="190" width="260" height="26" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-47" value="+ executa(base_agent, prompt): result" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="NR3NLs1SwqtUjtMI9eED-20" vertex="1">
          <mxGeometry y="216" width="260" height="26" as="geometry" />
        </mxCell>
        <mxCell id="w4Hvd4hT7apZMpLXSnyB-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="NR3NLs1SwqtUjtMI9eED-40" target="w4Hvd4hT7apZMpLXSnyB-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1045" y="1550" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-40" value="SpecialistAgent" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="880" y="1280" width="170" height="86" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-47" value="- tools: List&amp;lt;BaseTool&amp;gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="NR3NLs1SwqtUjtMI9eED-40" vertex="1">
          <mxGeometry y="26" width="170" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-42" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="NR3NLs1SwqtUjtMI9eED-40" vertex="1">
          <mxGeometry y="52" width="170" height="8" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-43" value="+ method(type): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="NR3NLs1SwqtUjtMI9eED-40" vertex="1">
          <mxGeometry y="60" width="170" height="26" as="geometry" />
        </mxCell>
        <mxCell id="w4Hvd4hT7apZMpLXSnyB-1" value="Tool" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="1080" y="1440" width="180" height="268" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-2" value="- id: uuid" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="w4Hvd4hT7apZMpLXSnyB-1" vertex="1">
          <mxGeometry y="26" width="180" height="26" as="geometry" />
        </mxCell>
        <mxCell id="w4Hvd4hT7apZMpLXSnyB-2" value="- name: string" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="w4Hvd4hT7apZMpLXSnyB-1" vertex="1">
          <mxGeometry y="52" width="180" height="26" as="geometry" />
        </mxCell>
        <mxCell id="w4Hvd4hT7apZMpLXSnyB-5" value="- description: string" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="w4Hvd4hT7apZMpLXSnyB-1" vertex="1">
          <mxGeometry y="78" width="180" height="26" as="geometry" />
        </mxCell>
        <mxCell id="24T6QsIyFG16VPnvyuSI-5" value="- when_to_use: string" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="w4Hvd4hT7apZMpLXSnyB-1" vertex="1">
          <mxGeometry y="104" width="180" height="26" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-3" value="- parameters_schema: json" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="w4Hvd4hT7apZMpLXSnyB-1" vertex="1">
          <mxGeometry y="130" width="180" height="26" as="geometry" />
        </mxCell>
        <mxCell id="24T6QsIyFG16VPnvyuSI-3" value="- return_schema: json" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="w4Hvd4hT7apZMpLXSnyB-1" vertex="1">
          <mxGeometry y="156" width="180" height="26" as="geometry" />
        </mxCell>
        <mxCell id="w4Hvd4hT7apZMpLXSnyB-3" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="w4Hvd4hT7apZMpLXSnyB-1" vertex="1">
          <mxGeometry y="182" width="180" height="8" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-5" value="+ validate_input(input): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="w4Hvd4hT7apZMpLXSnyB-1" vertex="1">
          <mxGeometry y="190" width="180" height="26" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-6" value="+ call(input): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;fontStyle=1" parent="w4Hvd4hT7apZMpLXSnyB-1" vertex="1">
          <mxGeometry y="216" width="180" height="26" as="geometry" />
        </mxCell>
        <mxCell id="w4Hvd4hT7apZMpLXSnyB-4" value="+ format_output(result): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="w4Hvd4hT7apZMpLXSnyB-1" vertex="1">
          <mxGeometry y="242" width="180" height="26" as="geometry" />
        </mxCell>
        <mxCell id="2ZoE_nwwO2nTcwCi95nZ-1" value="Llm" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="1120" y="933" width="240" height="190" as="geometry" />
        </mxCell>
        <mxCell id="2ZoE_nwwO2nTcwCi95nZ-2" value="- provider: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="2ZoE_nwwO2nTcwCi95nZ-1" vertex="1">
          <mxGeometry y="26" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="2ZoE_nwwO2nTcwCi95nZ-6" value="- apiKey: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="2ZoE_nwwO2nTcwCi95nZ-1" vertex="1">
          <mxGeometry y="52" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="2ZoE_nwwO2nTcwCi95nZ-7" value="- modelName: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="2ZoE_nwwO2nTcwCi95nZ-1" vertex="1">
          <mxGeometry y="78" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="2ZoE_nwwO2nTcwCi95nZ-8" value="- tokenizerModelName: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="2ZoE_nwwO2nTcwCi95nZ-1" vertex="1">
          <mxGeometry y="104" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="2ZoE_nwwO2nTcwCi95nZ-9" value="- useTransformersTokenizer: boolean" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="2ZoE_nwwO2nTcwCi95nZ-1" vertex="1">
          <mxGeometry y="130" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="2ZoE_nwwO2nTcwCi95nZ-3" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="2ZoE_nwwO2nTcwCi95nZ-1" vertex="1">
          <mxGeometry y="156" width="240" height="8" as="geometry" />
        </mxCell>
        <mxCell id="2ZoE_nwwO2nTcwCi95nZ-4" value="+ method(type): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="2ZoE_nwwO2nTcwCi95nZ-1" vertex="1">
          <mxGeometry y="164" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-118" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="phcxIo_GTUzgT_Vqn9Rf-1" target="NR3NLs1SwqtUjtMI9eED-14" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-1" value="Business" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="120" y="1190" width="160" height="112" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-2" value="- id: uuid" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-1" vertex="1">
          <mxGeometry y="26" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-5" value="- name: string" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-1" vertex="1">
          <mxGeometry y="52" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-3" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="phcxIo_GTUzgT_Vqn9Rf-1" vertex="1">
          <mxGeometry y="78" width="160" height="8" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-4" value="+ method(type): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-1" vertex="1">
          <mxGeometry y="86" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-98" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="phcxIo_GTUzgT_Vqn9Rf-13" target="phcxIo_GTUzgT_Vqn9Rf-24" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="200" y="1900" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-100" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="phcxIo_GTUzgT_Vqn9Rf-98" vertex="1" connectable="0">
          <mxGeometry x="-0.7343" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-101" value="N" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="phcxIo_GTUzgT_Vqn9Rf-98" vertex="1" connectable="0">
          <mxGeometry x="0.6364" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zUz646fmQ5Xu5w4TcClL-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="phcxIo_GTUzgT_Vqn9Rf-13" target="phcxIo_GTUzgT_Vqn9Rf-78" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-13" value="LongSessionMemory" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="120" y="1660" width="160" height="164" as="geometry" />
        </mxCell>
        <mxCell id="zUz646fmQ5Xu5w4TcClL-1" value="- id: long" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-13" vertex="1">
          <mxGeometry y="26" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="zUz646fmQ5Xu5w4TcClL-2" value="- customer: Customer" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-13" vertex="1">
          <mxGeometry y="52" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-22" value="- started_at: LocalDateTime" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-13" vertex="1">
          <mxGeometry y="78" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-23" value="- ended_at: LocalDateTime" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-13" vertex="1">
          <mxGeometry y="104" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-15" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="phcxIo_GTUzgT_Vqn9Rf-13" vertex="1">
          <mxGeometry y="130" width="160" height="8" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-16" value="+ method(type): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-13" vertex="1">
          <mxGeometry y="138" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-97" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="phcxIo_GTUzgT_Vqn9Rf-24" target="phcxIo_GTUzgT_Vqn9Rf-75" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="380" y="2014" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-24" value="Turn" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="300" y="1820" width="160" height="138" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-27" value="- speaker: SpeakerTurn" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-24" vertex="1">
          <mxGeometry y="26" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-28" value="- content: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-24" vertex="1">
          <mxGeometry y="52" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-31" value="- dateTime: LocalDateTime" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-24" vertex="1">
          <mxGeometry y="78" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-29" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="phcxIo_GTUzgT_Vqn9Rf-24" vertex="1">
          <mxGeometry y="104" width="160" height="8" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-30" value="+ method(type): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-24" vertex="1">
          <mxGeometry y="112" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-43" value="{&#xa;  &quot;session_id&quot;: &quot;abc123&quot;,&#xa;  &quot;user_id&quot;: &quot;mauricio&quot;,&#xa;  &quot;turns&quot;: [&#xa;    {&quot;role&quot;: &quot;user&quot;, &quot;content&quot;: &quot;Olá agente!&quot;, &quot;timestamp&quot;: &quot;2025-07-25T15:20&quot;},&#xa;    {&quot;role&quot;: &quot;bot&quot;, &quot;content&quot;: &quot;Olá, Mauricio!&quot;, &quot;timestamp&quot;: &quot;2025-07-25T15:20&quot;}&#xa;  ],&#xa;  &quot;last_intent&quot;: &quot;cumprimento&quot;,&#xa;  &quot;context&quot;: &quot;início de conversa&quot;&#xa;}" style="text;whiteSpace=wrap;fontSize=9;fontFamily=Helvetica;" parent="1" vertex="1">
          <mxGeometry x="630" y="1600" width="310" height="130" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-111" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="phcxIo_GTUzgT_Vqn9Rf-44" target="G3mayhNQRmHL4hnrbGOk-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="500" y="1810" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-112" value="5" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="phcxIo_GTUzgT_Vqn9Rf-111" vertex="1" connectable="0">
          <mxGeometry x="0.7658" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zUz646fmQ5Xu5w4TcClL-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="phcxIo_GTUzgT_Vqn9Rf-44" target="phcxIo_GTUzgT_Vqn9Rf-78" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="490" y="1429" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-44" value="ShortTermMemory" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="410" y="1530" width="160" height="138" as="geometry" />
        </mxCell>
        <mxCell id="zUz646fmQ5Xu5w4TcClL-3" value="- id: long" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-44" vertex="1">
          <mxGeometry y="26" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="zUz646fmQ5Xu5w4TcClL-4" value="- customer: Customer" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-44" vertex="1">
          <mxGeometry y="52" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-54" value="- messages: List&amp;lt;Turn&amp;gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-44" vertex="1">
          <mxGeometry y="78" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-46" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="phcxIo_GTUzgT_Vqn9Rf-44" vertex="1">
          <mxGeometry y="104" width="160" height="8" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-47" value="+ method(type): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-44" vertex="1">
          <mxGeometry y="112" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-72" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;" parent="1" source="phcxIo_GTUzgT_Vqn9Rf-44" target="phcxIo_GTUzgT_Vqn9Rf-43" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1090" y="1360" as="sourcePoint" />
            <mxPoint x="1140" y="1310" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-75" value="&lt;p style=&quot;margin:0px;margin-top:4px;text-align:center;&quot;&gt;&lt;i&gt;&amp;lt;&amp;lt;enum&amp;gt;&amp;gt;&lt;/i&gt;&lt;br&gt;&lt;b&gt;SpeakerTurn&lt;/b&gt;&lt;/p&gt;&lt;hr size=&quot;1&quot; style=&quot;border-style:solid;&quot;&gt;&lt;p style=&quot;margin:0px;margin-left:4px;&quot;&gt;+ BOT&lt;br&gt;+ USER&lt;/p&gt;" style="verticalAlign=top;align=left;overflow=fill;html=1;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="510" y="1970" width="130" height="88" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-78" value="Customer" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="120" y="1390" width="160" height="78" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-79" value="- id: str" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-78" vertex="1">
          <mxGeometry y="26" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-82" value="- name: str" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="phcxIo_GTUzgT_Vqn9Rf-78" vertex="1">
          <mxGeometry y="52" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-88" value="" style="endArrow=classic;html=1;rounded=0;edgeStyle=orthogonalEdgeStyle;" parent="1" source="phcxIo_GTUzgT_Vqn9Rf-1" target="phcxIo_GTUzgT_Vqn9Rf-78" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="590" y="1510" as="sourcePoint" />
            <mxPoint x="640" y="1460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-119" value="Extends" style="endArrow=block;endSize=16;endFill=0;html=1;rounded=0;edgeStyle=elbowEdgeStyle;elbow=vertical;" parent="1" source="NR3NLs1SwqtUjtMI9eED-14" target="NR3NLs1SwqtUjtMI9eED-20" edge="1">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="790" y="1440" as="sourcePoint" />
            <mxPoint x="950" y="1440" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-120" value="Extends" style="endArrow=block;endSize=16;endFill=0;html=1;rounded=0;edgeStyle=elbowEdgeStyle;elbow=vertical;" parent="1" source="NR3NLs1SwqtUjtMI9eED-40" target="NR3NLs1SwqtUjtMI9eED-20" edge="1">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="790" y="1340" as="sourcePoint" />
            <mxPoint x="950" y="1340" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-121" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;" parent="1" source="phcxIo_GTUzgT_Vqn9Rf-44" target="NR3NLs1SwqtUjtMI9eED-14" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="464.4559585492227" y="1480" as="sourcePoint" />
            <mxPoint x="710" y="1610" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="phcxIo_GTUzgT_Vqn9Rf-123" value="Utiliza a Long e Short Session" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="phcxIo_GTUzgT_Vqn9Rf-121" vertex="1" connectable="0">
          <mxGeometry x="-0.0424" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="G3mayhNQRmHL4hnrbGOk-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="G3mayhNQRmHL4hnrbGOk-1" target="phcxIo_GTUzgT_Vqn9Rf-75" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="690" y="2010" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="G3mayhNQRmHL4hnrbGOk-1" value="ShortTurn" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="610" y="1750" width="160" height="138" as="geometry" />
        </mxCell>
        <mxCell id="G3mayhNQRmHL4hnrbGOk-2" value="- speaker: SpeakerTurn" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="G3mayhNQRmHL4hnrbGOk-1" vertex="1">
          <mxGeometry y="26" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="G3mayhNQRmHL4hnrbGOk-3" value="- content: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="G3mayhNQRmHL4hnrbGOk-1" vertex="1">
          <mxGeometry y="52" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="G3mayhNQRmHL4hnrbGOk-4" value="- dateTime: LocalDateTime" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="G3mayhNQRmHL4hnrbGOk-1" vertex="1">
          <mxGeometry y="78" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="G3mayhNQRmHL4hnrbGOk-5" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="G3mayhNQRmHL4hnrbGOk-1" vertex="1">
          <mxGeometry y="104" width="160" height="8" as="geometry" />
        </mxCell>
        <mxCell id="G3mayhNQRmHL4hnrbGOk-6" value="+ method(type): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="G3mayhNQRmHL4hnrbGOk-1" vertex="1">
          <mxGeometry y="112" width="160" height="26" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-7" value="{&#xa;  &quot;type&quot;: &quot;object&quot;,&#xa;  &quot;properties&quot;: {&#xa;    &quot;nome&quot;: {&#xa;      &quot;type&quot;: &quot;string&quot;,&#xa;      &quot;description&quot;: &quot;Nome do produto&quot;&#xa;    },&#xa;    &quot;preco_maximo&quot;: {&#xa;      &quot;type&quot;: &quot;number&quot;,&#xa;      &quot;description&quot;: &quot;Preço máximo aceitável&quot;&#xa;    }&#xa;  },&#xa;  &quot;required&quot;: [&quot;nome&quot;]&#xa;}" style="text;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="1360" y="1440" width="260" height="220" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-8" value="{&#xa;  &quot;type&quot;: &quot;array&quot;,&#xa;  &quot;items&quot;: {&#xa;    &quot;type&quot;: &quot;object&quot;,&#xa;    &quot;properties&quot;: {&#xa;      &quot;id&quot;:        { &quot;type&quot;: &quot;integer&quot; },&#xa;      &quot;nome&quot;:      { &quot;type&quot;: &quot;string&quot; },&#xa;      &quot;preco&quot;:     { &quot;type&quot;: &quot;number&quot; },&#xa;      &quot;categoria&quot;: { &quot;type&quot;: &quot;string&quot; },&#xa;      &quot;descricao&quot;: { &quot;type&quot;: &quot;string&quot; }&#xa;    },&#xa;    &quot;required&quot;: [&quot;id&quot;, &quot;nome&quot;, &quot;preco&quot;]&#xa;  }&#xa;}" style="text;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="1360" y="1680" width="220" height="220" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-23" value="Extends" style="endArrow=block;endSize=16;endFill=0;html=1;rounded=0;edgeStyle=elbowEdgeStyle;" parent="1" source="2f4zr50_fD8sO4PcWzEf-25" target="w4Hvd4hT7apZMpLXSnyB-1" edge="1">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="1050" y="1800" as="sourcePoint" />
            <mxPoint x="1310" y="1690" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-24" value="find_product_by_id (id)&lt;br&gt;&lt;br&gt;list_product_by_name_description (identificacao_nominal)&lt;br&gt;&lt;br&gt;list_product_by_category_marca_modelo (classificacao)&lt;br&gt;&lt;br&gt;filter_products_by_price (&lt;br&gt;&lt;span style=&quot;&quot;&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;&lt;/span&gt;&lt;/span&gt;identificacao_nominal ou classificacao, &lt;br&gt;&#x9;&lt;span style=&quot;&quot;&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;&lt;/span&gt;&lt;/span&gt;preco_minimo, preco_maximo)&lt;br&gt;&lt;br&gt;compare_products (id_1, id_2)&lt;br&gt;&lt;br&gt;list_best_sellers&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;check_product_stock" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;rounded=0;spacingTop=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="1160" y="1958" width="330" height="282" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-25" value="ConcreteExampleTool" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="1080" y="1820" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-32" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="2f4zr50_fD8sO4PcWzEf-25" vertex="1">
          <mxGeometry y="26" width="180" height="8" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-34" value="+ call(input): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;fontStyle=1" parent="2f4zr50_fD8sO4PcWzEf-25" vertex="1">
          <mxGeometry y="34" width="180" height="26" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-37" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;" parent="1" source="2f4zr50_fD8sO4PcWzEf-25" target="2f4zr50_fD8sO4PcWzEf-24" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1210" y="1710" as="sourcePoint" />
            <mxPoint x="1260" y="1660" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-39" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="2f4zr50_fD8sO4PcWzEf-3" target="2f4zr50_fD8sO4PcWzEf-7" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1150" y="1877" as="sourcePoint" />
            <mxPoint x="1200" y="1893" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-40" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="24T6QsIyFG16VPnvyuSI-3" target="2f4zr50_fD8sO4PcWzEf-8" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1150" y="1573" as="sourcePoint" />
            <mxPoint x="1290" y="1504" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-46" style="edgeStyle=elbowEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;elbow=vertical;" parent="1" source="2f4zr50_fD8sO4PcWzEf-41" target="w4Hvd4hT7apZMpLXSnyB-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1160" y="1530" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-41" value="WriterAgent" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="1200" y="1280" width="170" height="86" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-42" value="- tools: List&amp;lt;BaseTool&amp;gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="2f4zr50_fD8sO4PcWzEf-41" vertex="1">
          <mxGeometry y="26" width="170" height="26" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-43" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" parent="2f4zr50_fD8sO4PcWzEf-41" vertex="1">
          <mxGeometry y="52" width="170" height="8" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-44" value="+ method(type): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" parent="2f4zr50_fD8sO4PcWzEf-41" vertex="1">
          <mxGeometry y="60" width="170" height="26" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-45" value="Extends" style="endArrow=block;endSize=16;endFill=0;html=1;rounded=0;edgeStyle=elbowEdgeStyle;elbow=vertical;" parent="1" source="2f4zr50_fD8sO4PcWzEf-41" target="NR3NLs1SwqtUjtMI9eED-20" edge="1">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="1060" y="1820" as="sourcePoint" />
            <mxPoint x="1060" y="1698" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-49" value="Task:&#xa;{&#xa;    &quot;pergunta&quot;: &quot;string&quot;,&#xa;    &quot;intencao_global&quot;: &quot;string&quot;,&#xa;    &quot;agentes_execucao&quot;: [&#xa;        {   &#xa;            &quot;orderm&quot;: int,&#xa;            &quot;nome&quot;: &quot;string&quot;,&#xa;            &quot;intencao_agente&quot;: &quot;string&quot;, &#xa;            &quot;motivo&quot;: &quot;string&quot;,&#xa;            &quot;ferramentas&quot;: [&#xa;                {&#xa;                    &quot;nome&quot;: &quot;string&quot;,&#xa;                    &quot;parametros&quot;: [ { ... } ],&#xa;                    &quot;motivo&quot;: &quot;string&quot;&#xa;                }, &#xa;                { ... }&#xa;            ],&#xa;        },&#xa;        { ... }&#xa;    ]&#xa;}" style="text;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="320" y="880" width="210" height="330" as="geometry" />
        </mxCell>
        <mxCell id="2f4zr50_fD8sO4PcWzEf-50" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="2f4zr50_fD8sO4PcWzEf-49" target="NR3NLs1SwqtUjtMI9eED-25" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="1180" as="sourcePoint" />
            <mxPoint x="850" y="1130" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="myYJnoRE6FnZDYsIK6Cf" name="Memory">
    <mxGraphModel dx="957" dy="478" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="oxcXaEZojkD2pNS2FyUf-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oxcXaEZojkD2pNS2FyUf-1" target="oxcXaEZojkD2pNS2FyUf-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-1" value="Manager Agent" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="400" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oxcXaEZojkD2pNS2FyUf-2" target="oxcXaEZojkD2pNS2FyUf-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-2" value="Mauricio" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="120" y="80" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oxcXaEZojkD2pNS2FyUf-4" target="oxcXaEZojkD2pNS2FyUf-5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-4" value="Specialist Agent" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="760" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-5" value="Actor" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="1170" y="80" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-9" value="Pergunta: Tem iphone?" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="120" y="200" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-18" value="&lt;b&gt;Short Term Memory:&lt;/b&gt;&lt;br&gt;&lt;br&gt;customer: Mauricio&lt;br&gt;intent: Buscar iphones&lt;br&gt;turns:&lt;br&gt;&amp;nbsp;speaker: user, content: tem iphone?&lt;br&gt;tipo_sessao: inicio conversa" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="400" y="200" width="200" height="140" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-19" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1320" y="360" as="sourcePoint" />
            <mxPoint x="120" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-20" value="Short Term Memory:&lt;br&gt;&lt;br&gt;customer: Mauricio&lt;br&gt;intent: Buscar iphones&lt;br&gt;turns:&lt;br&gt;&amp;nbsp;role: user, content: tem iphone?&lt;br&gt;&amp;nbsp;role: bot, content: tem varios iphones&lt;br&gt;tipo_sessao: inicio conversa" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="760" y="200" width="200" height="160" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-21" value="Resposta: Tem vários iphones" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1160" y="200" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-22" value="Pergunta: Quero saber sobre o iphone 11" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="120" y="400" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-23" value="Short Term Memory:&lt;br&gt;&lt;br&gt;customer: Mauricio&lt;br&gt;intent: Detalhes sobre o iphone 11&lt;br&gt;turns:&lt;br&gt;&amp;nbsp;speaker: user, content: tem iphone?&lt;br&gt;&amp;nbsp;speaker: agent, content: Tem vários iphones.&amp;nbsp;&lt;br&gt;tipo_sessao: continuacao" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="400" y="400" width="200" height="140" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-24" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1320" y="560" as="sourcePoint" />
            <mxPoint x="120" y="560" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-26" value="Resposta: Tem vários iphones" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1160" y="400" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-27" value="" style="shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;flipH=1;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;" parent="1" vertex="1">
          <mxGeometry x="80" y="80" width="20" height="280" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-28" value="Turno 1" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="10" y="200" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-29" value="" style="shape=curlyBracket;whiteSpace=wrap;html=1;rounded=1;flipH=1;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;" parent="1" vertex="1">
          <mxGeometry x="80" y="360" width="20" height="280" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-30" value="Turno 2" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="10" y="480" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="oxcXaEZojkD2pNS2FyUf-31" value="Short Term Memory:&lt;br&gt;&lt;br&gt;customer: Mauricio&lt;br&gt;intent: Detalhes sobre o iphone 11&lt;br&gt;turns:&lt;br&gt;&amp;nbsp;speaker: user, content: tem iphone?&lt;br&gt;&amp;nbsp;speaker: agent, content: Tem vários iphones.&amp;nbsp;&lt;br&gt;tipo_sessao: continuacao" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="760" y="400" width="200" height="140" as="geometry" />
        </mxCell>
        <mxCell id="jn5Ig2ROnFr6K_rJNNan-1" value="Caso de uso: Salvar STM&#xa;&#xa;&#xa;    1. O usuário inicia a conversa com o Sistema enviando uma mensagem;&#xa;    2. O Sistema consulta a Sessão da conversa do usuário na Memória de curto prazo (STM):&#xa;        1. Senão existir Sessão para o usuário, então é criada uma Sessão;&#xa;    3. O Sistema processa a mensagem e gera uma mensagem de resposta;&#xa;    4. O Sistema adiciona a mensagem do usuário e a resposta do sistema na Sessão;&#xa;    5. O Sistema verifica se a mensagem do usuário é fim da conversa:&#xa;        1. Então, o Sistema resume a Sessão da STM e armazena na Memória de longo prazo (LTM);&#xa;        2. Senão, o Sistema atualiza a Sessão na STM;&#xa;    6. O Sistema envia ao usuário a resposta.&#xa;&#xa;&#xa;&#xa;&#xa;ShortTermMemory:&#xa;{&#xa;    &quot;id&quot;: &quot;uuid&quot;,&#xa;    &quot;customer&quot;: &quot;string&quot;,&#xa;    &quot;historico_mensagens&quot;: [&#xa;        { &#xa;            &quot;speaker&quot;: &quot;user | agent&quot;,&#xa;            &quot;content&quot;: &quot;string&quot;&#xa;            &quot;date_time&quot;: &quot;2025-07-28 16:52&quot;&#xa;        }&#xa;    ]&#xa;}" style="text;fontFamily=Helvetica;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="180" y="630" width="560" height="420" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
