# Product Specialist Agent v2 – Refined Implementation Plan

## 1. Context & Integration
- **Inheritance**: The new agent will inherit from `AgentBase` (`SimplerAgenticChat/src/agents/agent_base.py`), ensuring compatibility with the orchestrator and memory management.
- **LLM Access**: Use the `LLMFactory` (`LangChainAgent/core/utils/llm_factory.py`) to instantiate the LLM, with configuration and environment variable support.
- **Configuration**: Use `ConfigLoader` (`LangChainAgent/core/utils/config_loader.py`) to load agent, LLM, and product field configs.
- **Orchestrator Integration**: The agent will expose the same interface as the current ProductSpecialistAgent, so it can be called by the orchestrator without changes.
- **Dual Backend**: Support both PostgreSQL (read-only) and a development mode using `LangChainAgent/data/products.json`.

## 2. Requirements Recap
- Configurable product fields and LLM model (from `.config`).
- Data source: Support both PostgreSQL (read-only) and a development mode using `LangChainAgent/data/products.json`.
- Agentic tools: FTS, structured queries, synonym expansion, intent/entity extraction, product comparison, recommendations.
- No write operations.
- Logging: Toggleable via config.
- Context-aware output: Basic for initial, detailed for follow-up.
- DB credentials via env vars.
- Backend deployment.

## 3. Tool Design

### 3.1. Intent & Entity Extraction Tool
- **Purpose**: Receives a user query (e.g., "Tem notebook da Dell com 16GB de RAM até 4000 reais?") and returns:
  ```json
  {
    "intencao": "buscar_por_categoria",
    "entidades": {
      "categoria": "notebook",
      "marca": "dell",
      "ram": "16GB",
      "preco_max": 4000
    }
  }
  ```
- **Implementation**:
  - Use the LLM to map the query to a predefined set of intents and extract entities matching DB fields.
  - The list of intents and entity types is defined in the config.
  - The tool should be stateless and return only what is present in the query.
- **Example code snippet**:
  ```python
  class IntentEntityExtractor:
      def __init__(self, llm, intents, entity_schema):
          self.llm = llm
          self.intents = intents
          self.entity_schema = entity_schema

      def extract(self, query: str) -> dict:
          prompt = f"""
          Dada a seguinte consulta: '{query}'
          Identifique a intenção (uma das: {self.intents}) e extraia as entidades ({self.entity_schema}).
          Responda em JSON.
          """
          response = self.llm.generate(prompt)
          return json.loads(response)
  ```

### 3.2. Structured Query Tool
- **Purpose**: Receives the extracted intent/entities and prepares a SQL query or JSON filter.
- **Implementation**:
  - For SQL: Generate a `SELECT ... WHERE ...` query using the extracted entities.
  - For JSON: Filter the product list using the same logic.
  - The mapping from entity names to DB/JSON fields is defined in the config.
- **Example code snippet**:
  ```python
  class ProductQueryBuilder:
      def __init__(self, field_map):
          self.field_map = field_map

      def build_sql(self, entities: dict) -> str:
          # Build WHERE clause from entities
          # Example: category ILIKE '%notebook%' AND ...
          ...
  ```

### 3.3. FTS Tool
- **Purpose**: Full-text search on product name/description/tags.
- **Implementation**:
  - For SQL: Use PostgreSQL `tsquery`.
  - For JSON: Use Python fuzzy matching.

### 3.4. Synonym Expansion Tool
- **Purpose**: Use LLM to expand query terms with synonyms for better recall.

### 3.5. Product Comparison & Recommendation Tools
- **Purpose**: Given product IDs or context, fetch and format comparisons or recommendations.
- **Implementation**: Read-only, works with either SQL or JSON backend.

## 4. Agent Construction
- **Initialization**:
  - Load config using `ConfigLoader`.
  - Instantiate LLM using `LLMFactory`.
  - Load product fields, intent/entity schema, and tool settings from config.
  - Set mode: `"json"` (dev) or `"sql"` (prod), switch data source accordingly.
- **Execution**:
  - Accept orchestrator input (query, context, customer_id, etc.).
  - Use Intent & Entity Extraction Tool to parse the query.
  - Route to Structured Query Tool and/or FTS Tool as needed.
  - Fetch products from the appropriate backend (JSON or SQL).
  - Format output as required (list for initial, details for follow-up).
  - Return: `{products, query, customer_id, status}`.
- **Inheritance**:
  - Inherit from `AgentBase`.
  - Implement `execute` or `handle_message` as required by orchestrator.
- **Example Agent Skeleton**:
  ```python
  class ProductSpecialistAgent(AgentBase):
      def __init__(self, config, llm_factory, mode="json"):
          super().__init__(...)
          self.config = config
          self.llm = llm_factory.get_llm_with_fallback()
          self.mode = mode
          self.intent_extractor = IntentEntityExtractor(self.llm, config["intents"], config["entity_schema"])
          self.query_builder = ProductQueryBuilder(config["field_map"])
          # ... other tools

      def execute(self, input_data):
          query = input_data["query"]
          intent_entities = self.intent_extractor.extract(query)
          if self.mode == "json":
              # filter products.json
              ...
          else:
              # build and run SQL
              ...
          # format and return result
  ```

## 5. Logging
- Log queries, responses, and tool usage if enabled in config.

## 6. Output Formatting
- **Initial queries**: List of products with basic info and match score.
- **Follow-ups**: Detailed info for referenced products.
- **Never invent data**: Only return what is in the DB/JSON.

## 7. Dual Backend Support
- **Development mode**: Use `LangChainAgent/data/products.json` for all queries.
- **Production mode**: Use PostgreSQL, credentials from env vars.
- **Switching**: Controlled by config flag.

## 8. Mock Logic & Example: LangChain Tools for Product Search

To guide the development of the planned tools, here is a mock implementation using LangChain's Tool API. This demonstrates how to define, register, and use tools for product search and detail retrieval in a multi-turn conversation.

### Example Tool Functions

```python
def basic_product_search(query: str) -> str:
    # Preprocess with LLM into tsquery
    tsquery = preprocess_query(query)
    sql = """
    SELECT id, name, short_description
    FROM products
    WHERE product_tsv @@ to_tsquery('english', %s)
    LIMIT 5;
    """
    with psycopg2.connect(...) as conn:
        with conn.cursor() as cur:
            cur.execute(sql, (tsquery,))
            rows = cur.fetchall()
    if not rows:
        return "No matching products found."
    return "\n".join([f"{id}. {name}: {desc}" for id, name, desc in rows])

def detailed_product_info(identifier: str) -> str:
    # identifier can be name or ID
    sql = """
    SELECT name, price, features, specs, availability
    FROM products
    WHERE id::text = %s OR LOWER(name) = LOWER(%s)
    LIMIT 1;
    """
    with psycopg2.connect(...) as conn:
        with conn.cursor() as cur:
            cur.execute(sql, (identifier, identifier))
            row = cur.fetchone()
    if not row:
        return "Product not found."
    name, price, features, specs, availability = row
    return f"{name} - ${price}\nFeatures: {features}\nSpecs: {specs}\nAvailable: {availability}"
```

### Registering as LangChain Tools

```python
from langchain.agents import Tool

basic_tool = Tool.from_function(
    name="ProductSearchTool",
    func=basic_product_search,
    description=(
        "Use this tool to find products matching a query. "
        "Only returns basic info: id, name, short description."
    )
)

detail_tool = Tool.from_function(
    name="ProductDetailTool",
    func=detailed_product_info,
    description=(
        "Use this tool to get full info about a specific product. "
        "Input should be a product ID or exact name from previous results."
    )
)
```

### Passing Tools to the Agent

```python
agent = initialize_agent(
    tools=[basic_tool, detail_tool],
    llm=llm,
    agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
    verbose=True,
)
```

### Multi-Turn Conversation Example

- **Turn 1:**
  - User: “Do you have electric scooters under $800?”
  - Agent chooses ProductSearchTool
  - Returns: basic list of scooters
- **Turn 2:**
  - User: “Tell me more about the Mi Electric Pro 2.”
  - Agent chooses ProductDetailTool
  - Returns: full specs and price

> **Note:** 
This is a development guide/example for the planned tools. The real implementation will adapt these patterns for both JSON and SQL backends, and integrate with the intent/entity extraction and query builder logic described above.

## 9. Recommended LangChain Agent Skeleton & Best Practices

To ensure clarity and alignment with LangChain's agentic tooling, follow these guidelines for implementing the Product Specialist Agent:

- **Each capability (search, detail, etc.) should be a LangChain Tool**—a stateless function or callable, registered with a clear description.
- **The agent is constructed via `initialize_agent`**, not by subclassing a custom class for the main loop.
- **The LLM is passed to the agent**, not to each tool individually (unless the tool itself is LLM-powered, e.g., for intent/entity extraction).
- **The orchestrator interacts with the agent via `agent.run(user_query)`** (or similar), letting LangChain handle tool selection and invocation.
- **Extensibility**: Add more tools (e.g., recommendations, comparisons) by defining new functions and registering them as `Tool` objects.

### Example: Idiomatic LangChain Agent Construction

```python
from langchain.agents import Tool, initialize_agent, AgentType

def basic_product_search(query: str) -> str:
    # ... (see previous mock)
    pass

def detailed_product_info(identifier: str) -> str:
    # ... (see previous mock)
    pass

basic_tool = Tool.from_function(
    name="ProductSearchTool",
    func=basic_product_search,
    description="Use this tool to find products matching a query. Only returns basic info: id, name, short description."
)

detail_tool = Tool.from_function(
    name="ProductDetailTool",
    func=detailed_product_info,
    description="Use this tool to get full info about a specific product. Input should be a product ID or exact name from previous results."
)

agent = initialize_agent(
    tools=[basic_tool, detail_tool],
    llm=llm,  # Provided by your LLMFactory/config
    agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
    verbose=True,
)

# Example usage in orchestrator loop:
def handle_user_query(user_query: str):
    return agent.run(user_query)
```

#### LLM-Powered Tools
If you need LLM-powered tools (e.g., for intent/entity extraction), define them as functions and register as Tools as well:

```python
def extract_intent_entities(query: str) -> dict:
    # Use LLM to extract intent/entities (including categoria, which is constrained by config)
    return my_intent_extractor.llm_extract(query)

intent_tool = Tool.from_function(
    name="IntentEntityExtractor",
    func=extract_intent_entities,
    description="Extracts intent and entities (including categoria) from a user query. Categoria is constrained by config."
)
```

> **Note:** Do not subclass a custom agent for the main loop if you want to use LangChain’s agentic features. Let LangChain handle tool selection and invocation.


## 10. Best Practices for Maintainability & Upgradability

To ensure the Product Specialist Agent remains maintainable and easy to upgrade as requirements evolve, follow these best practices throughout the implementation:

- **Modular Design**: Implement each tool, utility, and data access layer as a separate, reusable module. Avoid monolithic functions or classes.
- **Configuration-Driven**: Use configuration files (YAML/JSON) for all environment-specific settings, product fields, LLM models, and tool parameters. Avoid hardcoding values.
- **Clear Interfaces**: Define clear, minimal interfaces for each tool and for orchestrator/agent interaction. Use type hints and docstrings.
- **Testing**: Write unit tests for each tool, utility, and integration point. Use mock data (e.g., products.json) for development and CI.
- **Documentation**: Document each tool, agent, and configuration option. Keep the plan and code comments up to date.
- **Extensibility**: Design tools and agent logic so new tools (e.g., for recommendations, analytics) can be added with minimal changes to existing code.
- **Separation of Concerns**: Keep business logic, data access, and LLM/tool orchestration separate. For example, SQL/JSON access should be isolated from tool logic.
- **Versioning**: Use semantic versioning for the agent and tools. Track changes in a changelog or version file.
- **Upgrade Path**: When adding new features, maintain backward compatibility where possible. Deprecate old interfaces gradually.
- **Error Handling & Logging**: Implement robust error handling and configurable logging for all tools and agent actions.

> **Integrate these practices into every stage of the plan—from tool design, agent construction, and configuration, to orchestrator integration and deployment.**

---

# Plano detalhado em português

## 1. Contexto e Integração
- **Herança**: O novo agente herda de `AgentBase` (`SimplerAgenticChat/src/agents/agent_base.py`), garantindo compatibilidade com o orquestrador e gerenciamento de memória.
- **Acesso ao LLM**: Usa o `LLMFactory` (`LangChainAgent/core/utils/llm_factory.py`) para instanciar o LLM, com suporte a configuração e variáveis de ambiente.
- **Configuração**: Usa `ConfigLoader` (`LangChainAgent/core/utils/config_loader.py`) para carregar configs do agente, LLM e campos de produto.
- **Integração com Orquestrador**: O agente expõe a mesma interface do ProductSpecialistAgent atual, podendo ser chamado pelo orquestrador sem mudanças.
- **Backend duplo**: Suporte tanto a PostgreSQL (apenas leitura) quanto modo de desenvolvimento usando `LangChainAgent/data/products.json`.

## 2. Requisitos
- Campos de produto e modelo LLM configuráveis (via `.config`).
- Fonte de dados: Suporte a PostgreSQL (apenas leitura) e modo dev com `LangChainAgent/data/products.json`.
- Ferramentas agentic: FTS, queries estruturadas, expansão de sinônimos, extração de intenção/entidades, comparação e recomendação de produtos.
- Sem operações de escrita.
- Log ativável via config.
- Saída sensível ao contexto: básica para consulta inicial, detalhada para follow-up.
- Credenciais do BD via env vars.
- Implantação como backend.

## 3. Design das Ferramentas

### 3.1. Ferramenta de Extração de Intenção/Entidades
- **Propósito**: Recebe uma consulta do usuário (ex: "Tem notebook da Dell com 16GB de RAM até 4000 reais?") e retorna:
  ```json
  {
    "intencao": "buscar_por_categoria",
    "entidades": {
      "categoria": "notebook",
      "marca": "dell",
      "ram": "16GB",
      "preco_max": 4000
    }
  }
  ```
- **Implementação**:
  - Usa o LLM para mapear a consulta para um conjunto pré-definido de intenções e extrair entidades que correspondam aos campos do BD.
  - Lista de intenções e tipos de entidades definida na config.
  - Ferramenta deve ser stateless e retornar apenas o que está presente na consulta.
- **Exemplo de código**:
  ```python
  class IntentEntityExtractor:
      def __init__(self, llm, intents, entity_schema):
          self.llm = llm
          self.intents = intents
          self.entity_schema = entity_schema

      def extract(self, query: str) -> dict:
          prompt = f"""
          Dada a seguinte consulta: '{query}'
          Identifique a intenção (uma das: {self.intents}) e extraia as entidades ({self.entity_schema}).
          Responda em JSON.
          """
          response = self.llm.generate(prompt)
          return json.loads(response)
  ```

### 3.2. Ferramenta de Query Estruturada
- **Propósito**: Recebe intenção/entidades extraídas e prepara query SQL ou filtro JSON.
- **Implementação**:
  - Para SQL: Gera um `SELECT ... WHERE ...` usando as entidades extraídas.
  - Para JSON: Filtra a lista de produtos com a mesma lógica.
  - Mapeamento de nomes de entidades para campos do BD/JSON definido na config.
- **Exemplo de código**:
  ```python
  class ProductQueryBuilder:
      def __init__(self, field_map):
          self.field_map = field_map

      def build_sql(self, entities: dict) -> str:
          # Monta WHERE a partir das entidades
          # Exemplo: category ILIKE '%notebook%' AND ...
          ...
  ```

### 3.3. Ferramenta FTS
- **Propósito**: Busca full-text em nome/descrição/tags do produto.
- **Implementação**:
  - Para SQL: Usa `tsquery` do PostgreSQL.
  - Para JSON: Fuzzy matching em Python.

### 3.4. Ferramenta de Expansão de Sinônimos
- **Propósito**: Usa LLM para expandir termos da consulta com sinônimos.

### 3.5. Ferramentas de Comparação e Recomendação
- **Propósito**: Dado IDs ou contexto, busca e formata comparações ou recomendações.
- **Implementação**: Apenas leitura, funciona tanto com SQL quanto JSON.

## 4. Construção do Agente
- **Inicialização**:
  - Carrega config via `ConfigLoader`.
  - Instancia LLM via `LLMFactory`.
  - Carrega campos de produto, schema de intenção/entidades e settings das ferramentas da config.
  - Define modo: `"json"` (dev) ou `"sql"` (prod), alternando fonte de dados.
- **Execução**:
  - Recebe input do orquestrador (query, contexto, customer_id, etc).
  - Usa ferramenta de extração de intenção/entidades.
  - Roteia para Query Estruturada e/ou FTS conforme necessário.
  - Busca produtos no backend apropriado (JSON ou SQL).
  - Formata saída conforme necessário (lista para inicial, detalhes para follow-up).
  - Retorna: `{products, query, customer_id, status}`.
- **Herança**:
  - Herda de `AgentBase`.
  - Implementa `execute` ou `handle_message` conforme interface do orquestrador.
- **Exemplo de esqueleto do agente**:
  ```python
  class ProductSpecialistAgent(AgentBase):
      def __init__(self, config, llm_factory, mode="json"):
          super().__init__(...)
          self.config = config
          self.llm = llm_factory.get_llm_with_fallback()
          self.mode = mode
          self.intent_extractor = IntentEntityExtractor(self.llm, config["intents"], config["entity_schema"])
          self.query_builder = ProductQueryBuilder(config["field_map"])
          # ... outras ferramentas

      def execute(self, input_data):
          query = input_data["query"]
          intent_entities = self.intent_extractor.extract(query)
          if self.mode == "json":
              # filtra products.json
              ...
          else:
              # monta e executa SQL
              ...
          # formata e retorna resultado
  ```

## 5. Logging
- Log de queries, respostas e uso das ferramentas se ativado na config.

## 6. Formatação da Saída
- **Consultas iniciais**: Lista de produtos com info básica e score de correspondência.
- **Follow-ups**: Info detalhada dos produtos referenciados.
- **Nunca invente dados**: Apenas retorne o que está no BD/JSON.

## 7. Suporte a Backend Duplo
- **Modo desenvolvimento**: Usa `LangChainAgent/data/products.json` para todas as queries.
- **Modo produção**: Usa PostgreSQL, credenciais via env vars.
- **Alternância**: Controlada por flag na config.


---

## 8. Lógica Mock & Exemplo: Ferramentas LangChain para Busca de Produtos

Para guiar o desenvolvimento das ferramentas planejadas, segue uma implementação mock usando a API Tool do LangChain. Demonstra como definir, registrar e usar ferramentas para busca e detalhamento de produtos em conversas multi-turno.

### Funções de Ferramenta (Exemplo)

```python
def basic_product_search(query: str) -> str:
    # Pré-processa com LLM para tsquery
    tsquery = preprocess_query(query)
    sql = """
    SELECT id, name, short_description
    FROM products
    WHERE product_tsv @@ to_tsquery('portuguese', %s)
    LIMIT 5;
    """
    with psycopg2.connect(...) as conn:
        with conn.cursor() as cur:
            cur.execute(sql, (tsquery,))
            rows = cur.fetchall()
    if not rows:
        return "Nenhum produto encontrado."
    return "\n".join([f"{id}. {name}: {desc}" for id, name, desc in rows])

def detailed_product_info(identifier: str) -> str:
    # identificador pode ser nome ou ID
    sql = """
    SELECT name, price, features, specs, availability
    FROM products
    WHERE id::text = %s OR LOWER(name) = LOWER(%s)
    LIMIT 1;
    """
    with psycopg2.connect(...) as conn:
        with conn.cursor() as cur:
            cur.execute(sql, (identifier, identifier))
            row = cur.fetchone()
    if not row:
        return "Produto não encontrado."
    name, price, features, specs, availability = row
    return f"{name} - R${price}\nCaracterísticas: {features}\nEspecificações: {specs}\nDisponível: {availability}"
```

### Registrando como Ferramentas LangChain

```python
from langchain.agents import Tool

basic_tool = Tool.from_function(
    name="ProductSearchTool",
    func=basic_product_search,
    description=(
        "Use esta ferramenta para buscar produtos por consulta. "
        "Retorna apenas info básica: id, nome, descrição curta."
    )
)

detail_tool = Tool.from_function(
    name="ProductDetailTool",
    func=detailed_product_info,
    description=(
        "Use esta ferramenta para obter info completa de um produto. "
        "Entrada deve ser ID ou nome exato de resultados anteriores."
    )
)
```

### Passando as Ferramentas para o Agente

```python
agent = initialize_agent(
    tools=[basic_tool, detail_tool],
    llm=llm,
    agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
    verbose=True,
)
```

### Exemplo de Conversa Multi-Turno

- **Turno 1:**
  - Usuário: “Tem patinete elétrico até R$800?”
  - Agente escolhe ProductSearchTool
  - Retorna: lista básica de patinetes
- **Turno 2:**
  - Usuário: “Me fale mais sobre o Mi Electric Pro 2.”
  - Agente escolhe ProductDetailTool
  - Retorna: especificações completas e preço

> **Nota:** Este é um guia de desenvolvimento/exemplo para as ferramentas planejadas. A implementação real irá adaptar esses padrões para ambos os backends (JSON e SQL) e integrar com a lógica de extração de intenção/entidades e montagem de queries descrita acima.

---

## 9. Esqueleto Recomendado de Agente LangChain & Boas Práticas

Para garantir clareza e alinhamento com o tooling agentic do LangChain, siga estas diretrizes para implementar o Product Specialist Agent:

- **Cada capacidade (busca, detalhe, etc.) deve ser uma Tool do LangChain**—função ou callable stateless, registrada com descrição clara.
- **O agente é construído via `initialize_agent`**, não por subclasse customizada para o loop principal.
- **O LLM é passado para o agente**, não para cada ferramenta individualmente (exceto se a ferramenta for LLM-powered, ex: extração de intenção/entidades).
- **O orquestrador interage com o agente via `agent.run(user_query)`** (ou similar), deixando o LangChain decidir e invocar a ferramenta.
- **Extensibilidade**: Adicione mais ferramentas (ex: recomendações, comparações) definindo novas funções e registrando como `Tool`.

### Exemplo: Construção Idiomática de Agente LangChain

```python
from langchain.agents import Tool, initialize_agent, AgentType

def basic_product_search(query: str) -> str:
    # ... (ver mock anterior)
    pass

def detailed_product_info(identifier: str) -> str:
    # ... (ver mock anterior)
    pass

basic_tool = Tool.from_function(
    name="ProductSearchTool",
    func=basic_product_search,
    description="Use esta ferramenta para buscar produtos por consulta. Retorna info básica: id, nome, descrição curta."
)

detail_tool = Tool.from_function(
    name="ProductDetailTool",
    func=detailed_product_info,
    description="Use esta ferramenta para obter info completa de um produto. Entrada deve ser ID ou nome exato de resultados anteriores."
)

agent = initialize_agent(
    tools=[basic_tool, detail_tool],
    llm=llm,  # Fornecido pelo seu LLMFactory/config
    agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
    verbose=True,
)

# Exemplo de uso no loop do orquestrador:
def handle_user_query(user_query: str):
    return agent.run(user_query)
```

#### Ferramentas LLM-powered
Se precisar de ferramentas LLM-powered (ex: extração de intenção/entidades), defina como função e registre como Tool também:

```python
def extract_intent_entities(query: str) -> dict:
    # Usa LLM para extrair intenção/entidades
    return my_intent_extractor.llm_extract(query)

intent_tool = Tool.from_function(
    name="IntentEntityExtractor",
    func=extract_intent_entities,
    description="Extrai intenção e entidades de uma consulta do usuário."
)
```

> **Nota:** Não subclasse um agente customizado para o loop principal se quiser usar as features agentic do LangChain. Deixe o LangChain decidir e invocar a ferramenta.
