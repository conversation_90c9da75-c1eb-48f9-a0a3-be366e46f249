<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:color="http://www.omg.org/spec/BPMN/non-normative/color/1.0" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_14v4kaz" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.37.0" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.7.0">
  <bpmn:collaboration id="Collaboration_1vsei8s">
    <bpmn:participant id="Participant_11g4bwe" name="Interação Chatbot" processRef="Process_0m6pl02" />
    <bpmn:textAnnotation id="TextAnnotation_08s5d3j">
      <bpmn:text>Busca o contexto na STM e ou LTM e adiciona ao prompt</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0qojvbv" associationDirection="None" sourceRef="Activity_1j4784z" targetRef="TextAnnotation_08s5d3j" />
  </bpmn:collaboration>
  <bpmn:process id="Process_0m6pl02" isExecutable="true">
    <bpmn:laneSet id="LaneSet_0u65uuk">
      <bpmn:lane id="Lane_077qvn7" name="Tool">
        <bpmn:flowNodeRef>Activity_0v27ib2</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_1ilbuqx" name="Specialist Agent">
        <bpmn:flowNodeRef>Activity_1q5reip</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1s55hxt</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1tsvezf</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_16i3hrm</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_16r1y5f" name="Llm">
        <bpmn:flowNodeRef>Activity_1uvmq29</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_0xhgkoq</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_13v3xoc" name="Query Agent">
        <bpmn:flowNodeRef>Activity_1ms98aj</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1j4784z</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1av73xo</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1bnakrz</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Gateway_08e1zat</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_1f3l0e7" name="Orquestrador Agent">
        <bpmn:extensionElements />
        <bpmn:flowNodeRef>Activity_004lorm</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>StartEvent_1</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_02cfbx6</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_1kccfq6</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_0lrb6tn</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_0yzzpce" name="Response Agent">
        <bpmn:flowNodeRef>Activity_1h65y77</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:task id="Activity_004lorm" name="Receber pergunta">
      <bpmn:incoming>Flow_1eclttk</bpmn:incoming>
      <bpmn:outgoing>Flow_1gk7c2r</bpmn:outgoing>
    </bpmn:task>
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_1eclttk</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:task id="Activity_1ms98aj" name="Analisar Consulta">
      <bpmn:incoming>Flow_1gk7c2r</bpmn:incoming>
      <bpmn:outgoing>Flow_0r6xjq6</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_1j4784z" name="Controir prompt">
      <bpmn:incoming>Flow_0r6xjq6</bpmn:incoming>
      <bpmn:outgoing>Flow_1rbuiui</bpmn:outgoing>
      <bpmn:dataOutputAssociation id="DataOutputAssociation_1qdqery">
        <bpmn:targetRef>DataStoreReference_1f6w22w</bpmn:targetRef>
      </bpmn:dataOutputAssociation>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_1eclttk" sourceRef="StartEvent_1" targetRef="Activity_004lorm" />
    <bpmn:sequenceFlow id="Flow_1gk7c2r" sourceRef="Activity_004lorm" targetRef="Activity_1ms98aj" />
    <bpmn:sequenceFlow id="Flow_0r6xjq6" sourceRef="Activity_1ms98aj" targetRef="Activity_1j4784z" />
    <bpmn:sequenceFlow id="Flow_1rbuiui" sourceRef="Activity_1j4784z" targetRef="Activity_1uvmq29" />
    <bpmn:sequenceFlow id="Flow_0rxexf5" sourceRef="Activity_1bnakrz" targetRef="Activity_0lrb6tn" />
    <bpmn:sequenceFlow id="Flow_09721af" sourceRef="Activity_1tsvezf" targetRef="Activity_16i3hrm" />
    <bpmn:sequenceFlow id="Flow_0rtthz9" sourceRef="Activity_16i3hrm" targetRef="Activity_0xhgkoq" />
    <bpmn:sequenceFlow id="Flow_1ik3tx8" sourceRef="Activity_0xhgkoq" targetRef="Activity_1q5reip" />
    <bpmn:sequenceFlow id="Flow_1g8ws5f" sourceRef="Activity_1q5reip" targetRef="Activity_1s55hxt" />
    <bpmn:sequenceFlow id="Flow_0wpwun7" sourceRef="Activity_1s55hxt" targetRef="Activity_0v27ib2" />
    <bpmn:sequenceFlow id="Flow_0az27vo" sourceRef="Activity_0v27ib2" targetRef="Activity_1h65y77" />
    <bpmn:sequenceFlow id="Flow_0rrkx8n" sourceRef="Activity_1h65y77" targetRef="Activity_02cfbx6" />
    <bpmn:sequenceFlow id="Flow_1hf6xwl" sourceRef="Activity_02cfbx6" targetRef="Event_1kccfq6" />
    <bpmn:sequenceFlow id="Flow_0bix4et" sourceRef="Activity_0lrb6tn" targetRef="Activity_1tsvezf" />
    <bpmn:task id="Activity_02cfbx6" name="Exibir resultado">
      <bpmn:incoming>Flow_0rrkx8n</bpmn:incoming>
      <bpmn:outgoing>Flow_1hf6xwl</bpmn:outgoing>
    </bpmn:task>
    <bpmn:endEvent id="Event_1kccfq6">
      <bpmn:incoming>Flow_1hf6xwl</bpmn:incoming>
      <bpmn:terminateEventDefinition id="TerminateEventDefinition_10rk2ia" />
    </bpmn:endEvent>
    <bpmn:dataStoreReference id="DataStoreReference_1f6w22w" name="Consulta STM ou LTM" />
    <bpmn:sequenceFlow id="Flow_00nvywq" sourceRef="Activity_1uvmq29" targetRef="Gateway_08e1zat" />
    <bpmn:sequenceFlow id="Flow_1ne8kiz" sourceRef="Gateway_08e1zat" targetRef="Activity_1bnakrz" />
    <bpmn:sequenceFlow id="Flow_1x0di0e" sourceRef="Gateway_08e1zat" targetRef="Activity_1av73xo" />
    <bpmn:task id="Activity_0lrb6tn" name="Criar fluxo de execução de Agentes">
      <bpmn:incoming>Flow_0rxexf5</bpmn:incoming>
      <bpmn:outgoing>Flow_0bix4et</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_1av73xo" name="Executa fallback">
      <bpmn:incoming>Flow_1x0di0e</bpmn:incoming>
    </bpmn:task>
    <bpmn:task id="Activity_1q5reip" name="Normaliza resposta">
      <bpmn:incoming>Flow_1ik3tx8</bpmn:incoming>
      <bpmn:outgoing>Flow_1g8ws5f</bpmn:outgoing>
      <bpmn:dataOutputAssociation id="DataOutputAssociation_12ztbez">
        <bpmn:targetRef>DataStoreReference_1ptl1ls</bpmn:targetRef>
      </bpmn:dataOutputAssociation>
    </bpmn:task>
    <bpmn:task id="Activity_1s55hxt" name="Seleciona ferramentas">
      <bpmn:incoming>Flow_1g8ws5f</bpmn:incoming>
      <bpmn:outgoing>Flow_0wpwun7</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_1tsvezf" name="Analisa consulta">
      <bpmn:incoming>Flow_0bix4et</bpmn:incoming>
      <bpmn:outgoing>Flow_09721af</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_1uvmq29" name="Executar prompt">
      <bpmn:incoming>Flow_1rbuiui</bpmn:incoming>
      <bpmn:outgoing>Flow_00nvywq</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_1bnakrz" name="Normalizar resposta">
      <bpmn:incoming>Flow_1ne8kiz</bpmn:incoming>
      <bpmn:outgoing>Flow_0rxexf5</bpmn:outgoing>
      <bpmn:dataOutputAssociation id="DataOutputAssociation_16xko3b">
        <bpmn:targetRef>DataStoreReference_1vxr84a</bpmn:targetRef>
      </bpmn:dataOutputAssociation>
      <bpmn:dataOutputAssociation id="DataOutputAssociation_1famwd4">
        <bpmn:targetRef>DataObjectReference_14kfbhm</bpmn:targetRef>
      </bpmn:dataOutputAssociation>
    </bpmn:task>
    <bpmn:exclusiveGateway id="Gateway_08e1zat" name="Executou com sucesso?">
      <bpmn:incoming>Flow_00nvywq</bpmn:incoming>
      <bpmn:outgoing>Flow_1ne8kiz</bpmn:outgoing>
      <bpmn:outgoing>Flow_1x0di0e</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:dataStoreReference id="DataStoreReference_1vxr84a" name="Salva no STM" />
    <bpmn:dataObjectReference id="DataObjectReference_14kfbhm" name="Resposta em .json" dataObjectRef="DataObject_0ozzwhz" />
    <bpmn:dataObject id="DataObject_0ozzwhz" />
    <bpmn:task id="Activity_16i3hrm" name="Controi prompt">
      <bpmn:incoming>Flow_09721af</bpmn:incoming>
      <bpmn:outgoing>Flow_0rtthz9</bpmn:outgoing>
    </bpmn:task>
    <bpmn:task id="Activity_0xhgkoq" name="Executa prompt">
      <bpmn:incoming>Flow_0rtthz9</bpmn:incoming>
      <bpmn:outgoing>Flow_1ik3tx8</bpmn:outgoing>
    </bpmn:task>
    <bpmn:dataStoreReference id="DataStoreReference_1ptl1ls" name="Salva STM" />
    <bpmn:task id="Activity_0v27ib2" name="Executa">
      <bpmn:incoming>Flow_0wpwun7</bpmn:incoming>
      <bpmn:outgoing>Flow_0az27vo</bpmn:outgoing>
      <bpmn:dataOutputAssociation id="DataOutputAssociation_0vpa5h6">
        <bpmn:targetRef>DataStoreReference_1mztcwg</bpmn:targetRef>
      </bpmn:dataOutputAssociation>
    </bpmn:task>
    <bpmn:dataStoreReference id="DataStoreReference_1mztcwg" name="Salva STM" />
    <bpmn:dataStoreReference id="DataStoreReference_1j7kded" name="LTM" />
    <bpmn:task id="Activity_1h65y77" name="Gerar resposta">
      <bpmn:incoming>Flow_0az27vo</bpmn:incoming>
      <bpmn:outgoing>Flow_0rrkx8n</bpmn:outgoing>
      <bpmn:dataOutputAssociation id="DataOutputAssociation_1f18y3l">
        <bpmn:targetRef>DataStoreReference_1j7kded</bpmn:targetRef>
      </bpmn:dataOutputAssociation>
    </bpmn:task>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1vsei8s">
      <bpmndi:BPMNShape id="Participant_11g4bwe_di" bpmnElement="Participant_11g4bwe" isHorizontal="true">
        <dc:Bounds x="132" y="38" width="2538" height="982" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0yzzpce_di" bpmnElement="Lane_0yzzpce" isHorizontal="true">
        <dc:Bounds x="162" y="860" width="2508" height="160" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1f3l0e7_di" bpmnElement="Lane_1f3l0e7" isHorizontal="true">
        <dc:Bounds x="162" y="38" width="2508" height="156" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_13v3xoc_di" bpmnElement="Lane_13v3xoc" isHorizontal="true">
        <dc:Bounds x="162" y="194" width="2508" height="226" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_16r1y5f_di" bpmnElement="Lane_16r1y5f" isHorizontal="true">
        <dc:Bounds x="162" y="420" width="2508" height="140" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1ilbuqx_di" bpmnElement="Lane_1ilbuqx" isHorizontal="true" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
        <dc:Bounds x="162" y="560" width="2508" height="170" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_077qvn7_di" bpmnElement="Lane_077qvn7" isHorizontal="true">
        <dc:Bounds x="162" y="730" width="2508" height="130" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_004lorm_di" bpmnElement="Activity_004lorm">
        <dc:Bounds x="320" y="78" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="212" y="100" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ms98aj_di" bpmnElement="Activity_1ms98aj">
        <dc:Bounds x="490" y="238" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1j4784z_di" bpmnElement="Activity_1j4784z">
        <dc:Bounds x="660" y="238" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_02cfbx6_di" bpmnElement="Activity_02cfbx6">
        <dc:Bounds x="2420" y="90" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_149haeu_di" bpmnElement="Event_1kccfq6">
        <dc:Bounds x="2572" y="112" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="DataStoreReference_1f6w22w_di" bpmnElement="DataStoreReference_1f6w22w">
        <dc:Bounds x="645" y="125" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="628" y="95" width="85" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0lrb6tn_di" bpmnElement="Activity_0lrb6tn">
        <dc:Bounds x="1290" y="78" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1av73xo_di" bpmnElement="Activity_1av73xo">
        <dc:Bounds x="1020" y="200" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1q5reip_di" bpmnElement="Activity_1q5reip">
        <dc:Bounds x="1760" y="610" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1s55hxt_di" bpmnElement="Activity_1s55hxt">
        <dc:Bounds x="1940" y="610" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1tsvezf_di" bpmnElement="Activity_1tsvezf">
        <dc:Bounds x="1290" y="610" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1uvmq29_di" bpmnElement="Activity_1uvmq29">
        <dc:Bounds x="760" y="450" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1bnakrz_di" bpmnElement="Activity_1bnakrz">
        <dc:Bounds x="1020" y="310" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_08e1zat_di" bpmnElement="Gateway_08e1zat" isMarkerVisible="true">
        <dc:Bounds x="875" y="325" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="795" y="336" width="70" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="DataStoreReference_1vxr84a_di" bpmnElement="DataStoreReference_1vxr84a">
        <dc:Bounds x="1125" y="435" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1115" y="492" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="DataObjectReference_14kfbhm_di" bpmnElement="DataObjectReference_14kfbhm">
        <dc:Bounds x="1002" y="435" width="36" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="988" y="492" width="65" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_16i3hrm_di" bpmnElement="Activity_16i3hrm">
        <dc:Bounds x="1440" y="610" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0xhgkoq_di" bpmnElement="Activity_0xhgkoq">
        <dc:Bounds x="1580" y="450" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="DataStoreReference_1ptl1ls_di" bpmnElement="DataStoreReference_1ptl1ls">
        <dc:Bounds x="1665" y="705" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1663" y="762" width="54" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0v27ib2_di" bpmnElement="Activity_0v27ib2">
        <dc:Bounds x="2080" y="760" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="DataStoreReference_1mztcwg_di" bpmnElement="DataStoreReference_1mztcwg">
        <dc:Bounds x="1985" y="855" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1983" y="912" width="54" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1h65y77_di" bpmnElement="Activity_1h65y77">
        <dc:Bounds x="2260" y="910" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1eclttk_di" bpmnElement="Flow_1eclttk">
        <di:waypoint x="248" y="118" />
        <di:waypoint x="320" y="118" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gk7c2r_di" bpmnElement="Flow_1gk7c2r">
        <di:waypoint x="420" y="118" />
        <di:waypoint x="455" y="118" />
        <di:waypoint x="455" y="278" />
        <di:waypoint x="490" y="278" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0r6xjq6_di" bpmnElement="Flow_0r6xjq6">
        <di:waypoint x="590" y="278" />
        <di:waypoint x="660" y="278" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rbuiui_di" bpmnElement="Flow_1rbuiui">
        <di:waypoint x="710" y="318" />
        <di:waypoint x="710" y="490" />
        <di:waypoint x="760" y="490" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rxexf5_di" bpmnElement="Flow_0rxexf5">
        <di:waypoint x="1120" y="350" />
        <di:waypoint x="1265" y="350" />
        <di:waypoint x="1265" y="118" />
        <di:waypoint x="1290" y="118" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09721af_di" bpmnElement="Flow_09721af">
        <di:waypoint x="1390" y="650" />
        <di:waypoint x="1440" y="650" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rtthz9_di" bpmnElement="Flow_0rtthz9">
        <di:waypoint x="1490" y="610" />
        <di:waypoint x="1490" y="490" />
        <di:waypoint x="1580" y="490" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ik3tx8_di" bpmnElement="Flow_1ik3tx8">
        <di:waypoint x="1680" y="490" />
        <di:waypoint x="1810" y="490" />
        <di:waypoint x="1810" y="610" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g8ws5f_di" bpmnElement="Flow_1g8ws5f">
        <di:waypoint x="1860" y="650" />
        <di:waypoint x="1940" y="650" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wpwun7_di" bpmnElement="Flow_0wpwun7">
        <di:waypoint x="1990" y="690" />
        <di:waypoint x="1990" y="800" />
        <di:waypoint x="2080" y="800" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0az27vo_di" bpmnElement="Flow_0az27vo">
        <di:waypoint x="2180" y="800" />
        <di:waypoint x="2310" y="800" />
        <di:waypoint x="2310" y="910" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rrkx8n_di" bpmnElement="Flow_0rrkx8n">
        <di:waypoint x="2360" y="950" />
        <di:waypoint x="2380" y="950" />
        <di:waypoint x="2380" y="130" />
        <di:waypoint x="2420" y="130" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hf6xwl_di" bpmnElement="Flow_1hf6xwl">
        <di:waypoint x="2520" y="130" />
        <di:waypoint x="2572" y="130" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bix4et_di" bpmnElement="Flow_0bix4et">
        <di:waypoint x="1340" y="158" />
        <di:waypoint x="1340" y="610" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00nvywq_di" bpmnElement="Flow_00nvywq">
        <di:waypoint x="860" y="490" />
        <di:waypoint x="900" y="490" />
        <di:waypoint x="900" y="375" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ne8kiz_di" bpmnElement="Flow_1ne8kiz">
        <di:waypoint x="925" y="350" />
        <di:waypoint x="1020" y="350" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1x0di0e_di" bpmnElement="Flow_1x0di0e">
        <di:waypoint x="900" y="325" />
        <di:waypoint x="900" y="240" />
        <di:waypoint x="1020" y="240" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="DataStoreReference_1j7kded_di" bpmnElement="DataStoreReference_1j7kded">
        <dc:Bounds x="2175" y="1025" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2189" y="1082" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_08s5d3j_di" bpmnElement="TextAnnotation_08s5d3j">
        <dc:Bounds x="780" y="110" width="130" height="60" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="DataOutputAssociation_1qdqery_di" bpmnElement="DataOutputAssociation_1qdqery">
        <di:waypoint x="698" y="238" />
        <di:waypoint x="678" y="175" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="DataOutputAssociation_0vpa5h6_di" bpmnElement="DataOutputAssociation_0vpa5h6">
        <di:waypoint x="2081" y="833" />
        <di:waypoint x="2035" y="863" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="DataOutputAssociation_1f18y3l_di" bpmnElement="DataOutputAssociation_1f18y3l">
        <di:waypoint x="2266" y="989" />
        <di:waypoint x="2225" y="1027" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="DataOutputAssociation_12ztbez_di" bpmnElement="DataOutputAssociation_12ztbez">
        <di:waypoint x="1761" y="682" />
        <di:waypoint x="1715" y="713" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="DataOutputAssociation_16xko3b_di" bpmnElement="DataOutputAssociation_16xko3b">
        <di:waypoint x="1099" y="390" />
        <di:waypoint x="1132" y="435" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="DataOutputAssociation_1famwd4_di" bpmnElement="DataOutputAssociation_1famwd4">
        <di:waypoint x="1052" y="390" />
        <di:waypoint x="1031" y="435" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0qojvbv_di" bpmnElement="Association_0qojvbv">
        <di:waypoint x="741" y="238" />
        <di:waypoint x="795" y="170" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
