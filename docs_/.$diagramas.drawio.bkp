<mxfile host="Electron" modified="2025-07-19T16:48:53.507Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.2.5 Chrome/120.0.6099.109 Electron/28.1.0 Safari/537.36" etag="nyLA4h1N1wgq5j7_mVPX" version="24.2.5" type="device">
  <diagram name="Página-1" id="LTj1gPiyagm7XH6kqlLW">
    <mxGraphModel dx="1674" dy="836" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="NR3NLs1SwqtUjtMI9eED-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="NR3NLs1SwqtUjtMI9eED-1" target="NR3NLs1SwqtUjtMI9eED-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-4" value="Pergunta (query)&lt;div&gt;Usuário (customer_id)&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="NR3NLs1SwqtUjtMI9eED-3">
          <mxGeometry x="0.008" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-1" value="Actor" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="40" y="160" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" edge="1" parent="1" source="NR3NLs1SwqtUjtMI9eED-2" target="NR3NLs1SwqtUjtMI9eED-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-7" value="query" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="NR3NLs1SwqtUjtMI9eED-6">
          <mxGeometry x="-0.0167" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-2" value="OrchestratorAgent" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="280" y="160" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.75;entryDx=0;entryDy=0;exitX=0;exitY=0.75;exitDx=0;exitDy=0;" edge="1" parent="1" source="NR3NLs1SwqtUjtMI9eED-5" target="NR3NLs1SwqtUjtMI9eED-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-11" value="Análise da query" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="NR3NLs1SwqtUjtMI9eED-10">
          <mxGeometry x="-0.05" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-5" value="AnalyserAgent" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="560" y="160" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-12" value="Retorno do Agente Analisador de Pergunta:&#xa;&#xa;{&#xa;   &quot;context_notes&quot;:&quot;Sem contexto relevante. Última query do usuário: Quais smartphones estão disponíveis?&quot;,&#xa;   &quot;end_of_conversation&quot;:false,&#xa;   &quot;is_follow_up&quot;:false,&#xa;   &quot;product_context&quot;:{&#xa;      &quot;detail_level&quot;:&quot;basic&quot;,&#xa;      &quot;product_id&quot;:&quot;None&quot;,&#xa;      &quot;product_name&quot;:&quot;None&quot;&#xa;   },&#xa;   &quot;reasoning&quot;:&quot;Nova consulta sobre smartphones&quot;,&#xa;   &quot;relevant_products&quot;:[&#xa;      {&#xa;         &quot;product_id&quot;:&quot;None&quot;,&#xa;         &quot;product_name&quot;:&quot;Smartphones&quot;&#xa;      }&#xa;   ],&#xa;   &quot;required_agents&quot;:[&#xa;      &quot;response_generator&quot;,&#xa;      &quot;product_specialist&quot;&#xa;   ],&#xa;   &quot;specialized_queries&quot;:{&#xa;      &quot;product_specialist&quot;:&quot;Obter lista de smartphones disponíveis&quot;,&#xa;      &quot;response_generator&quot;:&quot;Listar smartphones disponíveis para o cliente&quot;&#xa;   },&#xa;   &quot;task_type&quot;:&quot;product_query&quot;&#xa;}" style="text;whiteSpace=wrap;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="440" y="320" width="380" height="400" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-13" value="&lt;h1 style=&quot;margin-top: 0px;&quot;&gt;Agente Orquestrador&lt;/h1&gt;&lt;div&gt;1. Recebe uma pergunta do usuário;&lt;/div&gt;&lt;div&gt;2. Enviar para Agente Analisador analisar a pergunta;&lt;/div&gt;&lt;div&gt;3. Cria o fluxo de execução do agentes especialistas;&lt;/div&gt;&lt;div&gt;4. Retorna o resultado&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="text;html=1;whiteSpace=wrap;overflow=hidden;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="80" y="320" width="280" height="160" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-14" value="OrchestratorAgent" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="1160" width="240" height="138" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-15" value="- input: InputUser" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-14">
          <mxGeometry y="26" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-18" value="- analyserAgent: AnalyserAgent" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-14">
          <mxGeometry y="52" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-19" value="- specialistsAgents: List&amp;lt;SpecialistAgent&amp;gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-14">
          <mxGeometry y="78" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-16" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-14">
          <mxGeometry y="104" width="240" height="8" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-17" value="+ method(type): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-14">
          <mxGeometry y="112" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-20" value="BaseAgent" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="470" y="860" width="230" height="190" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-21" value="- name: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-20">
          <mxGeometry y="26" width="230" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-26" value="- description: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-20">
          <mxGeometry y="52" width="230" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-48" value="- promptTemplate: String" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-20">
          <mxGeometry y="78" width="230" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-27" value="- memory: Memory" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-20">
          <mxGeometry y="104" width="230" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-28" value="- llm: Llm" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-20">
          <mxGeometry y="130" width="230" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-24" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-20">
          <mxGeometry y="156" width="230" height="8" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-25" value="+ execute(any): any" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-20">
          <mxGeometry y="164" width="230" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-29" value="Extends" style="endArrow=block;endSize=16;endFill=0;html=1;rounded=0;edgeStyle=elbowEdgeStyle;elbow=vertical;" edge="1" parent="1" source="NR3NLs1SwqtUjtMI9eED-14" target="NR3NLs1SwqtUjtMI9eED-20">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="540" y="990" as="sourcePoint" />
            <mxPoint x="700" y="990" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-30" value="AnalyserAgent" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="465" y="1160" width="240" height="86" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-31" value="- query: string" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-30">
          <mxGeometry y="26" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-34" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-30">
          <mxGeometry y="52" width="240" height="8" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-35" value="+ method(type): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-30">
          <mxGeometry y="60" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-37" value="Extends" style="endArrow=block;endSize=16;endFill=0;html=1;rounded=0;edgeStyle=elbowEdgeStyle;" edge="1" parent="1" source="NR3NLs1SwqtUjtMI9eED-30" target="NR3NLs1SwqtUjtMI9eED-20">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="410" y="1170" as="sourcePoint" />
            <mxPoint x="595" y="1034" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-40" value="SpecialistAgent" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="1160" width="240" height="112" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-46" value="- skills: List&amp;lt;String&amp;gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-40">
          <mxGeometry y="26" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-47" value="- tools: List&amp;lt;Tool&amp;gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-40">
          <mxGeometry y="52" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-42" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-40">
          <mxGeometry y="78" width="240" height="8" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-43" value="+ method(type): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="NR3NLs1SwqtUjtMI9eED-40">
          <mxGeometry y="86" width="240" height="26" as="geometry" />
        </mxCell>
        <mxCell id="NR3NLs1SwqtUjtMI9eED-44" value="Extends" style="endArrow=block;endSize=16;endFill=0;html=1;rounded=0;edgeStyle=elbowEdgeStyle;elbow=vertical;" edge="1" parent="1" source="NR3NLs1SwqtUjtMI9eED-40" target="NR3NLs1SwqtUjtMI9eED-20">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="570" y="1170" as="sourcePoint" />
            <mxPoint x="595" y="1034" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
