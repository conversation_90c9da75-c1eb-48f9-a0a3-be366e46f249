# Product Specialist Agent v3 - Custom Tools Implementation Plan

## 1. Project Analysis & Context

### Current Architecture
- **ProductSpecialistAgent**: Custom implementation with fuzzy matching, tag-based search, and synonym generation
- **ResponseGeneratorAgent**: Uses <PERSON><PERSON><PERSON><PERSON>'s ReAct pattern but is token-heavy and not robust enough
- **Integration**: Works with orchestrator, memory manager, and other agents in the chain
- **Data Source**: Currently uses JSON file (`LangChainAgent/data/products.json`), needs PostgreSQL support later

### Key Insights from Current Implementation
- Effective fuzzy matching and tag-based filtering
- Smart synonym generation using LLM
- Context-aware responses (basic vs detailed)
- Follow-up handling with conversation context
- Logging and debugging capabilities
- Memory integration for conversation state

## 2. Design Philosophy

### Custom Tool-Based Architecture Benefits
- **Modularity**: Each capability as a separate tool class
- **Extensibility**: Easy to add new search/filter capabilities
- **Testability**: Individual tools can be tested in isolation
- **Custom Control**: Full control over tool selection and execution logic
- **Maintainability**: Clear separation of concerns
- **Token Efficiency**: Custom orchestration without <PERSON><PERSON><PERSON><PERSON> overhead

### Hybrid Approach
- Use custom tool classes for modular capabilities
- Preserve current logic for search algorithms and data processing
- Maintain existing interfaces for seamless integration
- Keep custom agent logic inheriting from BaseAgent

## 3. Custom Tool Architecture Design

### 3.1 Tool Base Class

```python
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import json
import time

class ProductTool(ABC):
    """Base class for all product-related tools."""
    
    def __init__(self, name: str, description: str, agent_instance=None):
        self.name = name
        self.description = description
        self.agent = agent_instance
        self.execution_count = 0
        self.total_execution_time = 0.0
    
    @abstractmethod
    def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the tool with given parameters."""
        pass
    
    def get_schema(self) -> Dict[str, Any]:
        """Return the tool's parameter schema."""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": self._get_parameters_schema()
        }
    
    @abstractmethod
    def _get_parameters_schema(self) -> Dict[str, Any]:
        """Return the parameters schema for this tool."""
        pass
    
    def _log_execution(self, input_params: dict, output: dict, execution_time: float):
        """Log tool execution for debugging and analytics."""
        self.execution_count += 1
        self.total_execution_time += execution_time
        
        if self.agent and hasattr(self.agent, '_log_tool_usage'):
            self.agent._log_tool_usage(self.name, input_params, output, execution_time)
```

### 3.2 Core Search Tools

#### BasicProductSearchTool
```python
class BasicProductSearchTool(ProductTool):
    """Tool for basic product search using fuzzy matching."""
    
    def __init__(self, agent_instance):
        super().__init__(
            name="basic_product_search",
            description="Search for products using fuzzy matching and tag-based filtering. Returns basic product information.",
            agent_instance=agent_instance
        )
    
    def execute(self, query: str, category: str = None, max_results: int = 5) -> Dict[str, Any]:
        """Execute basic product search."""
        start_time = time.time()
        
        try:
            # Use existing search logic from current implementation
            results = self.agent._search_products_basic(query, category, max_results)
            
            output = {
                "success": True,
                "results": results,
                "count": len(results),
                "query": query,
                "category": category
            }
            
        except Exception as e:
            output = {
                "success": False,
                "error": str(e),
                "results": [],
                "count": 0
            }
        
        execution_time = time.time() - start_time
        self._log_execution({"query": query, "category": category}, output, execution_time)
        
        return output
    
    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "Search query from user"},
                "category": {"type": "string", "description": "Optional category filter"},
                "max_results": {"type": "integer", "description": "Maximum number of results", "default": 5}
            },
            "required": ["query"]
        }
```

#### DetailedProductSearchTool
```python
class DetailedProductSearchTool(ProductTool):
    """Tool for detailed product search with comprehensive information."""
    
    def __init__(self, agent_instance):
        super().__init__(
            name="detailed_product_search",
            description="Search for products with comprehensive details and specifications.",
            agent_instance=agent_instance
        )
    
    def execute(self, query: str, detail_level: str = "detailed") -> Dict[str, Any]:
        """Execute detailed product search."""
        start_time = time.time()
        
        try:
            # Use existing detailed search logic
            results = self.agent._search_products_detailed(query, detail_level)
            
            output = {
                "success": True,
                "results": results,
                "count": len(results),
                "detail_level": detail_level
            }
            
        except Exception as e:
            output = {
                "success": False,
                "error": str(e),
                "results": []
            }
        
        execution_time = time.time() - start_time
        self._log_execution({"query": query, "detail_level": detail_level}, output, execution_time)
        
        return output
    
    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "Search query from user"},
                "detail_level": {"type": "string", "enum": ["basic", "detailed", "full"], "default": "detailed"}
            },
            "required": ["query"]
        }
```

#### ProductByIdTool
```python
class ProductByIdTool(ProductTool):
    """Tool for retrieving specific product by ID."""
    
    def __init__(self, agent_instance):
        super().__init__(
            name="get_product_by_id",
            description="Retrieve specific product information by product ID.",
            agent_instance=agent_instance
        )
    
    def execute(self, product_id: str, detail_level: str = "full") -> Dict[str, Any]:
        """Get product by ID."""
        start_time = time.time()
        
        try:
            product = self.agent._get_product_by_id(product_id, detail_level)
            
            output = {
                "success": True,
                "product": product,
                "product_id": product_id
            }
            
        except Exception as e:
            output = {
                "success": False,
                "error": str(e),
                "product": None
            }
        
        execution_time = time.time() - start_time
        self._log_execution({"product_id": product_id, "detail_level": detail_level}, output, execution_time)
        
        return output
    
    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "product_id": {"type": "string", "description": "Unique product identifier"},
                "detail_level": {"type": "string", "enum": ["basic", "detailed", "full"], "default": "full"}
            },
            "required": ["product_id"]
        }
```

### 3.3 Context-Aware Tools

#### ConversationContextTool
```python
class ConversationContextTool(ProductTool):
    """Tool for retrieving conversation context and history."""
    
    def __init__(self, agent_instance):
        super().__init__(
            name="get_conversation_context",
            description="Retrieve conversation context including previously discussed products and history.",
            agent_instance=agent_instance
        )
    
    def execute(self, customer_id: str) -> Dict[str, Any]:
        """Get conversation context."""
        start_time = time.time()
        
        try:
            context = self.agent._get_conversation_context(customer_id)
            
            output = {
                "success": True,
                "context": context,
                "customer_id": customer_id
            }
            
        except Exception as e:
            output = {
                "success": False,
                "error": str(e),
                "context": {}
            }
        
        execution_time = time.time() - start_time
        self._log_execution({"customer_id": customer_id}, output, execution_time)
        
        return output
    
    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "customer_id": {"type": "string", "description": "Customer identifier for context retrieval"}
            },
            "required": ["customer_id"]
        }
```

#### FollowUpHandlerTool
```python
class FollowUpHandlerTool(ProductTool):
    """Tool for processing follow-up queries with context."""
    
    def __init__(self, agent_instance):
        super().__init__(
            name="handle_follow_up_query",
            description="Process follow-up queries using conversation context and product references.",
            agent_instance=agent_instance
        )
    
    def execute(self, query: str, customer_id: str, context: dict = None) -> Dict[str, Any]:
        """Handle follow-up query."""
        start_time = time.time()
        
        try:
            if context is None:
                context = self.agent._get_conversation_context(customer_id)
            
            result = self.agent._handle_follow_up(query, context)
            
            output = {
                "success": True,
                "result": result,
                "resolved_references": result.get("resolved_references", [])
            }
            
        except Exception as e:
            output = {
                "success": False,
                "error": str(e),
                "result": None
            }
        
        execution_time = time.time() - start_time
        self._log_execution({"query": query, "customer_id": customer_id}, output, execution_time)
        
        return output
    
    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "Follow-up query from user"},
                "customer_id": {"type": "string", "description": "Customer identifier"},
                "context": {"type": "object", "description": "Optional conversation context"}
            },
            "required": ["query", "customer_id"]
        }
```

### 3.4 PostgreSQL-Ready Search Tools

#### VectorProductSearchTool
```python
class VectorProductSearchTool(ProductTool):
    """Advanced product search using PostgreSQL vector search and FTS."""
    
    def __init__(self, agent_instance):
        super().__init__(
            name="vector_product_search",
            description="Advanced product search using vector similarity, FTS, and hybrid search methods.",
            agent_instance=agent_instance
        )
    
    def execute(self, query: str, search_type: str = "hybrid", max_results: int = 5, 
                category_filter: str = None, price_range: Dict = None) -> Dict[str, Any]:
        """Execute vector-based product search."""
        start_time = time.time()
        
        try:
            # Prepare search parameters
            search_params = {
                'query': query,
                'search_type': search_type,  # 'vector', 'fts', 'hybrid'
                'max_results': max_results,
                'category_filter': category_filter,
                'price_range': price_range
            }
            
            # Execute search based on data source
            if self.agent.data_source_type == 'postgresql':
                results = self._postgresql_vector_search(search_params)
            else:
                # Fallback to JSON with simulated vector scoring
                results = self._json_enhanced_search(search_params)
            
            # Structure results with scores and metadata
            structured_results = self._structure_search_results(results, query, search_type)
            
            output = {
                "success": True,
                "results": structured_results,
                "count": len(structured_results),
                "search_metadata": {
                    "search_type": search_type,
                    "query_processed": query,
                    "filters_applied": {
                        "category": category_filter,
                        "price_range": price_range
                    }
                }
            }
            
        except Exception as e:
            output = {
                "success": False,
                "error": str(e),
                "results": [],
                "count": 0
            }
        
        execution_time = time.time() - start_time
        self._log_execution(search_params, output, execution_time)
        
        return output
    
    def _postgresql_vector_search(self, params: Dict) -> List[Dict]:
        """Execute PostgreSQL vector search with FTS."""
        query = params['query']
        search_type = params['search_type']
        max_results = params['max_results']
        
        # Generate query embedding (future implementation)
        # query_embedding = self.agent.embedding_model.encode(query)
        
        if search_type == 'vector':
            sql = """
            SELECT p.*, 
                   1 - (p.embedding <=> %s::vector) as similarity_score,
                   ts_rank(p.search_vector, plainto_tsquery('portuguese', %s)) as fts_score
            FROM products p
            WHERE (%s IS NULL OR p.category = %s)
            ORDER BY p.embedding <=> %s::vector
            LIMIT %s;
            """
        elif search_type == 'fts':
            sql = """
            SELECT p.*, 
                   ts_rank(p.search_vector, plainto_tsquery('portuguese', %s)) as fts_score,
                   0 as similarity_score
            FROM products p
            WHERE p.search_vector @@ plainto_tsquery('portuguese', %s)
              AND (%s IS NULL OR p.category = %s)
            ORDER BY ts_rank(p.search_vector, plainto_tsquery('portuguese', %s)) DESC
            LIMIT %s;
            """
        else:  # hybrid
            sql = """
            WITH vector_results AS (
                SELECT p.*, 
                       1 - (p.embedding <=> %s::vector) as similarity_score,
                       'vector' as search_method
                FROM products p
                WHERE (%s IS NULL OR p.category = %s)
                ORDER BY p.embedding <=> %s::vector
                LIMIT %s
            ),
            fts_results AS (
                SELECT p.*, 
                       ts_rank(p.search_vector, plainto_tsquery('portuguese', %s)) as fts_score,
                       'fts' as search_method
                FROM products p
                WHERE p.search_vector @@ plainto_tsquery('portuguese', %s)
                  AND (%s IS NULL OR p.category = %s)
                ORDER BY ts_rank(p.search_vector, plainto_tsquery('portuguese', %s)) DESC
                LIMIT %s
            )
            SELECT * FROM (
                SELECT *, similarity_score * 0.7 + COALESCE(fts_score, 0) * 0.3 as combined_score
                FROM vector_results
                UNION ALL
                SELECT *, COALESCE(similarity_score, 0) * 0.7 + fts_score * 0.3 as combined_score
                FROM fts_results
            ) combined
            ORDER BY combined_score DESC
            LIMIT %s;
            """
        
        # Execute query (implementation depends on connection setup)
        # results = self.agent.db_connection.execute(sql, params)
        # return results
        
        # For now, return empty list (will be implemented with actual DB)
        return []
    
    def _json_enhanced_search(self, params: Dict) -> List[Dict]:
        """Enhanced JSON search with simulated vector scoring."""
        query = params['query']
        max_results = params['max_results']
        category_filter = params['category_filter']
        
        # Use existing search logic with enhanced scoring
        results = self.agent._search_products_basic(query, category_filter, max_results * 2)
        
        # Add simulated vector scores
        for result in results:
            result['similarity_score'] = self._calculate_similarity_score(query, result)
            result['fts_score'] = self._calculate_fts_score(query, result)
            result['combined_score'] = result['similarity_score'] * 0.7 + result['fts_score'] * 0.3
        
        # Sort by combined score and limit results
        results.sort(key=lambda x: x['combined_score'], reverse=True)
        return results[:max_results]
    
    def _structure_search_results(self, results: List[Dict], query: str, search_type: str) -> List[Dict]:
        """Structure search results with consistent format."""
        structured_results = []
        
        for result in results:
            structured_result = {
                "id": result.get('id'),
                "name": result.get('name'),
                "category": result.get('category'),
                "brand": result.get('brand'),
                "price": result.get('price'),
                "short_description": result.get('short_description'),
                "image_url": result.get('image_url'),
                "availability": result.get('availability', True),
                "scores": {
                    "similarity_score": result.get('similarity_score', 0),
                    "fts_score": result.get('fts_score', 0),
                    "combined_score": result.get('combined_score', 0),
                    "relevance_explanation": self._explain_relevance(result, query)
                },
                "key_features": self._extract_key_features(result, query),
                "match_highlights": self._generate_match_highlights(result, query)
            }
            
            structured_results.append(structured_result)
        
        return structured_results
    
    def _explain_relevance(self, product: Dict, query: str) -> str:
        """Generate explanation for why this product is relevant."""
        explanations = []
        
        if query.lower() in product.get('name', '').lower():
            explanations.append("Nome corresponde à busca")
        
        if query.lower() in product.get('category', '').lower():
            explanations.append("Categoria relevante")
        
        # Add more relevance explanations based on features, specs, etc.
        
        return "; ".join(explanations) if explanations else "Correspondência geral"
    
    def _extract_key_features(self, product: Dict, query: str) -> List[str]:
        """Extract key features relevant to the query."""
        features = []
        
        # Extract from specifications if available
        specs = product.get('specifications', {})
        for key, value in specs.items():
            if any(term in key.lower() or term in str(value).lower() 
                   for term in query.lower().split()):
                features.append(f"{key}: {value}")
        
        return features[:3]  # Limit to top 3 relevant features
    
    def _generate_match_highlights(self, product: Dict, query: str) -> Dict[str, str]:
        """Generate highlighted matches in product fields."""
        highlights = {}
        query_terms = query.lower().split()
        
        for field in ['name', 'description', 'category']:
            field_value = product.get(field, '')
            if field_value and any(term in field_value.lower() for term in query_terms):
                highlights[field] = field_value  # In real implementation, add highlighting
        
        return highlights
    
    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "Search query"},
                "search_type": {
                    "type": "string", 
                    "enum": ["vector", "fts", "hybrid"], 
                    "default": "hybrid",
                    "description": "Type of search to perform"
                },
                "max_results": {"type": "integer", "default": 5},
                "category_filter": {"type": "string", "description": "Filter by category"},
                "price_range": {
                    "type": "object",
                    "properties": {
                        "min": {"type": "number"},
                        "max": {"type": "number"}
                    }
                }
            },
            "required": ["query"]
        }
```

#### CategorySearchTool
```python
class CategorySearchTool(ProductTool):
    """Tool for category-based product search and browsing."""
    
    def __init__(self, agent_instance):
        super().__init__(
            name="category_search",
            description="Search and browse products by category with filtering options.",
            agent_instance=agent_instance
        )
    
    def execute(self, category: str, subcategory: str = None, 
                sort_by: str = "relevance", filters: Dict = None) -> Dict[str, Any]:
        """Execute category-based search."""
        start_time = time.time()
        
        try:
            if self.agent.data_source_type == 'postgresql':
                results = self._postgresql_category_search(category, subcategory, sort_by, filters)
            else:
                results = self._json_category_search(category, subcategory, sort_by, filters)
            
            # Structure results with category metadata
            structured_results = self._structure_category_results(results, category, subcategory)
            
            output = {
                "success": True,
                "results": structured_results,
                "count": len(structured_results),
                "category_metadata": {
                    "category": category,
                    "subcategory": subcategory,
                    "total_in_category": self._get_category_count(category),
                    "available_filters": self._get_available_filters(category),
                    "sort_by": sort_by
                }
            }
            
        except Exception as e:
            output = {
                "success": False,
                "error": str(e),
                "results": [],
                "count": 0
            }
        
        execution_time = time.time() - start_time
        self._log_execution({
            "category": category, 
            "subcategory": subcategory, 
            "sort_by": sort_by
        }, output, execution_time)
        
        return output
    
    def _postgresql_category_search(self, category: str, subcategory: str, 
                                   sort_by: str, filters: Dict) -> List[Dict]:
        """PostgreSQL category search with advanced filtering."""
        sql = """
        SELECT p.*, 
               COUNT(*) OVER() as total_count,
               ROW_NUMBER() OVER(ORDER BY 
                   CASE WHEN %s = 'price_asc' THEN p.price END ASC,
                   CASE WHEN %s = 'price_desc' THEN p.price END DESC,
                   CASE WHEN %s = 'name' THEN p.name END ASC,
                   p.created_at DESC
               ) as rank
        FROM products p
        WHERE p.category ILIKE %s
          AND (%s IS NULL OR p.subcategory ILIKE %s)
          AND (%s IS NULL OR p.price >= %s)
          AND (%s IS NULL OR p.price <= %s)
          AND p.availability = true
        ORDER BY rank
        LIMIT 20;
        """
        
        # Execute with proper parameters
        # results = self.agent.db_connection.execute(sql, params)
        return []
    
    def _json_category_search(self, category: str, subcategory: str, 
                             sort_by: str, filters: Dict) -> List[Dict]:
        """JSON-based category search."""
        results = []
        
        for product in self.agent.products:
            if product.get('category', '').lower() == category.lower():
                if not subcategory or product.get('subcategory', '').lower() == subcategory.lower():
                    # Apply filters
                    if self._apply_filters(product, filters):
                        results.append(product)
        
        # Sort results
        if sort_by == 'price_asc':
            results.sort(key=lambda x: x.get('price', 0))
        elif sort_by == 'price_desc':
            results.sort(key=lambda x: x.get('price', 0), reverse=True)
        elif sort_by == 'name':
            results.sort(key=lambda x: x.get('name', ''))
        
        return results
    
    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "category": {"type": "string", "description": "Product category"},
                "subcategory": {"type": "string", "description": "Product subcategory"},
                "sort_by": {
                    "type": "string",
                    "enum": ["relevance", "price_asc", "price_desc", "name"],
                    "default": "relevance"
                },
                "filters": {
                    "type": "object",
                    "properties": {
                        "price_min": {"type": "number"},
                        "price_max": {"type": "number"},
                        "brand": {"type": "string"},
                        "features": {"type": "array", "items": {"type": "string"}}
                    }
                }
            },
            "required": ["category"]
        }
```

## 4. Custom Agent Implementation

### 4.1 Core Agent Class

```python
from core.agents.base_agent import BaseAgent
from core.utils.llm_manager import LlmManager
from typing import Dict, Any, List
import json


class ProductSpecialistAgentV3(BaseAgent):
    """
    Custom tool-based product specialist agent.
    Inherits from BaseAgent and uses LLMFactory for LLM access.
    """

    def __init__(self, config: Dict[str, Any], llm, count_tokens_fn=None, memory_manager=None):
        super().__init__(config, llm, count_tokens_fn, memory_manager)

        # Load products and tag schema (existing logic)
        self.data_path = config.get('data_path', '../../data/')
        self.products = self._load_products()
        self.tag_schema = self._load_tag_schema()

        # Initialize tools
        self.tools = self._create_tools()
        self.tool_registry = {tool.name: tool for tool in self.tools}

        # Tool selection strategy
        self.tool_selection_strategy = config.get('tool_selection_strategy', 'llm_guided')

        # Performance tracking
        self.query_count = 0
        self.total_query_time = 0.0

    def _create_tools(self) -> List[ProductTool]:
        """Create and configure all tools for the agent."""
        tools = [
            BasicProductSearchTool(self),
            DetailedProductSearchTool(self),
            ProductByIdTool(self),
            ConversationContextTool(self),
            FollowUpHandlerTool(self),
            SynonymGeneratorTool(self),
            ProductComparisonTool(self),
            CategoryFilterTool(self)
        ]

        print(f"[ProductSpecialistAgentV3] Initialized {len(tools)} tools")
        return tools

    def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main execution method maintaining orchestrator compatibility.
        """
        start_time = time.time()
        self.query_count += 1

        try:
            # Extract input data
            query = input_data.get('specialized_query', '')
            customer_id = input_data.get('customer_id', 'default_customer')
            conversation_data = input_data.get('conversation_data', {})

            # Analyze query and select appropriate tools
            tool_plan = self._analyze_and_plan_tools(query, customer_id, conversation_data)

            # Execute tool plan
            execution_results = self._execute_tool_plan(tool_plan, query, customer_id, conversation_data)

            # Generate final response
            final_response = self._generate_final_response(execution_results, query, customer_id)

            # Update memory if available
            if self.memory_manager:
                self._update_memory(customer_id, query, execution_results, final_response)

            execution_time = time.time() - start_time
            self.total_query_time += execution_time

            return {
                'success': True,
                'response': final_response,
                'agent_id': self.id,
                'execution_time': execution_time,
                'tools_used': [step['tool'] for step in tool_plan],
                'token_usage': self._get_token_usage()
            }

        except Exception as e:
            execution_time = time.time() - start_time
            print(f"[ProductSpecialistAgentV3] Error in execute: {str(e)}")

            return {
                'success': False,
                'error': str(e),
                'agent_id': self.id,
                'execution_time': execution_time
            }

    def _analyze_and_plan_tools(self, query: str, customer_id: str, conversation_data: Dict) -> List[Dict]:
        """
        Analyze query and create execution plan for tools using LLM guidance.
        Uses intelligent tool selection instead of hard-coded rules.
        """
        # Prepare context for LLM analysis
        context_info = self._prepare_tool_selection_context(query, customer_id, conversation_data)

        # Use LLM to select appropriate tools
        if self.tool_selection_strategy == 'llm_guided':
            tool_plan = self._llm_guided_tool_selection(query, context_info)
        else:
            # Fallback to rule-based selection
            tool_plan = self._rule_based_tool_selection(query, context_info)

        return tool_plan

    def _prepare_tool_selection_context(self, query: str, customer_id: str, conversation_data: Dict) -> Dict:
        """Prepare context information for tool selection."""
        return {
            'is_follow_up': self._is_follow_up_query(query, conversation_data),
            'has_product_context': bool(conversation_data.get('product_context')),
            'conversation_length': len(conversation_data.get('conversation_history', [])),
            'previous_products': conversation_data.get('previous_products', []),
            'customer_preferences': conversation_data.get('customer_preferences', {}),
            'query_intent': self._analyze_query_intent(query),
            'query_complexity': self._assess_query_complexity(query)
        }

    def _llm_guided_tool_selection(self, query: str, context: Dict) -> List[Dict]:
        """Use LLM to intelligently select tools based on query and context."""

        # Create tool selection prompt
        tools_description = self._get_tools_description()

        prompt = f"""
Você é um especialista em seleção de ferramentas para busca de produtos. Analise a consulta do usuário e o contexto para selecionar as ferramentas mais apropriadas.

CONSULTA DO USUÁRIO: {query}

CONTEXTO:
- É follow-up: {context['is_follow_up']}
- Tem contexto de produtos: {context['has_product_context']}
- Produtos anteriores: {len(context['previous_products'])} produtos
- Intenção da consulta: {context['query_intent']}
- Complexidade: {context['query_complexity']}

FERRAMENTAS DISPONÍVEIS:
{tools_description}

INSTRUÇÕES:
1. Selecione as ferramentas necessárias na ordem de execução
2. Para cada ferramenta, especifique os parâmetros principais
3. Considere se é busca básica, detalhada, por ID, comparação, etc.
4. Para follow-ups, sempre considere contexto primeiro
5. Para buscas complexas, pode usar múltiplas ferramentas

RESPONDA EM JSON:
{{
    "tools": [
        {{
            "tool": "nome_da_ferramenta",
            "params": {{"param1": "valor1", "param2": "valor2"}},
            "reason": "motivo para usar esta ferramenta",
            "priority": 1
        }}
    ],
    "search_strategy": "basic|detailed|comparison|follow_up",
    "expected_result_type": "product_list|single_product|comparison|context_aware"
}}

JSON:"""

        try:
            response = self._call_llm(prompt, context.get('customer_id', 'system'))

            # Parse LLM response
            tool_plan_data = json.loads(response.strip())

            # Convert to internal format
            tool_plan = []
            for tool_info in tool_plan_data.get('tools', []):
                tool_plan.append({
                    'tool': tool_info['tool'],
                    'params': tool_info['params'],
                    'reason': tool_info['reason'],
                    'priority': tool_info.get('priority', 1),
                    'search_strategy': tool_plan_data.get('search_strategy', 'basic'),
                    'expected_result_type': tool_plan_data.get('expected_result_type', 'product_list')
                })

            return sorted(tool_plan, key=lambda x: x['priority'])

        except Exception as e:
            print(f"[ProductSpecialistAgentV3] LLM tool selection failed: {e}")
            # Fallback to rule-based selection
            return self._rule_based_tool_selection(query, context)

    def _get_tools_description(self) -> str:
        """Get formatted description of all available tools."""
        descriptions = []
        for tool in self.tools:
            schema = tool.get_schema()
            descriptions.append(f"- {tool.name}: {tool.description}")

        return "\n".join(descriptions)

    def _rule_based_tool_selection(self, query: str, context: Dict) -> List[Dict]:
        """Fallback rule-based tool selection."""
        tool_plan = []

        if context['is_follow_up']:
            tool_plan.append({
                'tool': 'get_conversation_context',
                'params': {'customer_id': context.get('customer_id', 'default')},
                'reason': 'Follow-up query detected, need context',
                'priority': 1
            })
            tool_plan.append({
                'tool': 'handle_follow_up_query',
                'params': {'query': query, 'customer_id': context.get('customer_id', 'default')},
                'reason': 'Process follow-up with context',
                'priority': 2
            })
        else:
            # Determine search type based on query analysis
            search_type = self._determine_search_type(query)

            if search_type == 'basic':
                tool_plan.append({
                    'tool': 'vector_product_search',
                    'params': {'query': query, 'search_type': 'hybrid', 'max_results': 5},
                    'reason': 'Basic product search with vector similarity',
                    'priority': 1
                })
            elif search_type == 'detailed':
                tool_plan.append({
                    'tool': 'detailed_product_search',
                    'params': {'query': query, 'detail_level': 'detailed', 'include_specs': True},
                    'reason': 'Detailed product information requested',
                    'priority': 1
                })
            elif search_type == 'comparison':
                tool_plan.append({
                    'tool': 'vector_product_search',
                    'params': {'query': query, 'search_type': 'comparison', 'max_results': 10},
                    'reason': 'Search products for comparison',
                    'priority': 1
                })
                tool_plan.append({
                    'tool': 'compare_products',
                    'params': {'comparison_criteria': self._extract_comparison_criteria(query)},
                    'reason': 'Compare found products',
                    'priority': 2
                })

        return tool_plan

    def _analyze_query_intent(self, query: str) -> str:
        """Analyze query to determine user intent."""
        query_lower = query.lower()

        if any(word in query_lower for word in ['comparar', 'diferença', 'melhor', 'vs']):
            return 'comparison'
        elif any(word in query_lower for word in ['detalhes', 'especificações', 'características']):
            return 'detailed_info'
        elif any(word in query_lower for word in ['preço', 'custo', 'valor', 'barato']):
            return 'price_focused'
        elif any(word in query_lower for word in ['categoria', 'tipo', 'seção']):
            return 'category_browse'
        else:
            return 'general_search'

    def _assess_query_complexity(self, query: str) -> str:
        """Assess the complexity of the query."""
        word_count = len(query.split())

        if word_count <= 3:
            return 'simple'
        elif word_count <= 8:
            return 'medium'
        else:
            return 'complex'

    def _execute_tool_plan(self, tool_plan: List[Dict], query: str, customer_id: str, conversation_data: Dict) -> List[
        Dict]:
        """Execute the planned tools in sequence."""
        execution_results = []
        context = {}

        for step in tool_plan:
            tool_name = step['tool']
            params = step['params']

            if tool_name in self.tool_registry:
                tool = self.tool_registry[tool_name]

                # Update params with context if needed
                if tool_name == 'handle_follow_up_query' and 'context' in context:
                    params['context'] = context['context']
                elif tool_name == 'compare_products' and 'product_ids' not in params:
                    # Extract product IDs from previous search results
                    previous_results = self._get_previous_search_results(execution_results)
                    if previous_results:
                        params['product_ids'] = [p['id'] for p in previous_results[:5]]

                # Execute tool
                result = tool.execute(**params)

                # Store result and update context
                execution_results.append({
                    'tool': tool_name,
                    'params': params,
                    'result': result,
                    'reason': step['reason']
                })

                # Update context for next tools
                if tool_name == 'get_conversation_context':
                    context.update(result)
                elif 'results' in result:
                    context['last_search_results'] = result['results']

        return execution_results

    def _generate_final_response(self, execution_results: List[Dict], query: str, customer_id: str) -> str:
        """Generate final response with structured product information."""

        # Extract products from execution results
        products_found = self._extract_products_from_results(execution_results)

        # Prepare structured context for LLM
        context_summary = self._prepare_structured_response_context(execution_results, products_found)

        # Create enhanced prompt for response generation
        prompt = self._create_enhanced_response_prompt(query, context_summary, products_found, customer_id)

        # Generate response using LLM
        response = self._call_llm(prompt, customer_id)

        return response

    def _prepare_structured_response_context(self, execution_results: List[Dict], products: List[Dict]) -> str:
        """Prepare structured context with product information and scores."""
        context_parts = []

        for result in execution_results:
            tool_name = result['tool']
            tool_result = result['result']

            if tool_result.get('success'):
                if 'results' in tool_result:
                    # Format product results with scores and metadata
                    products_info = []
                    for product in tool_result['results']:
                        product_info = f"""
Produto: {product.get('name', 'N/A')}
ID: {product.get('id', 'N/A')}
Categoria: {product.get('category', 'N/A')}
Preço: R$ {product.get('price', 'N/A')}
Relevância: {product.get('scores', {}).get('combined_score', 0):.2f}
Características principais: {', '.join(product.get('key_features', []))}
"""
                        products_info.append(product_info)

                    context_parts.append(f"Ferramenta {tool_name}:\n" + "\n".join(products_info))
                else:
                    context_parts.append(f"Ferramenta {tool_name}: {tool_result}")

        return "\n\n".join(context_parts)

    def _create_enhanced_response_prompt(self, query: str, context_summary: str,
                                         products: List[Dict], customer_id: str) -> str:
        """Create enhanced prompt for response generation."""

        products_count = len(products)
        has_scores = any(p.get('scores') for p in products)

        prompt = f"""
Você é um especialista em produtos ajudando um cliente a encontrar produtos.

CONSULTA DO CLIENTE: {query}

PRODUTOS ENCONTRADOS ({products_count} produtos):
{context_summary}

INSTRUÇÕES PARA RESPOSTA:
1. Seja conversacional e útil em português
2. Apresente os produtos de forma organizada
3. {f"Use as pontuações de relevância para destacar os melhores matches" if has_scores else ""}
4. Inclua informações importantes como preço, características principais
5. Se houver muitos produtos, destaque os 3 melhores
6. Seja preciso - use apenas informações fornecidas
7. Ofereça ajuda adicional se apropriado

FORMATO DA RESPOSTA:
- Introdução breve respondendo à consulta
- Lista dos produtos relevantes
- Destaque características importantes
- Conclusão com oferta de ajuda adicional

Resposta:"""

        return prompt

    # Existing methods from current implementation (adapted)
    def _load_products(self):
        """Load products from JSON file (existing logic)."""
        # Implementation from current ProductSpecialistAgent
        pass

    def _load_tag_schema(self):
        """Load tag schema (existing logic)."""
        # Implementation from current ProductSpecialistAgent
        pass

    def _search_products_basic(self, query: str, category: str = None, max_results: int = 5):
        """Basic product search using existing fuzzy matching logic."""
        # Implementation from current ProductSpecialistAgent
        pass

    def _search_products_detailed(self, query: str, detail_level: str = "detailed"):
        """Detailed product search using existing logic."""
        # Implementation from current ProductSpecialistAgent
        pass

    def _get_product_by_id(self, product_id: str, detail_level: str = "full"):
        """Get product by ID using existing logic."""
        # Implementation from current ProductSpecialistAgent
        pass

    def _is_follow_up_query(self, query: str, conversation_data: Dict) -> bool:
        """Determine if query is a follow-up (existing logic)."""
        # Implementation from current ProductSpecialistAgent
        pass

    def _determine_search_type(self, query: str) -> str:
        """Determine type of search needed based on query."""
        # Custom logic to analyze query and determine search type
        # Can use simple keyword matching or LLM analysis
        pass
```

### 4.2 Tool Selection Strategies

```python
class ToolSelectionStrategy(ABC):
    """Abstract base class for tool selection strategies."""
    
    @abstractmethod
    def select_tools(self, query: str, context: Dict, available_tools: List[ProductTool]) -> List[Dict]:
        pass

class RuleBasedToolSelection(ToolSelectionStrategy):
    """Rule-based tool selection using keywords and patterns."""
    
    def select_tools(self, query: str, context: Dict, available_tools: List[ProductTool]) -> List[Dict]:
        tools_to_use = []
        
        # Rule-based logic for tool selection
        if any(word in query.lower() for word in ['comparar', 'diferença', 'melhor']):
            tools_to_use.append('compare_products')
        elif any(word in query.lower() for word in ['detalhes', 'especificações', 'características']):
            tools_to_use.append('detailed_product_search')
        else:
            tools_to_use.append('basic_product_search')
        
        return tools_to_use

class LLMGuidedToolSelection(ToolSelectionStrategy):
    """LLM-guided tool selection for more intelligent decisions."""
    
    def __init__(self, llm, agent_instance):
        self.llm = llm
        self.agent = agent_instance
    
    def select_tools(self, query: str, context: Dict, available_tools: List[ProductTool]) -> List[Dict]:
        # Use LLM to analyze query and select appropriate tools
        tool_descriptions = "\n".join([f"- {tool.name}: {tool.description}" for tool in available_tools])
        
        prompt = f"""
Analise a consulta do usuário e selecione as ferramentas mais apropriadas.

Consulta: {query}
Contexto: {json.dumps(context, ensure_ascii=False)}

Ferramentas disponíveis:
{tool_descriptions}

Retorne apenas os nomes das ferramentas necessárias, separados por vírgula.
Exemplo: basic_product_search, detailed_product_search

Ferramentas selecionadas:"""
        
        response = self.agent._call_llm(prompt)
        selected_tools = [tool.strip() for tool in response.split(',')]
        
        return selected_tools
```

## 5. Integration Strategy

### 5.1 Orchestrator Compatibility

The agent maintains full compatibility with the existing orchestrator by:

```python
def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Maintains exact same interface as current ProductSpecialistAgent.
    Input: orchestrator format
    Output: orchestrator format
    """
    # Same interface, tool-based implementation
    pass
```

### 5.2 Memory Manager Integration

```python
def _update_memory(self, customer_id: str, query: str, execution_results: List[Dict], final_response: str):
    """Update memory manager with conversation state and product context."""
    if self.memory_manager:
        # Extract product references from results
        product_references = self._extract_product_references(execution_results)
        
        # Update conversation context
        self.memory_manager.update_conversation_context(
            customer_id=customer_id,
            query=query,
            response=final_response,
            products_discussed=product_references,
            tools_used=[result['tool'] for result in execution_results]
        )
```

## 6. Data Layer Abstraction

### 6.1 Data Source Interface

```python
from abc import ABC, abstractmethod

class ProductDataSource(ABC):
    """Abstract interface for product data sources."""
    
    @abstractmethod
    def search_products(self, query: str, filters: dict = None) -> List[dict]:
        pass
    
    @abstractmethod
    def get_product_by_id(self, product_id: str) -> dict:
        pass
    
    @abstractmethod
    def get_categories(self) -> List[str]:
        pass

class JSONProductDataSource(ProductDataSource):
    """JSON file-based product data source (current implementation)."""
    
    def __init__(self, data_path: str):
        self.products = self._load_products(data_path)
        self.tag_schema = self._load_tag_schema(data_path)
    
    def search_products(self, query: str, filters: dict = None) -> List[dict]:
        # Existing search logic from current implementation
        pass
    
    def get_product_by_id(self, product_id: str) -> dict:
        # Existing logic
        pass

class PostgreSQLProductDataSource(ProductDataSource):
    """PostgreSQL-based product data source (future implementation)."""
    
    def __init__(self, connection_params: dict):
        import psycopg2
        self.conn = psycopg2.connect(**connection_params)
    
    def search_products(self, query: str, filters: dict = None) -> List[dict]:
        # SQL-based search implementation
        pass
```

## 7. Performance Optimization

### 7.1 Token Usage Optimization

```python
class TokenOptimizer:
    """Optimize token usage in tool-based agent."""
    
    def __init__(self, agent_instance):
        self.agent = agent_instance
    
    def optimize_tool_results(self, results: List[Dict]) -> List[Dict]:
        """Optimize tool results to reduce token usage."""
        optimized = []
        
        for result in results:
            if result['tool'] == 'basic_product_search':
                # Keep only essential fields for basic search
                optimized_result = self._optimize_basic_search_result(result)
            elif result['tool'] == 'detailed_product_search':
                # Summarize detailed results
                optimized_result = self._optimize_detailed_search_result(result)
            else:
                optimized_result = result
            
            optimized.append(optimized_result)
        
        return optimized
    
    def _optimize_basic_search_result(self, result: Dict) -> Dict:
        """Optimize basic search results."""
        if 'results' in result['result']:
            optimized_products = []
            for product in result['result']['results']:
                optimized_product = {
                    'id': product.get('id'),
                    'name': product.get('name'),
                    'category': product.get('category'),
                    'price': product.get('price')
                }
                optimized_products.append(optimized_product)
            
            result['result']['results'] = optimized_products
        
        return result
```

## 8. Testing Strategy

### 8.1 Tool Testing

```python
import pytest
from unittest.mock import Mock, patch

class TestProductTools:
    """Test suite for individual tools."""
    
    def setup_method(self):
        self.mock_agent = Mock()
        self.mock_agent.products = [
            {"id": "1", "name": "Test Product", "category": "Electronics"}
        ]
    
    def test_basic_product_search_tool(self):
        tool = BasicProductSearchTool(self.mock_agent)
        
        # Mock the agent's search method
        self.mock_agent._search_products_basic.return_value = [
            {"id": "1", "name": "Test Product", "category": "Electronics"}
        ]
        
        result = tool.execute(query="test", max_results=5)
        
        assert result['success'] == True
        assert len(result['results']) == 1
        assert result['results'][0]['name'] == "Test Product"
    
    def test_tool_error_handling(self):
        tool = BasicProductSearchTool(self.mock_agent)
        
        # Mock an exception
        self.mock_agent._search_products_basic.side_effect = Exception("Test error")
        
        result = tool.execute(query="test")
        
        assert result['success'] == False
        assert 'error' in result
        assert result['results'] == []
```

### 8.2 Integration Testing

```python
class TestAgentIntegration:
    """Test suite for agent integration."""
    
    def test_orchestrator_compatibility(self):
        # Test that agent maintains same interface
        config = {"id": "test_agent", "role": "product_specialist"}
        llm = Mock()
        agent = ProductSpecialistAgentV3(config, llm)
        
        input_data = {
            "specialized_query": "buscar notebooks",
            "customer_id": "test_customer",
            "conversation_data": {}
        }
        
        result = agent.execute(input_data)
        
        # Verify orchestrator-compatible response format
        assert 'success' in result
        assert 'response' in result
        assert 'agent_id' in result
    
    def test_memory_integration(self):
        # Test memory manager integration
        pass
```

## 9. Migration Strategy

### 9.1 Phase 1: Core Implementation (Week 1)
1. Implement `ProductTool` base class
2. Create core search tools (`BasicProductSearchTool`, `DetailedProductSearchTool`)
3. Implement basic `ProductSpecialistAgentV3` class
4. Ensure orchestrator compatibility

### 9.2 Phase 2: Enhanced Features (Week 2)
1. Add context-aware tools (`ConversationContextTool`, `FollowUpHandlerTool`)
2. Implement tool selection strategies
3. Add comprehensive logging and error handling
4. Performance optimization

### 9.3 Phase 3: Advanced Features (Week 3)
1. Add comparison and filtering tools
2. Implement data source abstraction
3. Add comprehensive testing
4. Documentation and examples

### 9.4 Phase 4: Production Ready (Week 4)
1. PostgreSQL integration preparation
2. Performance tuning and monitoring
3. Production deployment preparation
4. Migration from current implementation

## 10. Success Metrics

### Functional Metrics
- Query response accuracy maintained or improved
- Follow-up handling effectiveness
- Context preservation quality
- Tool selection accuracy

### Performance Metrics
- Token usage reduction compared to ReAct agent
- Response time optimization
- Memory usage efficiency
- Tool execution performance

### Integration Metrics
- Seamless orchestrator compatibility
- Memory manager integration success
- Error handling robustness
- Backward compatibility with existing system

This updated plan provides a custom tool-based architecture that avoids LangChain's ReAct agent while maintaining all the benefits of modular, tool-based design
