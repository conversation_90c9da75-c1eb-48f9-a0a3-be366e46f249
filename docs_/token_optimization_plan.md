# Token Optimization Plan: Tiered LLM Usage

## Overview

This document outlines a tier-based approach to optimize token usage and reduce costs in the LangChainAgent system by leveraging local models for simpler tasks and reserving cloud LLMs for complex reasoning.

---

## 1. Agent Prompt Complexity & Ranking

| Agent                  | Complexity      | Typical Token Usage | Description                                                                 | Local Model Suitability |
|------------------------|----------------|---------------------|-----------------------------------------------------------------------------|------------------------|
| **QueryAgent**         | High           | 800-1200            | Long prompt, memory context, JSON, examples, complex reasoning              | ⚠️ Limited             |
| **SummarizationAgent** | High           | 600-900             | Structured summary, privacy/detail levels, context analysis                 | ⚠️ Limited             |
| **ResponseGenerator**  | Medium         | 300-500             | Product formatting, follow-up logic, detailed instructions                  | ✅ Good                |
| **ProductSpecialist**  | Low            | 100-200             | Product matching, basic fuzzy search, minimal prompt                        | ✅ Excellent           |
| **OrchestratorAgent**  | Very Low       | 50-100              | Agent selection/routing, minimal prompt                                     | ✅ Excellent           |

---

## 2. Tier-Based Optimization Plan

### 🔴 TIER 1: High Complexity (Cloud LLMs)
- **QueryAgent**:  
  - Model: Cloud (Gemini/Mistral)
  - Reason: Complex reasoning, memory analysis, JSON parsing
- **SummarizationAgent**:  
  - Model: Cloud (Gemini/Mistral)
  - Reason: Structured output, privacy handling, complex analysis

### 🟡 TIER 2: Medium Complexity (Hybrid)
- **ResponseGeneratorAgent**:  
  - Model: Local (Ollama/Mistral-7B), fallback to Cloud
  - Optimization: Simplify prompts, reduce product details

### 🟢 TIER 3: Low Complexity (Local Models)
- **ProductSpecialistAgent**:  
  - Model: Local (Ollama/Mistral-7B)
  - Reason: Simple product matching, no complex reasoning
- **OrchestratorAgent**:  
  - Model: Local (Ollama/Mistral-7B)
  - Reason: Basic routing, minimal prompt

---

## 3. Implementation Strategy

### Phase 1: Quick Wins (TIER 3)
- Move **ProductSpecialistAgent** and **OrchestratorAgent** to local models.
- Token savings: ~225 tokens per conversation.
- Risk: Low.

### Phase 2: Medium Optimization (TIER 2)
- Move **ResponseGeneratorAgent** to local model with cloud fallback.
- Simplify prompts and reduce product details.
- Token savings: ~400 tokens per conversation.
- Risk: Medium.

### Phase 3: Advanced Optimization (TIER 1)
- Keep **QueryAgent** and **SummarizationAgent** on cloud LLMs.
- Optimize prompts for brevity and clarity.
- Token savings: ~350 tokens per conversation (prompt optimization only).
- Risk: High (do not move to local for now).

---

## 4. Expected Token Savings

- **Current Total per Conversation**: ~2000-3000 tokens
- **After Optimization**: ~1200-1800 tokens
- **Estimated Savings**: 40-50%

| Agent                  | Estimated Savings |
|------------------------|------------------|
| ProductSpecialistAgent | ~150 tokens      |
| OrchestratorAgent      | ~75 tokens       |
| ResponseGeneratorAgent | ~400 tokens      |
| QueryAgent             | ~200 tokens      |
| SummarizationAgent     | ~150 tokens      |
| **Total**              | **~975 tokens**  |

---

## 5. Next Steps

1. **Implement TIER 3 optimization** (ProductSpecialist + Orchestrator)
2. **Test local model performance** with simplified prompts
3. **Implement TIER 2 optimization** (ResponseGenerator)
4. **Monitor quality and performance**
5. **Consider TIER 1 optimizations** (prompt simplification only)

--- 