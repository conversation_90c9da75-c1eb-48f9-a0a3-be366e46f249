# Product Specialist Agent v2 – Design & Requirements Questions

Please answer or comment on the following points to help design your new Product Specialist agent that fully integrates with LangChain tools and a database backend.

---

## 1. Database & Data Model
- **What is the structure of your product data?**
  - What are the key fields (e.g., name, category, description, tags, price, etc.)?

    *The field should be acquired from a configuration file for that agent, placed with the .config folder*

  - Is the data in a PostgreSQL database, and if so, what is the schema?

    *PostgreSQL, schema default*
    
- **Should the agent only use full-text search (FTS) or also support structured queries (e.g., filter by price, category, etc.)?**

    *We can create tools to both FTS and to support structured queries, via fields*

## 2. LLM & Prompting
- **Which LLM do you want to use?**
  - Is Groq/llama3-70b-8192 your preferred model, or do you want to keep this configurable?

    *Agent should follow the llm_factory scheme, also it should inherit from base_agent, but adapt to fully use the langchain agentic code. Therefore, the llm should be configurable also via configuration file - we should keep the current project structure for the llm access. Now we are using groq api to access qween model*

- **Should the agent generate SQL queries for all types of questions, or only for search?**
  - For example, should it also handle "What is the cheapest product in category X?" or "List all products above $100"?
  *I hope this can be done by selecting the appropriated tool. In the current implementation of the project the query manager already passes a filtered query, but the agent should be dotted of llm to find synonyms to enhance the query it has received*

## 3. Tools & Agent Behavior
- **What tools should the agent have?**
  - Just FTS, or also CRUD (create, update, delete) operations, analytics, etc.?
    *Just FTS and the other tools I described earlier*

- **Should the agent be able to call multiple tools in a single conversation?**
  - For example, search, then fetch details, then update a product.
  *No, the agent should not interfere with the database to change it. It should only be allowed to read, not write.*

- **What should the agent do if the search returns no results?**
  - Ask for clarification, suggest similar products, etc.?
  *If no results were found, it should state that, such that the next agent handle the response. The response for the products agent should be a list of products, with the asked categories, details, or adicional information, and dotted with the query and customer id for tracking. It also should be aware if the query is an initial search or a follow up. This logic is already implemented in the project via the orchestrator*

## 4. User Interaction & Output
- **What kind of responses do you want?**
  - Just product names, or detailed product info, or even conversational explanations?
  *It depends on the query, if its a follow up. For initial queries, the list of match products, with basic information and description, added by the scoring for that match. For follow ups it should return all details available. Should not invent information, just rely on the database*

- **Should the agent support follow-up questions and context retention?**
  - E.g., "Tell me more about the second product" after a search.
  *It should. The way the project is made, the orchestrator should pass if it is a follow up, a initial query for a product or for a list of products*

## 5. Security & Access
- **Are there any authentication or authorization requirements for database access?**

 *just the authentication for accessing the database*

- **Should the agent log queries and responses for auditing or improvement?**
    *Yes, but the logs could be triggered on/off*

## 6. Deployment & Environment
- **Will this run as a backend service, CLI tool, or something else?**
 *The idea is to use a VPS server*

- **Should the database connection info be hardcoded, in env vars, or passed at runtime?**
 *in env vars*

---

**Add any other requirements or comments below:** 