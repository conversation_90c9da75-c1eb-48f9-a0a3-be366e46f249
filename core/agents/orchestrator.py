import json
import time
import copy
from typing import Dict, Any, List
from enum import Enum
from dataclasses import dataclass
from .base_agent import BaseAgent
from .product_specialist import ProductSpecialistAgent
from .response_generator import ResponseGeneratorAgent
from .query_agent import QueryAgent
from .specialist_agent import SpecialistAgent
from .summarization_agent import SummarizationAgent
from core.utils.memory_manager import MemoryManager
from core.utils.config_loader import ConfigLoader


class TaskType(Enum):
    PRODUCT_QUERY = "product_query"
    STORE_INFO = "store_info"
    SUPPORT = "support"
    GENERAL = "general"


@dataclass
class AgentExecution:
    agent_id: str
    input_tokens: int
    output_tokens: int
    execution_time: float
    success: bool


class OrchestratorAgent(BaseAgent):
    def __init__(self, config: Dict[str, Any], llm):
        super().__init__(config, llm, memory_manager=MemoryManager())

        self._specialists_agents: list[SpecialistAgent] = []

        # Initialize MemoryManager
        self.memory_manager = self.memory_manager  # for clarity

        # Load agents configuration
        config_loader = ConfigLoader()
        agents_config = config_loader.load_config("config/agents.yaml")["agents"]

        # Validate agent configurations before initialization
        for agent_id, agent_config in agents_config.items():
            if not ConfigLoader.validate_agent_config(agent_config, agent_id):
                print(f"[OrchestratorAgent] Warning: Agent {agent_id} has missing required fields")

        # Initialize specialized agents with their own configs
        self.product_specialist = ProductSpecialistAgent(llm, data_path="data/", memory_manager=self.memory_manager)
        self.response_generator = ResponseGeneratorAgent(agents_config['response_generator'], llm,
                                                         memory_manager=self.memory_manager)
        self.query_agent = QueryAgent(agents_config['query_agent'], llm, memory_manager=self.memory_manager,
                                      data_path="data/")
        self.summarization_agent = SummarizationAgent(agents_config.get('summarization_agent', {}), llm,
                                                      memory_manager=self.memory_manager)

        # Load seller configuration using enhanced ConfigLoader
        try:
            self.seller_config = ConfigLoader.get_seller_config("config/seller_config.yaml")
        except Exception as e:
            print(f"[OrchestratorAgent] Warning: Could not load seller config: {e}")
            # Use default seller config
            self.seller_config = {
                'summary_settings': {
                    'detail_level': 'medium',
                    'privacy_level': 'standard',
                    'context_interactions': 3,
                    'max_summary_length': 200
                },
                'conversation_settings': {
                    'auto_summarize': True,
                    'save_context': True,
                    'follow_up_detection': True
                }
            }

        # Flag to control LLM-based analysis vs fallback methods
        self.use_llm_analysis = config.get('use_llm_analysis', True)

        # Task routing rules (efficient, no LLM needed) - KEPT AS FALLBACK
        self.task_rules = self._setup_task_rules()

        # Token tracking
        self.current_tokens_used = 0
        self.max_tokens_per_query = config.get('max_tokens', 1000)

    def append_specialist(self, specialist_agent: SpecialistAgent):
        self._specialists_agents.append(specialist_agent)

    def _setup_task_rules(self) -> Dict[TaskType, Dict]:
        """Setup efficient rule-based task routing (no LLM tokens)"""
        return {
            TaskType.PRODUCT_QUERY: {
                'keywords': ['produto', 'preço', 'estoque', 'disponível', 'comprar',
                             'smartphone', 'notebook', 'fone', 'valor', 'especificação',
                             'produtos', 'item', 'artigo', 'mercadoria', 'bem', 'detalhes',
                             'primeiro produto', 'segundo produto', 'outro produto'],
                'agents': ['product_specialist', 'response_generator'],
                'max_tokens': 350
            },
            TaskType.STORE_INFO: {
                'keywords': ['horário', 'endereço', 'contato', 'telefone', 'loja',
                             'funcionamento', 'localização', 'aberto', 'fechado'],
                'agents': ['response_generator'],
                'max_tokens': 150
            },
            TaskType.SUPPORT: {
                'keywords': ['problema', 'ajuda', 'suporte', 'erro', 'defeito',
                             'garantia', 'troca', 'devolução', 'defeituoso', 'quebrado',
                             'não funciona', 'mau funcionamento', 'assistência'],
                'agents': ['response_generator'],
                'max_tokens': 200
            },
            TaskType.GENERAL: {
                'keywords': ['obrigado', 'valeu', 'agradeço', 'tchau', 'até logo'],
                'agents': ['response_generator'],
                'max_tokens': 100
            }
        }

    def _classify_task(self, query: str) -> TaskType:
        """Efficient task classification without LLM"""
        query_lower = query.lower()

        for task_type, rules in self.task_rules.items():
            if any(keyword in query_lower for keyword in rules['keywords']):
                return task_type

        return TaskType.GENERAL

    def _is_follow_up_query(self, query: str, conversation_data: Dict[str, Any]) -> bool:
        """Determine if this is a follow-up query based on context"""
        if not conversation_data:
            return False

        # Check if there's previous product info and the query seems related
        previous_product_info = conversation_data.get("product_info")
        if not previous_product_info:
            return False

        # Enhanced follow-up indicators
        follow_up_indicators = [
            "mais", "detalhes", "outro", "similar", "parecido", "também", "e", "mas",
            "primeiro", "segundo", "terceiro", "próximo", "anterior", "outro produto",
            "mais informações", "mais detalhes", "especificações", "características"
        ]
        query_lower = query.lower()

        # Check for follow-up indicators
        has_follow_up_words = any(indicator in query_lower for indicator in follow_up_indicators)

        # Check if query is very short (likely follow-up)
        is_short_query = len(query.split()) <= 4

        # Check if query references previous context
        has_context_reference = any(word in query_lower for word in ["ele", "ela", "este", "esta", "esse", "essa"])

        # Check if it's a gratitude expression (general follow-up)
        is_gratitude = any(word in query_lower for word in ["obrigado", "valeu", "agradeço", "tchau"])

        return has_follow_up_words or is_short_query or has_context_reference or is_gratitude

    def _check_token_budget(self, required_tokens: int) -> bool:
        """Check if we have token budget available"""
        return (self.current_tokens_used + required_tokens) <= self.max_tokens_per_query

    def _execute_agent_safely(self, agent_name: str, conversation_data: Dict,
                              specialized_query: str = None) -> AgentExecution:
        """Execute agent with token tracking and error handling"""
        start_time = time.time()
        tokens_before = self.current_tokens_used

        print(f"\n[OrchestratorAgent] Executing agent: {agent_name}")
        print(f"[OrchestratorAgent] Conversation data keys: {list(conversation_data.keys())}")

        try:
            if agent_name == 'product_specialist':
                print(f"[DEBUG] ProductSpecialistAgent input - specialized_query: {specialized_query}")
                print(
                    f"[DEBUG] ProductSpecialistAgent input - conversation_data keys: {list(conversation_data.keys())}")
                result = self.product_specialist.process_query(
                    specialized_query,
                    conversation_data['customer_id'],
                    conversation_data  # Pass full conversation data for context
                )
            elif agent_name == 'response_generator':
                print(f"[DEBUG] ResponseGeneratorAgent input - specialized_query: {specialized_query}")
                print(
                    f"[DEBUG] ResponseGeneratorAgent input - conversation_data keys: {list(conversation_data.keys())}")
                print(
                    f"[DEBUG] ResponseGeneratorAgent input - product_info available: {'product_info' in conversation_data}")
                if 'product_info' in conversation_data:
                    print(
                        f"[DEBUG] ResponseGeneratorAgent input - product_info type: {type(conversation_data['product_info'])}")
                    print(
                        f"[DEBUG] ResponseGeneratorAgent input - product_info content: {conversation_data['product_info']}")

                # 🚨 ANTI-HALLUCINATION: Validate product_info completeness
                product_info = conversation_data.get('product_info', [])
                if isinstance(product_info, list) and len(product_info) > 0:
                    # Ensure all products have required fields
                    validated_products = []
                    for item in product_info:
                        product = item.get('product', item)
                        if product.get('name') and product.get('id'):
                            validated_products.append(item)
                        else:
                            print(f"[DEBUG] ⚠️ Filtering incomplete product: {product}")

                    conversation_data['product_info'] = validated_products
                    print(f"[DEBUG] 🛡️ Validated product_info: {len(validated_products)} complete products")

                result = self.response_generator.execute(conversation_data)
            else:
                raise ValueError(f"Unknown agent: {agent_name}")

            print(f"[OrchestratorAgent] Agent {agent_name} returned keys: {list(result.keys())}")
            conversation_data.update(result)

            # Extract product information immediately after update while data is available
            if agent_name == 'product_specialist':
                results = conversation_data.get('results', [])
                status = conversation_data.get('status', '')
                print(f"[DEBUG] Product specialist results: {len(results)} products, status: '{status}'")

                if status == 'no_products_found' or not results:
                    extracted_products = []
                    print(f"[DEBUG] No products found, setting empty list")
                else:
                    extracted_products = [r.get('product', r) for r in results]
                    print(f"[DEBUG] Extracted {len(extracted_products)} products from results")
                    if extracted_products:
                        print(f"[DEBUG] First product name: {extracted_products[0].get('name', 'N/A')}")

                # Store the extracted products in a way that persists
                conversation_data['extracted_product_info'] = extracted_products

            # Improve token tracking - use a more reasonable estimate
            # The previous estimate was too aggressive and causing premature budget exhaustion
            tokens_used = len(conversation_data['query'].split()) * 1  # More conservative estimate
            self.current_tokens_used += tokens_used

            execution_time = time.time() - start_time

            return AgentExecution(
                agent_id=agent_name,
                input_tokens=tokens_used // 2,
                output_tokens=tokens_used // 2,
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            print(f"[OrchestratorAgent] Error executing {agent_name}: {str(e)}")
            execution_time = time.time() - start_time

            return AgentExecution(
                agent_id=agent_name,
                input_tokens=0,
                output_tokens=0,
                execution_time=execution_time,
                success=False
            )

    def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Refactored: single-source-of-truth product context, robust agent chaining, minimal STM bloat."""
        start_time = time.time()
        self.current_tokens_used = 0
        executions = []
        customer_id = input_data.get('customer_id', 'default_customer')
        query = input_data.get('query', '')
        try:
            # 1. Query analysis
            if self.use_llm_analysis:
                analysis_result = self._analyze_query_intelligently(query, customer_id)
            else:
                analysis_result = self._analyze_query_fallback(query, customer_id)
            if 'end_of_conversation' not in analysis_result:
                analysis_result['end_of_conversation'] = False
            self.last_analysis = analysis_result.copy()
            task_type = analysis_result['task_type']
            is_follow_up = analysis_result['is_follow_up']
            required_agents = analysis_result['required_agents']
            reasoning = analysis_result['reasoning']
            analysis_method = analysis_result.get('analysis_method', '')
            # 2. Load STM
            short_term_memory = self.memory_manager.get_short_term_memory(customer_id)
            conversation_data = short_term_memory.get("conversation", {})
            conversation_history = conversation_data.get("conversation_history", [])
            all_products_discussed = conversation_data.get("all_products_discussed", [])
            # 3. Single source of truth: products_in_focus
            products_in_focus = []
            # Use relevant_products from analysis if present
            relevant_products = analysis_result.get('relevant_products', [])
            if relevant_products:
                products_in_focus = [p.get('product', p) for p in relevant_products]
            conversation_data['products_in_focus'] = products_in_focus
            # 4. Prepare new turn
            turn = {
                "turn_id": len(conversation_history) + 1,
                "timestamp": time.time(),
                "user_query": query,
                "agent_response": None,
                "task_type": task_type,
                "agents_executed": [],
                "reasoning": reasoning,
                "products_in_focus": products_in_focus,
                "follow_up": is_follow_up,
                "specialized_queries": analysis_result.get('specialized_queries', {}),
                "analysis_method": analysis_method,
                "end_of_conversation": analysis_result.get('end_of_conversation', False),
            }
            # 5. Update STM with minimal context
            conversation_data.update({
                'customer_id': customer_id,
                'query': query,
                'task_type': task_type,
                'is_follow_up': is_follow_up,
                'analysis_reasoning': reasoning,
                'specialized_queries': analysis_result.get('specialized_queries', {}),
                'product_context': analysis_result.get('product_context', {}),
                'conversation_history': conversation_history,
                'products_in_focus': products_in_focus,
                'last_query': query,
                'last_response': '',
                'last_task_type': task_type,
                'last_agents_executed': [],
                'last_reasoning': reasoning,
                'last_specialized_queries': analysis_result.get('specialized_queries', {}),
                'last_analysis_method': analysis_method,
                'last_end_of_conversation': analysis_result.get('end_of_conversation', False)
            })

            # Store seller_info if available
            if 'seller_info' in analysis_result:
                conversation_data['seller_info'] = analysis_result['seller_info']
                print(
                    f"[DEBUG] Stored seller_info in conversation_data: {analysis_result['seller_info'].get('name', 'Unknown')}")
            # 6. Agent execution chain
            norm_specialized_queries = {k.strip(): v for k, v in analysis_result.get('specialized_queries', {}).items()}
            norm_required_agents = [a.strip() for a in required_agents]
            # For product queries, always execute product_specialist before response_generator

            # results_execute_agent = {}
            # for specialist_agent_name in required_agents:
            #     results_execute_agent[specialist_agent_name] = self._specialists_agents[specialist_agent_name].execute(analysis_result)


            if task_type == 'product_query':
                if 'product_specialist' in norm_required_agents and 'response_generator' in norm_required_agents:
                    norm_required_agents = ['product_specialist', 'response_generator']
            for agent_name in norm_required_agents:
                # Build agent_input_data from the latest context after each agent
                agent_input_data = {
                    'customer_id': customer_id,
                    'query': query,
                    'products_in_focus': conversation_data.get('products_in_focus', []),
                    'relevant_products': conversation_data.get('products_in_focus', []),
                    'specialized_query': norm_specialized_queries.get(agent_name),
                    'is_follow_up': is_follow_up,
                    'product_context': analysis_result.get('product_context', {}),
                    'all_products_discussed': all_products_discussed,
                    'conversation_history': conversation_history,
                }
                if 'product_info' in conversation_data:
                    agent_input_data['product_info'] = conversation_data['product_info']
                if 'extracted_product_info' in conversation_data:
                    agent_input_data['extracted_product_info'] = conversation_data['extracted_product_info']
                # Pass seller info to response generator when available
                if agent_name == 'response_generator':
                    # Check both analysis_result and conversation_data for seller_info
                    seller_info = analysis_result.get('seller_info') or conversation_data.get('seller_info')
                    print(f"[DEBUG] Looking for seller_info - analysis_result keys: {list(analysis_result.keys())}")
                    print(f"[DEBUG] Looking for seller_info - conversation_data keys: {list(conversation_data.keys())}")
                    if seller_info:
                        agent_input_data['seller_info'] = seller_info
                        print(
                            f"[DEBUG] Passing seller info to response generator: {seller_info.get('name', 'Unknown')}")
                    else:
                        print(f"[DEBUG] No seller info found in analysis_result or conversation_data")
                execution = self._execute_agent_safely(
                    agent_name,
                    agent_input_data,
                    norm_specialized_queries.get(agent_name)
                )
                executions.append(execution)
                turn['agents_executed'].append(agent_name)
                # After agent execution, update products_in_focus and product_info if new products were returned
                if agent_name == 'product_specialist':
                    # Use the extracted product info that was stored during agent execution
                    extracted_products = agent_input_data.get('extracted_product_info', [])
                    print(f"[DEBUG] Using extracted product info: {len(extracted_products)} products")

                    if extracted_products:
                        products_in_focus = extracted_products
                        conversation_data['product_info'] = extracted_products
                        print(f"[DEBUG] Set product_info with {len(extracted_products)} products")
                        print(f"[DEBUG] First product name: {extracted_products[0].get('name', 'N/A')}")
                    else:
                        products_in_focus = []
                        conversation_data['product_info'] = []
                        print(f"[DEBUG] No products extracted, setting empty lists")

                    conversation_data['products_in_focus'] = products_in_focus
                if agent_name == 'response_generator' and execution.success:
                    turn['agent_response'] = agent_input_data.get('final_response', None)
                    conversation_data['final_response'] = agent_input_data.get('final_response', '')
            # 7. Update STM for next turn
            conversation_history.append(turn)
            conversation_data['conversation_history'] = conversation_history[-5:]
            conversation_data['products_in_focus'] = products_in_focus
            # 8. Final response
            final_response = conversation_data.get('final_response',
                                                   'Desculpe, não consegui processar sua solicitação.')
            # 9. Save STM
            self.memory_manager.save_short_term_memory(customer_id, conversation_data)
            return {
                'success': True,
                'final_response': final_response,
                'task_type': analysis_result.get('task_type', 'unknown'),
                'is_follow_up': is_follow_up,
                'agents_executed': norm_required_agents,
                'tokens_used': self.current_tokens_used,
                'tokens_budget': self.max_tokens_per_query,
                'execution_time': time.time() - start_time,
                'reasoning': analysis_result.get('reasoning', ''),
                'customer_id': customer_id
            }
        except Exception as e:
            return {
                'final_response': "Erro interno. Tente novamente.",
                'customer_id': customer_id,
                'task_type': 'error',
                'error': str(e),
                'tokens_used': self.current_tokens_used,
                'agent_id': self.id,
                'success': False
            }

    def _create_budget_exceeded_response(self, customer_id: str) -> Dict[str, Any]:
        """Response when token budget is exceeded"""
        return {
            'final_response': "Sua pergunta é muito complexa. Tente ser mais específico.",
            'customer_id': customer_id,
            'task_type': 'budget_exceeded',
            'tokens_used': 0,
            'tokens_budget': self.max_tokens_per_query,
            'agent_id': self.id,
            'success': True
        }

    def _analyze_query_intelligently(self, query: str, customer_id: str) -> Dict[str, Any]:
        """Use QueryAgent to intelligently analyze the query with memory context"""
        try:
            print(f"\n[OrchestratorAgent] Using LLM-based query analysis...")
            analysis = self.query_agent.analyze_query(query,
                                                      customer_id,
                                                      self.memory_manager,
                                                      self._specialists_agents)

            # Validate and normalize the analysis
            task_type = analysis.get('task_type', 'general')
            is_follow_up = analysis.get('is_follow_up', False)
            required_agents = analysis.get('required_agents', ['response_generator'])
            reasoning = analysis.get('reasoning', 'No reasoning provided')
            context_notes = analysis.get('context_notes', 'No context notes')
            specialized_queries = analysis.get('specialized_queries', {})
            product_context = analysis.get('product_context', {})
            end_of_conversation = analysis.get('end_of_conversation', False)

            print(f"[OrchestratorAgent] LLM Analysis Results:")
            print(f"  - Task Type: {task_type}")
            print(f"  - Is Follow-up: {is_follow_up}")
            print(f"  - Required Agents: {required_agents}")
            print(f"  - Reasoning: {reasoning}")
            print(f"  - Context Notes: {context_notes}")

            # Preserve all fields from analysis, including seller_info
            result = {
                'task_type': task_type,
                'is_follow_up': is_follow_up,
                'required_agents': required_agents,
                'reasoning': reasoning,
                'context_notes': context_notes,
                'specialized_queries': specialized_queries,
                'product_context': product_context,
                'analysis_method': 'llm',
                'end_of_conversation': end_of_conversation
            }

            # Preserve seller_info if present
            if 'seller_info' in analysis:
                result['seller_info'] = analysis['seller_info']
                print(f"[OrchestratorAgent] Preserved seller_info: {analysis['seller_info'].get('name', 'Unknown')}")

            return result

        except Exception as e:
            print(f"[OrchestratorAgent] LLM analysis failed: {e}")
            print(f"[OrchestratorAgent] Falling back to rule-based analysis...")
            return self._analyze_query_fallback(query, customer_id)

    def _analyze_query_fallback(self, query: str, customer_id: str) -> Dict[str, Any]:
        """Fallback to rule-based analysis when LLM fails"""
        task_type = self._classify_task(query)
        is_follow_up = self._is_follow_up_query(query, {})  # Empty context for fallback
        required_agents = self.task_rules[task_type]['agents']
        # Generate fallback specialized queries and product context
        specialized_queries = {}
        product_context = {}
        # Try to mimic QueryAgent fallback
        if hasattr(self, 'query_agent'):
            try:
                fallback = self.query_agent._fallback_analysis(query, {})
                specialized_queries = fallback.get('specialized_queries', {})
                product_context = fallback.get('product_context', {})
            except Exception:
                pass
        return {
            'task_type': task_type.value,
            'is_follow_up': is_follow_up,
            'required_agents': required_agents,
            'reasoning': f'Fallback: Rule-based classification using keywords',
            'context_notes': 'Analysis based on hardcoded rules',
            'specialized_queries': specialized_queries,
            'product_context': product_context,
            'analysis_method': 'fallback'
        }
