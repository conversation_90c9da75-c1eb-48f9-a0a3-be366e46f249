import json
from typing import Dict, Any, List
from .base_agent import BaseAgent
import os
from datetime import datetime
import re

class ResponseGeneratorAgent(BaseAgent):
    def __init__(self, config: Dict[str, Any], llm, memory_manager=None):
        super().__init__(config, llm, memory_manager=memory_manager)
        # Load the product list threshold from config
        self.max_products_for_llm = config.get('summary_settings', {}).get('max_products_for_llm', 5)

    def _preparse_product_list(self, product_info):
        """Extract key fields from a large product list for LLM readability."""
        parsed = []
        for idx, item in enumerate(product_info, 1):
            prod = item.get('product', {})
            entry = {
                'nome': prod.get('name', 'N/A'),
                'categoria': prod.get('category', 'N/A'),
                'preco': prod.get('price', 'N/A'),
                'descricao': prod.get('description', ''),
            }
            # Optionally include specs if present
            if prod.get('specifications'):
                entry['especificacoes'] = prod['specifications']
            parsed.append(entry)
        return parsed

    def _extract_product_names_from_data(self, product_info) -> List[str]:
        """Extract all product names from the provided product data."""
        product_names = []
        if isinstance(product_info, list):
            for item in product_info:
                if isinstance(item, dict):
                    # Handle both direct product objects and nested product objects
                    product = item.get('product', item)
                    name = product.get('name', '')
                    if name:
                        product_names.append(name.lower())
        return product_names

    def _validate_response_against_data(self, response: str, product_info) -> tuple[bool, List[str]]:
        """
        Validate that the response only mentions products and prices from the provided data.
        Returns (is_valid, list_of_violations)
        """
        if not product_info:
            return True, []

        violations = []
        response_lower = response.lower()

        # Extract valid product names and prices from provided data
        valid_product_names = self._extract_product_names_from_data(product_info)
        valid_prices = set()
        valid_brands = set()

        for item in product_info:
            product = item.get('product', item)

            # Collect valid prices
            price = product.get('price')
            if price:
                # Store multiple price formats for validation
                valid_prices.add(f"{price:.2f}")
                valid_prices.add(f"{price:.1f}")
                valid_prices.add(f"{int(price)}")
                valid_prices.add(f"{price:,.2f}")
                valid_prices.add(f"{price:,.0f}")

            # Collect valid brands
            brand = product.get('brand', '')
            if brand:
                valid_brands.add(brand.lower())

        # 1. Check for price patterns that might be hallucinated
        price_patterns = re.findall(r'r\$\s*[\d,]+\.?\d*', response_lower)
        for price_match in price_patterns:
            # Extract numeric value from price
            price_numeric = re.sub(r'[^\d,.]', '', price_match)
            price_numeric = price_numeric.replace(',', '').replace('.', '')

            try:
                # Handle different price formats (1800, 1.800, 7999, 7.999, etc.)
                if len(price_numeric) >= 3:
                    # Try as integer first (1800, 7999)
                    price_value = float(price_numeric)

                    # Also try with decimal point in different positions
                    price_variants = [price_value]
                    if len(price_numeric) >= 4:
                        # Try with decimal point (1800 -> 1.800, 7999 -> 7.999)
                        decimal_pos = len(price_numeric) - 3
                        price_with_decimal = float(price_numeric[:decimal_pos] + '.' + price_numeric[decimal_pos:])
                        price_variants.append(price_with_decimal)

                    # Check if any variant exists in our data
                    price_found = False
                    for variant in price_variants:
                        for valid_price_str in valid_prices:
                            try:
                                valid_price_val = float(valid_price_str.replace(',', ''))
                                if abs(variant - valid_price_val) < 0.01:  # Allow for small rounding differences
                                    price_found = True
                                    break
                            except ValueError:
                                continue
                        if price_found:
                            break

                    if not price_found:
                        violations.append(f"Mentioned price '{price_match}' which is not in provided data")
                else:
                    violations.append(f"Invalid price format '{price_match}'")
            except ValueError:
                violations.append(f"Invalid price format '{price_match}'")

        # 2. Check for specific product names that don't exist in our data
        # Look for patterns like "Product Name (ID: XXX)" which are clear product references
        product_mention_patterns = [
            r'([a-zA-Z][a-zA-Z0-9\s]+)\s*\(id:\s*\d+\)',  # Product Name (ID: 123)
        ]

        for pattern in product_mention_patterns:
            matches = re.findall(pattern, response_lower, re.IGNORECASE)
            for match in matches:
                product_name = match.strip()
                if len(product_name) > 3:  # Avoid false positives with short words
                    # Check if this product name exists in our data
                    name_found = any(product_name in valid_name for valid_name in valid_product_names)
                    if not name_found:
                        violations.append(f"Mentioned product '{product_name}' which is not in provided data")

        # 3. Check for brand mentions that don't exist in our data
        for brand in valid_brands:
            if brand in response_lower:
                # This is OK - brand exists in our data
                continue

        # Look for suspicious product mentions that are commonly hallucinated
        # Focus on complete product names that are frequently invented
        suspicious_patterns = [
            r'\bipad\s+air\b',
            r'\bipad\s+mini\b',
            r'\bdell\s+xps\s+15\b',
            r'\bhp\s+envy\s+15\b',
            r'\bthinkpad\s+p53\b',
            r'\bmsi\s+gs66\s+stealth\b',
            r'\basus\s+rog\b',
            r'\bacer\s+predator\b',
            r'\balienware\b',
            r'\bsurface\s+pro\b(?!\s+9)',  # Allow Surface Pro 9 but not generic Surface Pro
        ]

        for pattern in suspicious_patterns:
            if re.search(pattern, response_lower):
                # Check if this specific product exists in our data
                pattern_found = False
                for valid_name in valid_product_names:
                    if re.search(pattern, valid_name):
                        pattern_found = True
                        break

                if not pattern_found:
                    match = re.search(pattern, response_lower)
                    if match:
                        violations.append(f"Mentioned product '{match.group()}' which is not in provided data")

        return len(violations) == 0, violations

    def _create_safe_response(self, query: str, product_info) -> str:
        """Create a safe response when validation fails or no data is available."""
        if not product_info:
            return "Desculpe, não tenho informações sobre produtos disponíveis no momento. Como posso ajudá-lo de outra forma?"

        # Extract available product information
        available_products = []
        categories = set()

        for item in product_info:
            product = item.get('product', item)
            name = product.get('name', '')
            category = product.get('category', '')
            price = product.get('price')

            if name:
                product_info_str = name
                if price:
                    product_info_str += f" - R$ {price:,.2f}"
                available_products.append(product_info_str)

            if category:
                categories.add(category)

        # Analyze query to provide contextual response
        query_lower = query.lower()

        if available_products:
            if len(available_products) == 1:
                return f"Com base nos produtos disponíveis, temos: {available_products[0]}. Gostaria de saber mais detalhes?"
            elif len(available_products) <= 3:
                products_list = ', '.join(available_products[:-1]) + f" e {available_products[-1]}"
                return f"Com base nos produtos disponíveis, temos: {products_list}. Sobre qual gostaria de saber mais?"
            else:
                # For larger lists, group by category if possible
                if categories:
                    categories_list = ', '.join(sorted(categories))
                    return f"Temos {len(available_products)} produtos disponíveis nas categorias: {categories_list}. Poderia especificar que tipo de produto está procurando?"
                else:
                    return f"Temos {len(available_products)} produtos disponíveis. Poderia especificar que tipo de produto está procurando?"
        else:
            return "Desculpe, não encontrei produtos correspondentes à sua consulta nos dados disponíveis. Poderia reformular sua pergunta?"

    def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final customer response based on parsed input and task type."""
        # Parse all relevant input data
        query = input_data.get('specialized_query', '')
        product_info = input_data.get('product_info', '')
        seller_info = input_data.get('seller_info', None)
        context_notes = input_data.get('context_notes', '')
        task_type = input_data.get('task_type', '')
        is_follow_up = input_data.get('is_follow_up', False)
        customer_id = input_data.get('customer_id', '')
        conversation_history = input_data.get('conversation_history', [])
        all_products_discussed = input_data.get('all_products_discussed', [])
        last_product_list = input_data.get('last_product_list', [])
        products_in_focus = input_data.get('products_in_focus', [])

        print(f"[ResponseGeneratorAgent] Task type: {task_type}")
        print(f"[ResponseGeneratorAgent] Is follow-up: {is_follow_up}")
        print(f"[ResponseGeneratorAgent] Specialized Query: {query}")
        print(f"[ResponseGeneratorAgent] Product info: {type(product_info)}")
        print(f"[ResponseGeneratorAgent] Seller info: {type(seller_info)}")
        print(f"[ResponseGeneratorAgent] Context notes: {context_notes}")

        prompt = f"""
                    {self.system_prompt}

                    QUERY: {query}
                    """

        # Add conversation history to prompt
        if conversation_history:
            prompt += "\nHISTÓRICO DA CONVERSA (últimos 5 turnos):\n"
            for turn in conversation_history[-5:]:
                prompt += f"  [{turn.get('turn_id')}] Usuário: {turn.get('user_query')} | Bot: {turn.get('agent_response')}\n"
        # Add products in focus for this turn
        if products_in_focus:
            prompt += "\nPRODUTOS EM FOCO PARA ESTA CONSULTA:\n"
            for prod in products_in_focus:
                if 'product' in prod:
                    p = prod['product']
                else:
                    p = prod
                prompt += f"  - {p.get('name', 'N/A')} (ID: {p.get('id', 'N/A')})\n"
        # Add all products discussed (for reference)
        if all_products_discussed:
            prompt += "\nTODOS OS PRODUTOS DISCUTIDOS NA SESSÃO:\n"
            for prod in all_products_discussed:
                if 'product' in prod:
                    p = prod['product']
                else:
                    p = prod
                prompt += f"  - {p.get('name', 'N/A')} (ID: {p.get('id', 'N/A')})\n"
        # Add last product list
        if last_product_list:
            prompt += "\nÚLTIMA LISTA DE PRODUTOS APRESENTADA:\n"
            for i, prod in enumerate(last_product_list, 1):
                p = prod.get('product', {})
                prompt += f"  {i}. {p.get('name', 'N/A')} (ID: {p.get('id', 'N/A')})\n"
        # If the query refers to ordinal (e.g., 'segundo', 'terceiro'), resolve using last_product_list
        import re
        ordinal_map = {'primeiro': 0, 'segundo': 1, 'terceiro': 2}
        for word, idx in ordinal_map.items():
            if word in query.lower() and last_product_list and len(last_product_list) > idx:
                product = last_product_list[idx].get('product', {})
                prompt += f"\nDETALHES DO PRODUTO REFERENCIADO ('{word}'):\n{json.dumps(product, ensure_ascii=False, indent=2)}\n"

        # 1. Store info: strictly use received data
        if task_type == 'store_info' and seller_info:
            seller_summary = "\nINFORMAÇÕES DA LOJA:\n"
            try:
                if isinstance(seller_info, dict):
                    seller_summary += json.dumps(seller_info, ensure_ascii=False, indent=2)
                else:
                    seller_summary += str(seller_info)
            except Exception as e:
                seller_summary += f"[Erro ao processar seller_info: {e}]"

            prompt += f""" 
                    Aqui o sumário de informações sobre a loja
                    
                    {seller_summary}

                    Use apenas as informações acima para responder à pergunta do cliente sobre a loja. 
                    """
            
            print(f"[ResponseGeneratorAgent] Using strict SELLER INFO prompt.")


        # 2. Initial product query: let LLM select best matches from list
        elif task_type == 'product_query' and not is_follow_up:
            if isinstance(product_info, list) and product_info:
                if len(product_info) > self.max_products_for_llm:
                    # Pre-parse for large lists
                    parsed_list = self._preparse_product_list(product_info)

                product_list_str = "\nLISTA DE PRODUTOS:\n" + json.dumps(product_info, ensure_ascii=False, indent=2)
            
            elif isinstance(product_info, str) and product_info:
                product_list_str = product_info
            else:
                product_list_str = "Nenhum produto relevante encontrado."
            
            prompt += f"""
                    {product_list_str}

                    Esta é uma consulta de inicial sobre produtos.
                    Com base na lista acima, selecione e apresente os produtos mais relevantes para a consulta do cliente,
                    apresente APENAS os produtos que se relacionem com a QUERY.
                    Use apenas informações básicas sobre os produtos.                    
                    """
            print(f"[ResponseGeneratorAgent] Using PRODUCT LIST selection prompt.")


        # 3. Product follow-up: use LLM to select the relevant product(s) from the list, keeping all details
        elif task_type == 'product_query' and is_follow_up:
            if isinstance(product_info, list) and product_info:
                if len(product_info) > self.max_products_for_llm:
                    # Pre-parse for large lists, but keep all details/specs
                    parsed_list = self._preparse_product_list(product_info)
                
                # Inject raw list with all details
                product_list_str = "\nLISTA DE PRODUTOS:\n" + json.dumps(product_info, ensure_ascii=False, indent=2)
                
                prompt += f"""
                    DETALHES DO PRODUTO:
                    {product_list_str}

                    Esta é uma consulta de acompanhamento (follow-up). 
                    Use a lista acima e o contexto da consulta para selecionar e apresentar APENAS o(s) produto(s) mais relevante(s) de acordo com a QUERY. 
                    Devolva a resposta com o máximo de detalhes disponíveis sobre o(s) produtos(s), conforme recebidos. 
                    Não use tabelas de comparação.
                    Não use tabelas na resposta em nenhum caso, nem se proponha a retornar tabelas.
                    Mantenha-se fiel à QUERY e aos detalhes disponíveis do produto para formular a resposta.
                    """
            elif isinstance(product_info, dict) and product_info:
                # Present all details for the single product
                details = []
                prod = product_info.get('product', product_info)
                if prod.get('name'):
                    details.append(f"Nome: {prod.get('name')}")
                if prod.get('category'):
                    details.append(f"Categoria: {prod.get('category')}")
                if prod.get('price'):
                    details.append(f"Preço: R$ {prod.get('price')}")
                if prod.get('description'):
                    details.append(f"Descrição: {prod.get('description')}")
                if prod.get('specifications'):
                    specs = prod['specifications']
                    if isinstance(specs, dict):
                        for k, v in specs.items():
                            if v:
                                details.append(f"{k}: {v}")
                    elif isinstance(specs, list):
                        for v in specs:
                            if v:
                                details.append(str(v))
                if details:
                    product_details = "\n".join(details)
                    prompt += f"""
                            DETALHES DO PRODUTO:
                            {product_details}

                            Esta é uma consulta de acompanhamento (follow-up). 
                            Use a lista acima e o contexto da consulta para selecionar e apresentar APENAS o(s) produto(s) mais relevante(s) de acordo com a QUERY. 
                            Devolva a resposta com o máximo de detalhes disponíveis sobre o(s) produtos(s). 
                            Não use tabelas na resposta em nenhum caso, nem se proponha a retornar tabelas.
                            Mantenha-se fiel à QUERY e aos detalhes disponíveis do produto para formular a resposta.
                            """
                else:
                    prompt += f"""
                            Esta é uma consulta de acompanhamento (follow-up).
                            Nenhuma informação detalhada do produto está disponível.
                            Informe dificuldades de processamento e solicite gentilmente ao cliente que refaça a pergunta com maiores detalhes.
                            """
            else:
                prompt += f"""
                        Esta é uma consulta de acompanhamento (follow-up).
                        Nenhuma informação detalhada do produto está disponível.
                        Informe dificuldades de processamento e solicite gentilmente ao cliente que refaça a pergunta com maiores detalhes.
                        """
            print(f"[ResponseGeneratorAgent] Using PRODUCT FOLLOW-UP LLM selection prompt.")


        # 4. Unexpected/other task types: try to answer, flag to specialist if needed
        else:
            # Gather all context for the LLM
            context_str = f"Contexto: {context_notes}\n"
            if seller_info:
                context_str += f"Informações da loja: {json.dumps(seller_info, ensure_ascii=False)}\n"
            if product_info:
                context_str += f"Informações de produtos: {json.dumps(product_info, ensure_ascii=False)}\n"
            prompt += f"""
                    CONTEXTO
                    {context_str}

                    Tente responder à pergunta do cliente com base nas informações acima.
                    """
            print(f"[ResponseGeneratorAgent] Using fallback/other prompt.")

        prompt += f"""
                    Seja claro, conciso e objetivo.
                """

        # Log the prompt for debugging
        if not customer_id:
            print("[ResponseGeneratorAgent] WARNING: customer_id not set for logging, using 'unknown_customer'.")
            customer_id = "unknown_customer"
        log_dir = os.path.join(os.path.dirname(__file__), f'../../logs/{customer_id}')
        os.makedirs(log_dir, exist_ok=True)
        log_path = os.path.join(log_dir, 'response_generator_prompts.log')
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(f"\n---\n[{datetime.now().isoformat()}] customer_id={customer_id} | query={query}\nPROMPT:\n{prompt}\n")
        response = self._call_llm(prompt, customer_id=customer_id)

        # 🚨 ANTI-HALLUCINATION VALIDATION
        is_valid, violations = self._validate_response_against_data(response, product_info)

        if not is_valid:
            print(f"[ResponseGeneratorAgent] ⚠️ HALLUCINATION DETECTED: {violations}")
            with open(log_path, 'a', encoding='utf-8') as f:
                f.write(f"⚠️ HALLUCINATION DETECTED: {violations}\n")
                f.write(f"ORIGINAL RESPONSE:\n{response}\n")

            # Generate safe response instead
            response = self._create_safe_response(query, product_info)
            print(f"[ResponseGeneratorAgent] 🛡️ Using safe response: {response}")
            with open(log_path, 'a', encoding='utf-8') as f:
                f.write(f"🛡️ SAFE RESPONSE:\n{response}\n")
        else:
            print(f"[ResponseGeneratorAgent] ✅ Response validation passed")
            with open(log_path, 'a', encoding='utf-8') as f:
                f.write(f"✅ VALIDATION PASSED\n")

        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(f"FINAL RESPONSE:\n{response}\n")
        print(f"[ResponseGeneratorAgent] Final response: {response}")

        return {
            'final_response': response,
            'customer_id': customer_id,
            'agent_id': self.id,
            'processed_query': query,
            'validation_passed': is_valid,
            'violations_detected': violations if not is_valid else []
        }

