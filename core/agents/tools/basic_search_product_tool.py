from typing import Dict, Any

from api.db_session import get_db_session
from api.service.product_service import ProductService
from core.agents.tools.product_tool import ProductTool


class BasicSearchProductTool(ProductTool):
    """Tool for basic product search using fuzzy matching."""

    def __init__(self, agent_instance):
        super().__init__(
            name="basic_product_search",
            description="Search for products using fuzzy matching and tag-based filtering. Returns basic product information.",
            agent_instance=agent_instance
        )

    def execute(self, query: str, category: str = None, max_results: int = 5) -> Dict[str, Any]:
        """Execute basic product search."""
        # start_time = time.time()

        try:
            # Use existing search logic from current implementation
            # results = self.agent._search_products_basic(query, category, max_results)

            products = []
            with get_db_session() as db:
                products = ProductService(db).find_nominal(query)

            if not products:
                with get_db_session() as db:
                    products = ProductService(db).find_idnominal_classificacao(query)

            output = {
                "success": True,
                "results": products,
                "count": len(products),
                "query": query,
                "category": category
            }

        except Exception as e:
            output = {
                "success": False,
                "error": str(e),
                "results": [],
                "count": 0
            }

        # execution_time = time.time() - start_time
        # self._log_execution({"query": query, "category": category}, output, execution_time)

        return output

    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "Search query from user"},
                "category": {"type": "string", "description": "Optional category filter"},
                "max_results": {"type": "integer", "description": "Maximum number of results", "default": 5}
            },
            "required": ["query"]
        }
