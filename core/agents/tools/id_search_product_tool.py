from typing import Dict, Any, Optional

from api.db_session import get_db_session
from api.domain.product import Product
from api.service.product_service import ProductService
from core.agents.tools.product_tool import ProductTool


class IdSearchProductTool(ProductTool):
    """Tool for retrieving specific product by ID."""

    def __init__(self, agent_instance):
        super().__init__(
            name="get_product_by_id",
            description="Retrieve specific product information by product ID.",
            agent_instance=agent_instance
        )

    def _get_product_by_id(self, product_id: str, detail_level: str = "full") -> Optional[Dict]:
        """Get product by ID using existing logic."""


    def execute(self, product_id: str, detail_level: str = "full") -> Dict[str, Any]:

        try:
            product: Product
            with get_db_session() as db:
                product = ProductService(db).get_by_id(product_id)

            output = {
                "success": True,
                "product": product.copy(),
                "product_id": product_id
            }

        except Exception as e:
            output = {
                "success": False,
                "error": str(e),
                "product": None
            }

        # execution_time = time.time() - start_time
        # self._log_execution({"product_id": product_id, "detail_level": detail_level}, output, execution_time)

        return output

    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "product_id": {"type": "string", "description": "Unique product identifier"},
                "detail_level": {"type": "string", "enum": ["basic", "detailed", "full"], "default": "full"}
            },
            "required": ["product_id"]
        }
