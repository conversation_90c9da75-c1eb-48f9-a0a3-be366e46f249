import time
from typing import Dict, Any, List

from core.agents.tools.basic_search_product_tool import BasicSearchProductTool
from core.agents.tools.product_tool import ProductTool


class DetailedProductSearchTool(ProductTool):
    """Tool for detailed product search with comprehensive information."""

    def __init__(self, agent_instance):
        super().__init__(
            name="detailed_product_search",
            description="Search for products with comprehensive details and specifications.",
            agent_instance=agent_instance
        )

    def _search_products_detailed(self, query: str, detail_level: str = "detailed") -> List[Dict]:
        """Detailed product search using existing logic."""
        # Use basic search but return more detailed information
        basic_search_product_tool = BasicSearchProductTool(self.agent)
        basic_results = basic_search_product_tool._search_products_basic(query, None, 5)

        return basic_results

    def execute(self, query: str, detail_level: str = "detailed") -> Dict[str, Any]:
        """Execute detailed product search."""
        start_time = time.time()

        try:
            # Use existing detailed search logic
            results = self.agent._search_products_detailed(query, detail_level)

            output = {
                "success": True,
                "results": results,
                "count": len(results),
                "detail_level": detail_level
            }

        except Exception as e:
            output = {
                "success": False,
                "error": str(e),
                "results": []
            }

        execution_time = time.time() - start_time
        self._log_execution({"query": query, "detail_level": detail_level}, output, execution_time)

        return output

    def _get_parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "query": {"type": "string", "description": "Search query from user"},
                "detail_level": {"type": "string", "enum": ["basic", "detailed", "full"], "default": "detailed"}
            },
            "required": ["query"]
        }
