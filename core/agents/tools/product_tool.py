import time
from abc import ABC, abstractmethod
from typing import Dict, Any


class ProductTool(ABC):
    """Base class for all product-related tools."""

    def __init__(self, name: str, description: str, agent_instance=None):
        self.name = name
        self.description = description
        self.agent = agent_instance
        self.execution_count = 0
        self.total_execution_time = 0.0

    @abstractmethod
    def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the tool with given parameters."""
        pass

    def get_schema(self) -> Dict[str, Any]:
        """Return the tool's parameter schema."""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": self._get_parameters_schema()
        }

    @abstractmethod
    def _get_parameters_schema(self) -> Dict[str, Any]:
        """Return the parameters schema for this tool."""
        pass

    def _log_execution(self, input_params: dict, output: dict, execution_time: float):
        """Log tool execution for debugging and analytics."""
        self.execution_count += 1
        self.total_execution_time += execution_time

        if self.agent and hasattr(self.agent, '_log_tool_usage'):
            self.agent._log_tool_usage(self.name, input_params, output, execution_time)
