import json
from typing import Dict, Any, List
from .base_agent import BaseAgent
from core.utils.llm_manager import LlmManager
from core.utils.config_loader import Config<PERSON>oader
import os
from langchain.tools import StructuredTool
from pydantic import BaseModel, Field
from langchain.agents import create_react_agent, AgentExecutor
from langchain.prompts import PromptTemplate
from langchain.agents import AgentType
from langchain.memory import ConversationTokenBufferMemory
import unicodedata
import re

# --- Tool Input Schemas ---
class IntentEntityInput(BaseModel):
    query: str = Field(description="Consulta do usuário para extração de intenção e entidades.")

class QueryBuilderInput(BaseModel):
    entities_json: str = Field(description="Entidades extraídas em JSON.")

class ProductSearchInput(BaseModel):
    entities_json: str = Field(description="Entidades extraídas em JSON para busca de produtos.")

class ResponseManagerInput(BaseModel):
    input: str = Field(description="JSON string containing products_json and user_query.")

# --- Tool Classes ---
class IntentEntityExtractor:
    def __init__(self, llm, intents, entity_schema, categories=None):
        self.llm = llm
        self.intents = intents
        self.entity_schema = entity_schema
        self.categories = categories or []


    def _build_prompt(self, query: str) -> str:
        return f"""
Query: '{query}'
Extract intent from: {self.intents}
Extract entities from: {self.entity_schema}
For 'categoria' use only: {self.categories}
Extract other fields only if present in query.

Return JSON:
{{
  "intencao": "...",
  "entidades": {{
    "categoria": "...",
    "marca": "...",
    ...
  }}
}}

Example:
{{
  "intencao": "buscar_por_categoria",
  "entidades": {{
    "categoria": "notebook",
    "marca": "dell"
  }}
}}
"""

    def extract(self, query: str) -> dict:
        prompt = self._build_prompt(query)
        response = self.llm.invoke([{'role': 'user', 'content': prompt}])
        print(f"[DEBUG][IntentEntityExtractor] Raw LLM response: {response.content if hasattr(response, 'content') else response}")
        
        # Handle empty or invalid responses
        if not response or (hasattr(response, 'content') and not response.content):
            print(f"[WARNING][IntentEntityExtractor] Empty LLM response, using fallback")
            return self._fallback_extraction(query)
        
        try:
            # Try to get the content from the response
            content = response.content if hasattr(response, 'content') else str(response)
            
            # Clean up the content - remove any extra text before/after JSON
            content = content.strip()
            
            # Try to find JSON in the response
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                content = json_match.group(0)
            
            result = json.loads(content)
            print(f"[DEBUG][IntentEntityExtractor] Parsed result: {result}")
            return result
        except Exception as e:
            print(f"[ERROR][IntentEntityExtractor] Failed to parse LLM response as JSON: {e}")
            print(f"[ERROR][IntentEntityExtractor] Raw content: {content if 'content' in locals() else 'No content'}")
            return self._fallback_extraction(query)

    def _fallback_extraction(self, query: str) -> dict:
        """Fallback extraction when LLM fails - extract basic category and intent."""
        print(f"[DEBUG][IntentEntityExtractor] Using fallback extraction for query: {query}")
        
        # Simple keyword-based fallback
        query_lower = query.lower()
        
        # Extract category
        category = None
        for cat in self.categories:
            if cat.lower() in query_lower:
                category = cat
                break
        
        # Extract basic intent
        intent = "buscar_por_categoria"  # Default intent
        
        # Build basic entities
        entities = {}
        if category:
            entities["categoria"] = category
        
        # Try to extract other common entities
        if "marca" in query_lower or "marcas" in query_lower:
            entities["marca"] = "qualquer"
        if "preço" in query_lower or "preco" in query_lower or "valor" in query_lower:
            entities["preco"] = "qualquer"
        if "memória" in query_lower or "memoria" in query_lower:
            entities["memoria"] = "qualquer"
        
        result = {
            "intencao": intent,
            "entidades": entities
        }
        
        print(f"[DEBUG][IntentEntityExtractor] Fallback result: {result}")
        return result

    def tool(self, query: str) -> str:
        result = self.extract(query)
        print(f"[DEBUG][IntentEntityExtractor] tool() returning: {result}")
        return json.dumps(result, ensure_ascii=False)

class ProductQueryBuilder:
    def __init__(self, field_map, llm):
        self.field_map = field_map
        self.llm = llm

    def _generate_synonyms_with_llm(self, user_term: str, available_fields: list) -> list:
        """Use LLM to generate synonyms for user terms based on available product fields."""
        prompt = f"""
Term: "{user_term}"
Fields: {available_fields}

Map term to product fields. Return JSON array only.

Examples:
"memória" → ["storage", "ram"]
"tela" → ["screen"]
"preço" → ["price"]

Response: ["field1", "field2"]
"""
        try:
            response = self.llm.invoke([{'role': 'user', 'content': prompt}])
            
            # Handle empty responses
            if not response or (hasattr(response, 'content') and not response.content):
                print(f"[WARNING][ProductQueryBuilder] Empty LLM response for synonym generation of '{user_term}', using fallback")
                return self._fallback_synonyms(user_term, available_fields)
            
            result = response.content.strip() if hasattr(response, 'content') else str(response).strip()
            print(f"[DEBUG][ProductQueryBuilder] LLM synonym response for '{user_term}': {result}")
            
            # Try to find JSON in the response
            json_match = re.search(r'\[.*\]', result, re.DOTALL)
            if json_match:
                result = json_match.group(0)
            
            # Parse JSON response
            synonyms = json.loads(result)
            if isinstance(synonyms, list):
                return synonyms
            else:
                print(f"[WARNING][ProductQueryBuilder] LLM returned non-list for '{user_term}', using fallback")
                return self._fallback_synonyms(user_term, available_fields)
        except Exception as e:
            print(f"[ERROR][ProductQueryBuilder] Failed to generate synonyms for '{user_term}': {e}")
            return self._fallback_synonyms(user_term, available_fields)

    def _fallback_synonyms(self, user_term: str, available_fields: list) -> list:
        """Fallback synonym mapping when LLM fails."""
        # Simple keyword-based fallback mapping
        fallback_map = {
            "memória": ["storage", "ram"],
            "memoria": ["storage", "ram"],
            "armazenamento": ["storage"],
            "ram": ["ram"],
            "tela": ["screen"],
            "display": ["screen"],
            "câmera": ["camera"],
            "camera": ["camera"],
            "bateria": ["battery"],
            "preço": ["price"],
            "preco": ["price"],
            "marca": ["brand"],
            "modelo": ["model"],
            "cor": ["colors"],
            "cores": ["colors"],
            "garantia": ["warranty"],
            "disponível": ["in_stock"],
            "disponivel": ["in_stock"],
            "quantidade": ["quantity"]
        }
        
        user_term_lower = user_term.lower()
        if user_term_lower in fallback_map:
            synonyms = fallback_map[user_term_lower]
            # Filter to only include fields that actually exist
            valid_synonyms = [s for s in synonyms if s in available_fields]
            print(f"[DEBUG][ProductQueryBuilder] Fallback synonyms for '{user_term}': {valid_synonyms}")
            return valid_synonyms
        
        print(f"[DEBUG][ProductQueryBuilder] No fallback synonyms found for '{user_term}'")
        return []

    def _expand_synonyms(self, entities: dict, available_fields: list) -> dict:
        """Expand entity values using LLM-generated synonyms to match product fields."""
        expanded_entities = {}
        
        for key, value in entities.items():
            # Generate synonyms for this key using LLM
            synonyms = self._generate_synonyms_with_llm(key, available_fields)
            
            if synonyms:
                # Add all synonym fields
                for field in synonyms:
                    expanded_entities[field] = value
                print(f"[DEBUG][ProductQueryBuilder] Expanded '{key}' to fields: {synonyms}")
            else:
                # Keep the original key-value pair if no synonyms found
                expanded_entities[key] = value
                print(f"[DEBUG][ProductQueryBuilder] No synonyms found for '{key}', keeping original")
        
        return expanded_entities

    def build_json_filter(self, entities: dict, available_fields: list = None):
        # Default available fields if not provided
        if available_fields is None:
            available_fields = [
                'id', 'name', 'brand', 'model', 'category', 'price', 'description',
                'storage', 'ram', 'screen', 'camera', 'battery', 'os', 'colors',
                'warranty', 'in_stock', 'quantity', 'rating', 'reviews'
            ]
        
        # Expand synonyms before returning
        expanded_entities = self._expand_synonyms(entities, available_fields)
        return expanded_entities

    def tool(self, entities_json: str) -> str:
        print(f"[DEBUG][ProductQueryBuilder] Received entities_json: {entities_json}")
        try:
            data = json.loads(entities_json)
            # If coming from IntentEntityExtractor, extract 'entidades'
            entities = data.get("entidades", data)
            print(f"[DEBUG][ProductQueryBuilder] Original entities: {entities}")
        except Exception as e:
            print(f"[ERROR][ProductQueryBuilder] Failed to parse entities_json: {e}")
            return json.dumps({"message": "Erro: entrada inválida para filtro de produtos."}, ensure_ascii=False)
        
        result = self.build_json_filter(entities)
        print(f"[DEBUG][ProductQueryBuilder] Expanded entities: {result}")
        return json.dumps(result, ensure_ascii=False)

class ProductSearcher:
    def __init__(self, products, query_builder, mode="json", field_map=None):
        self.products = products
        self.query_builder = query_builder
        self.mode = mode
        self.field_map = field_map or {}

    def _normalize(self, s):
        if not isinstance(s, str):
            s = str(s)
        return unicodedata.normalize('NFKD', s).encode('ASCII', 'ignore').decode('ASCII').lower().strip()

    def _filter_products(self, entities: dict) -> List[Dict]:
        results = self.products
        for k, v in entities.items():
            # Skip filtering for "qualquer" values - they mean "any"
            if v == "qualquer" or v == "any" or v == "":
                continue
                
            product_key = self.field_map.get(k, k)
            def match(product):
                # Check direct product fields first
                val = product.get(product_key, '')
                
                # If not found in direct fields, check specifications
                if not val and 'specifications' in product:
                    val = product['specifications'].get(product_key, '')
                
                # If still not found, check if it's a nested specification field
                if not val and product_key in ['storage', 'ram', 'screen', 'camera', 'battery', 'os']:
                    if 'specifications' in product and product_key in product['specifications']:
                        val = product['specifications'][product_key]
                
                print(f"[DEBUG][ProductSearcher] Mapping entity '{k}' to product field '{product_key}'. Comparing product id={product.get('id')} {product_key}: {val} vs query: {v}")
                
                # Handle different value types
                if isinstance(v, str) and isinstance(val, str):
                    # For string values, check if the query term is mentioned in the product value
                    return self._normalize(v) in self._normalize(val)
                elif isinstance(v, (int, float)) and isinstance(val, (int, float)):
                    # For numeric values, check if product value meets the criteria
                    return val <= v  # Assuming v is a maximum value
                else:
                    # For other cases, try exact matching
                    val_list = val if isinstance(val, list) else [val]
                    query_list = v if isinstance(v, list) else [v]
                    for val_item in val_list:
                        for query_item in query_list:
                            if self._normalize(str(val_item)) == self._normalize(str(query_item)):
                                return True
                    return False
            results = [p for p in results if match(p)]
        return results

    def tool(self, entities_json: str) -> str:
        print(f"[DEBUG][ProductSearcher] Received entities_json: {entities_json}")
        if not entities_json or not entities_json.strip().startswith("{"):
            print(f"[ERROR][ProductSearcher] Invalid JSON input: {entities_json}")
            return json.dumps({"message": "Erro: entrada inválida para busca de produtos."}, ensure_ascii=False)
        try:
            data = json.loads(entities_json)
            # If coming from IntentEntityExtractor, extract 'entidades'
            entities = data.get("entidades", data)
            print(f"[DEBUG][ProductSearcher] Parsed entities: {entities}")
        except Exception as e:
            print(f"[ERROR][ProductSearcher] Failed to parse entities_json: {e}")
            return json.dumps({"message": "Erro: entrada inválida para busca de produtos."}, ensure_ascii=False)
        if self.mode == "json":
            filtered = self._filter_products(entities)
            print(f"[DEBUG][ProductSearcher] Filtered products: {filtered}")
            if not filtered:
                return json.dumps({"message": "Nenhum produto encontrado com os critérios fornecidos."}, ensure_ascii=False)
            return json.dumps(filtered, ensure_ascii=False)
        # else:
        #     sql = self.query_builder.build_sql(entities)
        #     print(f"[DEBUG] ProductSearchTool generated SQL: {sql}")
        #     return f"SQL: {sql}"

class ResponseManager:
    def __init__(self, llm, formatting_rules=None):
        self.llm = llm
        self.formatting_rules = formatting_rules or (
            """Generate a customer-friendly response that:
                1. Uses ONLY the information from the provided products JSON
                2. Never invents features, prices, availability, or any other information
                3. If no products are found, politely inform the user
                4. If products are found, present them in a helpful, informative way
                5. Keep the response natural and conversational
                6. Do not mention databases, JSON, or technical details"""
        )

    def _build_prompt(self, products_json: str, user_query: str) -> str:
        return f"""
Query: "{user_query}"
Products: {products_json}

Generate customer response in Portuguese using ONLY product data. Never invent info.
"""

    def tool(self, input: str) -> str:
        # Parse the input JSON to extract products_json and user_query
        try:
            data = json.loads(input)
            products_json = data.get("products_json", "[]")
            user_query = data.get("user_query", "Consulta do usuário")
        except Exception as e:
            print(f"[ERROR][ResponseManager] Failed to parse input: {e}")
            return "Erro ao processar informações dos produtos."
        
        # Validate products_json
        try:
            products = json.loads(products_json)
            if not products or (isinstance(products, list) and len(products) == 0):
                return "Desculpe, não encontramos produtos que correspondam à sua busca."
        except Exception as e:
            print(f"[ERROR][ResponseManager] Failed to parse products_json: {e}")
            return "Erro ao processar informações dos produtos."
        
        prompt = self._build_prompt(products_json, user_query)
        try:
            response = self.llm.invoke([{'role': 'user', 'content': prompt}])
            result = response.content.strip() if hasattr(response, 'content') else str(response).strip()
            return result
        except Exception as e:
            print(f"[ERROR][ResponseManager] LLM invocation failed: {e}")
            # Fallback response
            try:
                products = json.loads(products_json)
                if isinstance(products, list) and len(products) > 0:
                    product_names = [p.get('name', p.get('model', 'Produto')) for p in products[:3]]
                    return f"Temos os seguintes produtos: {', '.join(product_names)}"
                else:
                    return "Desculpe, não encontramos produtos que correspondam à sua busca."
            except:
                return "Desculpe, não foi possível processar sua consulta."


# --- Main Agent ---
class SearchAgent(BaseAgent):
    def __init__(self, config: Dict[str, Any], llm, mode="json", memory_manager=None):
        super().__init__(config, llm, memory_manager=memory_manager)
        self.mode = mode
        self.config = config
        self.llm = self.llm
        # Pass categories to extractor for strict matching
        self.intent_extractor = IntentEntityExtractor(
            self.llm,
            config["intents"],
            config["entity_schema"],
            categories=config.get("categories", [])
        )
        self.query_builder = ProductQueryBuilder(config["field_map"], self.llm)
        self.products = self._load_products() if self.mode == "json" else None
        self.product_searcher = ProductSearcher(self.products, self.query_builder, mode=self.mode, field_map=config.get("field_map", {}))
        self.response_manager = ResponseManager(self.llm)
        # Register tools as StructuredTool objects with schemas
        self.tools = [
            StructuredTool.from_function(
                func=self.intent_extractor.tool,
                name="IntentEntityExtractor",
                description="Extrai categoria, intenção e entidades de uma consulta do usuário.",
                args_schema=IntentEntityInput
            ),
            StructuredTool.from_function(
                func=self.query_builder.tool,
                name="ProductQueryBuilder",
                description="Gera um filtro para busca de produtos no JSON a partir das entidades extraídas.",
                args_schema=QueryBuilderInput
            ),
            StructuredTool.from_function(
                func=self.product_searcher.tool,
                name="ProductSearchTool",
                description="Busca produtos APENAS na lista carregada usando as categorias, e entidades extraídas. Nunca inventa produtos. Retorna apenas produtos reais.",
                args_schema=ProductSearchInput
            ),
            StructuredTool.from_function(
                func=self.response_manager.tool,
                name="ResponseManager",
                description="Gera uma resposta final ao cliente usando SOMENTE os produtos filtrados, de acordo com a intenção e a consulta do usuário. Nunca inventa informações. Segue regras estritas de formatação.",
                args_schema=ResponseManagerInput
            ),
        ]


        # Prompt for React agent
        prompt = PromptTemplate.from_template("""
You are a product search agent. Use the available tools to find and respond about products.

IMPORTANT: Follow this exact sequence for each step:

Question: {input}
Thought: I need to [describe what to do]
Action: [tool_name]
Action Input: [input for the tool]
Observation: [result from tool]
... (repeat Thought/Action/Action Input/Observation as needed)
Thought: Now I know the final answer
Final Answer: [final response to user]

Complete example workflow:
Question: Quais smartphones vocês têm?
Thought: I need to extract the category and entities from the user query
Action: IntentEntityExtractor
Action Input: "Quais smartphones vocês têm?"
Observation: {{"intencao": "buscar_por_categoria", "entidades": {{"categoria": "Smartphones"}}}}

Thought: I need to search for products using the extracted category
Action: ProductSearchTool
Action Input: {{"categoria": "Smartphones"}}
Observation: [{{"id": "001", "name": "Smartphone Pro X", "brand": "TechMobile"}}]

Thought: I need to generate a customer-friendly response
Action: ResponseManager
Action Input: {{"products_json": "[{{'id': '001', 'name': 'Smartphone Pro X', 'brand': 'TechMobile'}}]", "user_query": "Quais smartphones vocês têm?"}}
Observation: Temos o Smartphone Pro X da marca TechMobile

Thought: Now I know the final answer
Final Answer: Temos o Smartphone Pro X da marca TechMobile

Available categories: {categories}
Available tools: [{tool_names}]

{tools}

Question: {input}
Thought: {agent_scratchpad}
""").partial(
    categories=", ".join(self.config.get("categories", [])),
)
        agent = create_react_agent(self.llm, self.tools, prompt)
        self.agent_executor = AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=True,  # Set to True for more detailed logs
            handle_parsing_errors=True,
            max_iterations=4,  # Reduced to prevent loops
        )

    def _load_products(self) -> List[Dict]:
        path = self.config.get("json_path", "data/products.json")
        # If not absolute, resolve relative to project root (LangChainAgent)
        if not os.path.isabs(path):
            base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
            path = os.path.join(base_dir, path)
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get("products", [])
        except Exception as e:
            print(f"[SearchAgent] Error loading products: {e}")
            return []

    def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        query = input_data["query"]
        print(f"[DEBUG][SearchAgent] Received query: {query}")
        # Use invoke for structured input/output
        result = self.agent_executor.invoke({"input": query})
        print(f"[DEBUG][SearchAgent] Raw agent output: {result}")
        # If in JSON mode, ensure only real products or a not found message is returned
        if self.mode == "json":
            # Try to parse the output as JSON, fallback to string if not possible
            try:
                output = result["output"] if isinstance(result, dict) and "output" in result else result
                # If output is a stringified list or dict, parse it
                if isinstance(output, str) and (output.startswith("[") or output.startswith("{")):
                    parsed = json.loads(output)
                    print(f"[DEBUG][SearchAgent] Parsed agent output: {parsed}")
                    return {"result": parsed, "query": query, "customer_id": input_data.get("customer_id"), "status": "ok"}
                else:
                    print(f"[DEBUG][SearchAgent] Output is not JSON: {output}")
                    return {"result": output, "query": query, "customer_id": input_data.get("customer_id"), "status": "ok"}
            except Exception as e:
                print(f"[ERROR][SearchAgent] Exception parsing agent output: {e}")
                return {"result": result, "query": query, "customer_id": input_data.get("customer_id"), "status": "ok"}
        else:
            return {"result": result["output"] if isinstance(result, dict) and "output" in result else result, "query": query, "customer_id": input_data.get("customer_id"), "status": "ok"} 