from typing import Dict, Any
from .base_agent import BaseAgent

class LLMTestAgent(BaseAgent):
    __test__ = False  # Prevent pytest from collecting this as a test class
    """
    A simple test agent for testing LLM functionality.
    This agent performs basic tasks to verify LLM integration.
    """
    
    def __init__(self, config: Dict[str, Any], llm):
        super().__init__(config, llm)
    
    def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a simple test task using the LLM.
        
        Args:
            input_data: Dictionary containing test parameters
                - task: The task to perform (e.g., 'echo', 'translate', 'summarize')
                - text: The text to process
                - expected_response: Optional expected response for validation
        
        Returns:
            Dictionary containing the test results
        """
        task = input_data.get('task', 'echo')
        text = input_data.get('text', 'Hello World')
        expected_response = input_data.get('expected_response', None)
        
        # Create appropriate prompt based on task
        if task == 'echo':
            prompt = f"Echo back exactly this text: {text}"
        elif task == 'translate':
            prompt = f"Translate this text to Portuguese: {text}"
        elif task == 'summarize':
            prompt = f"Summarize this text in one sentence: {text}"
        elif task == 'count_words':
            prompt = f"Count the number of words in this text and respond with only the number: {text}"
        else:
            prompt = f"Process this text according to task '{task}': {text}"
        
        # Call LLM
        response = self._call_llm(prompt)
        
        # Validate response if expected_response is provided
        validation_result = None
        if expected_response:
            validation_result = {
                'expected': expected_response,
                'actual': response,
                'matches': expected_response.lower() in response.lower()
            }
        
        return {
            'task': task,
            'input_text': text,
            'response': response,
            'validation': validation_result,
            'agent_id': self.id,
            'token_usage': self.get_token_usage()
        }
    
    def test_llm_connectivity(self) -> Dict[str, Any]:
        """
        Test basic LLM connectivity with a simple prompt.
        
        Returns:
            Dictionary containing connectivity test results
        """
        test_prompt = "Respond with 'LLM is working' if you can process this request."
        
        try:
            response = self._call_llm(test_prompt)
            
            # More flexible success criteria
            success_indicators = [
                'llm is working',
                'working',
                'yes',
                'ok',
                'ready',
                'available',
                'can process',
                'able to process'
            ]
            
            response_lower = response.lower()
            success = any(indicator in response_lower for indicator in success_indicators)
            
            return {
                'test_type': 'connectivity',
                'success': success,
                'response': response,
                'agent_id': self.id,
                'token_usage': self.get_token_usage()
            }
        except Exception as e:
            return {
                'test_type': 'connectivity',
                'success': False,
                'error': str(e),
                'agent_id': self.id
            }
    
    def test_response_quality(self, test_cases: list) -> Dict[str, Any]:
        """
        Test LLM response quality with multiple test cases.
        
        Args:
            test_cases: List of dictionaries with 'input' and 'expected_keywords'
        
        Returns:
            Dictionary containing quality test results
        """
        results = []
        
        for i, test_case in enumerate(test_cases):
            input_text = test_case['input']
            expected_keywords = test_case.get('expected_keywords', [])
            
            try:
                response = self._call_llm(input_text)
                
                # Check if expected keywords are in response
                keyword_matches = []
                for keyword in expected_keywords:
                    if keyword.lower() in response.lower():
                        keyword_matches.append(keyword)
                
                results.append({
                    'test_case': i + 1,
                    'input': input_text,
                    'response': response,
                    'expected_keywords': expected_keywords,
                    'matched_keywords': keyword_matches,
                    'success': len(keyword_matches) > 0
                })
                
            except Exception as e:
                results.append({
                    'test_case': i + 1,
                    'input': input_text,
                    'error': str(e),
                    'success': False
                })
        
        return {
            'test_type': 'quality',
            'total_tests': len(test_cases),
            'passed_tests': sum(1 for r in results if r['success']),
            'results': results,
            'agent_id': self.id,
            'token_usage': self.get_token_usage()
        } 