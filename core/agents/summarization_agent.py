import json
import time
from datetime import datetime
from typing import Dict, Any, List
from .base_agent import BaseAgent

class SummarizationAgent(BaseAgent):
    def __init__(self, config: Dict[str, Any], llm, memory_manager=None):
        super().__init__(config, llm, memory_manager=memory_manager)
        
    def generate_conversation_summary(self, conversation_data: Dict[str, Any], seller_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a structured summary of the conversation based on seller configuration.
        
        Args:
            conversation_data: Full conversation context including STM and conversation history
            seller_config: Seller configuration with summary and privacy settings
            
        Returns:
            Structured summary in JSON format
        """
        print(f"[SummarizationAgent] Generating conversation summary...")
        print(f"[SummarizationAgent] Detail level: {seller_config.get('detail_level', 'medium')}")
        print(f"[SummarizationAgent] Privacy level: {seller_config.get('privacy_level', 'standard')}")
        if not hasattr(self, 'llm') or self.llm is None:
            print("[SummarizationAgent] ERROR: LLM object is None!")
        try:
            detail_level = seller_config.get('detail_level', 'medium')
            privacy_level = seller_config.get('privacy_level', 'standard')
            max_summary_length = seller_config.get('max_summary_length', 200)
            prompt = self._build_summarization_prompt(conversation_data, detail_level, privacy_level, max_summary_length)
            print(f"[SummarizationAgent] Calling LLM for summarization...")
            response = self._call_llm(prompt, customer_id=conversation_data.get('customer_id'))
            print(f"[SummarizationAgent] LLM response: {response}")
            summary = self._parse_summary_response(response)
            if summary:
                summary['timestamp'] = datetime.now().isoformat()
                summary['privacy_level'] = privacy_level
                summary['detail_level'] = detail_level
                print(f"[SummarizationAgent] Generated summary: {summary}")
                return summary
            else:
                print(f"[SummarizationAgent] Failed to parse summary, using fallback")
                fallback = self._generate_fallback_summary(conversation_data, seller_config)
                print(f"[SummarizationAgent] Fallback summary: {fallback}")
                return fallback
        except Exception as e:
            print(f"[SummarizationAgent] Exception during summary generation: {e}")
            fallback = self._generate_fallback_summary(conversation_data, seller_config)
            print(f"[SummarizationAgent] Fallback summary (exception): {fallback}")
            return fallback
    
    def _build_summarization_prompt(self, conversation_data: Dict[str, Any], detail_level: str, privacy_level: str, max_summary_length: int) -> str:
        """Build the summarization prompt based on configuration settings"""
        
        # Extract conversation information
        customer_id = conversation_data.get('customer_id', 'unknown')
        query = conversation_data.get('query', '')
        task_type = conversation_data.get('task_type', 'general')
        is_follow_up = conversation_data.get('is_follow_up', False)
        final_response = conversation_data.get('final_response', '')
        product_info = conversation_data.get('product_info', [])
        
        # Build product information based on privacy level
        products_discussed = self._extract_products_info(product_info, privacy_level)
        
        # Build conversation context
        conversation_context = self._build_conversation_context(conversation_data, detail_level)
        
        prompt = f"""
        {self.system_prompt}
        
        Gere um resumo estruturado da conversa com o cliente baseado nas configurações fornecidas.
        
        CONFIGURAÇÕES:
        - Nível de detalhe: {detail_level}
        - Nível de privacidade: {privacy_level}
        - Tamanho máximo do resumo: {max_summary_length} caracteres
        
        INFORMAÇÕES DA CONVERSA:
        - Cliente: {customer_id}
        - Query: {query}
        - Tipo de tarefa: {task_type}
        - É follow-up: {is_follow_up}
        - Resposta final: {final_response}
        - Produtos discutidos: {products_discussed}
        
        CONTEXTO DA CONVERSA:
        {conversation_context}
        
        INSTRUÇÕES DE RESUMO:
        """
        
        if detail_level == "low":
            prompt += """
        - Foque apenas no tipo de interação e resolução
        - Inclua apenas informações essenciais
        - Mantenha o resumo muito conciso
        """
        elif detail_level == "medium":
            prompt += """
        - Inclua produtos discutidos e consultas principais
        - Identifique a intenção do cliente
        - Forneça contexto suficiente para futuras interações
        """
        else:  # high
            prompt += """
        - Inclua fluxo detalhado da conversa
        - Identifique insights específicos sobre o cliente
        - Forneça contexto completo para personalização
        """
        
        if privacy_level == "minimal":
            prompt += """
        - Exclua detalhes pessoais e preços
        - Foque apenas em tipo de interação
        """
        elif privacy_level == "standard":
            prompt += """
        - Inclua produtos e consultas gerais
        - Exclua informações pessoais específicas
        """
        else:  # detailed
            prompt += """
        - Inclua todos os detalhes relevantes
        - Mantenha informações para uso interno
        """
        
        prompt += f"""
        
        RESPONDA EM JSON:
        {{
          "conversation_id": "conv_{int(time.time())}_{customer_id}",
          "customer_id": "{customer_id}",
          "interaction_type": "product_inquiry|support|general",
          "products_discussed": [
            {{
              "id": "product_id",
              "name": "product_name",
              "interest_level": "low|medium|high"
            }}
          ],
          "key_queries": ["lista_de_consultas_principais"],
          "customer_intent": "product_research|purchase_decision|support_request|price_inquiry",
          "resolution": "customer_satisfied|customer_purchased|escalated_to_support|no_resolution",
          "summary": "Resumo da conversa em até {max_summary_length} caracteres",
          "follow_up_required": true/false,
          "next_action": "none|follow_up_call|product_demo|support_escalation",
          "customer_insights": {{
            "preferences": ["preferências_identificadas"],
            "concerns": ["preocupações_identificadas"],
            "budget_indicator": "low|medium|high"
          }}
        }}
        """
        
        return prompt
    
    def _extract_products_info(self, product_info: List, privacy_level: str) -> List[Dict]:
        """Extract product information based on privacy level"""
        products = []
        
        if not product_info:
            return products
            
        for item in product_info:
            if isinstance(item, dict) and 'product' in item:
                product = item['product']
                product_data = {
                    'id': product.get('id', 'unknown'),
                    'name': product.get('name', 'unknown'),
                    'category': product.get('category', 'unknown')
                }
                
                # Add pricing based on privacy level
                if privacy_level in ['standard', 'detailed']:
                    product_data['price'] = product.get('price', 'N/A')
                
                # Add details based on privacy level
                if privacy_level == 'detailed':
                    product_data['description'] = product.get('description', '')[:100]
                    product_data['specifications'] = product.get('specifications', {})
                
                products.append(product_data)
        
        return products
    
    def _build_conversation_context(self, conversation_data: Dict[str, Any], detail_level: str) -> str:
        """Build conversation context based on detail level"""
        context_parts = []
        
        # Basic context always included
        context_parts.append(f"Tipo de tarefa: {conversation_data.get('task_type', 'unknown')}")
        context_parts.append(f"Follow-up: {conversation_data.get('is_follow_up', False)}")
        
        if detail_level in ['medium', 'high']:
            # Add reasoning and analysis
            reasoning = conversation_data.get('reasoning', '')
            if reasoning:
                context_parts.append(f"Análise: {reasoning}")
            
            # Add agent execution info
            agents_executed = conversation_data.get('agents_executed', [])
            if agents_executed:
                context_parts.append(f"Agentes executados: {', '.join(agents_executed)}")
        
        if detail_level == 'high':
            # Add full conversation flow
            context_parts.append(f"Query original: {conversation_data.get('query', '')}")
            context_parts.append(f"Resposta final: {conversation_data.get('final_response', '')[:200]}...")
            
            # Add product context
            product_context = conversation_data.get('product_context', {})
            if product_context:
                context_parts.append(f"Contexto do produto: {product_context}")
        
        return "\n".join(context_parts)
    
    def _parse_summary_response(self, response: str) -> Dict[str, Any]:
        """Parse the LLM response into structured summary"""
        try:
            # Clean the response - remove markdown code blocks
            cleaned_response = response.strip()
            
            # Remove markdown code blocks if present
            if cleaned_response.startswith('```json'):
                cleaned_response = cleaned_response.replace('```json', '').replace('```', '').strip()
            elif cleaned_response.startswith('```'):
                cleaned_response = cleaned_response.replace('```', '').strip()
            
            # Try to find JSON in the response using regex as fallback
            import re
            json_match = re.search(r'\{.*\}', cleaned_response, re.DOTALL)
            if json_match:
                cleaned_response = json_match.group()
            
            # Parse the JSON
            summary = json.loads(cleaned_response)
            
            # Validate required fields
            required_fields = ['conversation_id', 'customer_id', 'interaction_type', 'summary']
            for field in required_fields:
                if field not in summary:
                    print(f"[SummarizationAgent] Missing required field: {field}")
                    return None
            
            return summary
            
        except json.JSONDecodeError as e:
            print(f"[SummarizationAgent] Error parsing JSON: {e}")
            print(f"[SummarizationAgent] Raw response: {response}")
            return None
        except Exception as e:
            print(f"[SummarizationAgent] Unexpected error parsing response: {e}")
            return None
    
    def _generate_fallback_summary(self, conversation_data: Dict[str, Any], seller_config: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a fallback summary when LLM parsing fails"""
        customer_id = conversation_data.get('customer_id', 'unknown')
        task_type = conversation_data.get('task_type', 'general')
        query = conversation_data.get('query', '')
        
        # Extract basic product info
        product_info = conversation_data.get('product_info', [])
        products_discussed = []
        if product_info:
            for item in product_info:
                if isinstance(item, dict) and 'product' in item:
                    product = item['product']
                    products_discussed.append({
                        'id': product.get('id', 'unknown'),
                        'name': product.get('name', 'unknown'),
                        'category': product.get('category', 'unknown')
                    })
        
        # Determine customer intent based on task type
        intent_mapping = {
            'product_query': 'product_research',
            'support': 'support_request',
            'store_info': 'information_request',
            'general': 'general_inquiry'
        }
        customer_intent = intent_mapping.get(task_type, 'general_inquiry')
        
        # Generate basic summary
        summary_text = f"Cliente {customer_id} fez consulta sobre {task_type}. "
        if products_discussed:
            product_names = [p['name'] for p in products_discussed]
            summary_text += f"Produtos discutidos: {', '.join(product_names)}. "
        summary_text += "Conversa encerrada com satisfação do cliente."
        
        return {
            'conversation_id': f"conv_{int(time.time())}_{customer_id}",
            'timestamp': datetime.now().isoformat(),
            'customer_id': customer_id,
            'interaction_type': task_type,
            'products_discussed': products_discussed,
            'key_queries': [query],
            'customer_intent': customer_intent,
            'resolution': 'customer_satisfied',
            'summary': summary_text,
            'follow_up_required': False,
            'next_action': 'none',
            'privacy_level': seller_config.get('privacy_level', 'standard'),
            'detail_level': seller_config.get('detail_level', 'medium')
        }
    
    def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute method for compatibility with BaseAgent"""
        # This method is not used in the current design
        # The generate_conversation_summary method is the main interface
        raise NotImplementedError("Use generate_conversation_summary method instead of execute") 