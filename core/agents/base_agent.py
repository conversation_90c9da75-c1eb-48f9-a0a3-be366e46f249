from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

from langchain.llms.base import LLM

from core.utils.tokens_tracker import TokenTracker


class BaseAgent(ABC):
    def __init__(self, config: Dict[str, Any], llm: LLM, count_tokens_fn=None, memory_manager=None):
        self.id = config['id']
        self.role = config['role']
        self.system_prompt = config['system_prompt']
        self.max_tokens = config.get('max_tokens', 150)
        self.temperature = config.get('temperature', 0.2)
        # Todo: Criar Classe Llm:
        self.llm = llm
        self.token_tracker = TokenTracker()
        self.count_tokens_fn = count_tokens_fn
        self.memory_manager = memory_manager
        self._name: Optional[str] = None
        self._description: Optional[str] = None
        self._prompt_template: Optional[str] = None

    @abstractmethod
    def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute agent task"""
        pass

    def _call_llm(self, prompt: str, customer_id: str = None) -> str:
        """Call LLM and track tokens"""
        try:
            from langchain_core.messages import HumanMessage
            # Always use invoke method for LLMs (chat and completion)
            response = self.llm.invoke([HumanMessage(content=prompt)])
            # Extract content from response
            if hasattr(response, 'content'):
                response_text = response.content
            else:
                response_text = str(response)
            # Track tokens using the provided tokenizer if available
            if self.count_tokens_fn:
                input_tokens = self.count_tokens_fn(prompt)
                output_tokens = self.count_tokens_fn(response_text)
                print(
                    f"[BaseAgent] Tokenization | Agent: {self.id} | Prompt tokens: {input_tokens} | Response tokens: {output_tokens}")
                print(f"[BaseAgent] Prompt preview: '{prompt[:80]}'")
                print(f"[BaseAgent] Response preview: '{response_text[:80]}'")
            else:
                input_tokens = len(prompt.split()) * 1.3  # Approximation
                output_tokens = len(response_text.split()) * 1.3
            self.token_tracker.add_usage(
                customer_id=customer_id,
                agent_id=self.id,
                input_tokens=int(input_tokens),
                output_tokens=int(output_tokens)
            )
            return response_text
        except Exception as e:
            print(f"Error calling LLM: {e}")
            raise

    def get_token_usage(self) -> Dict[str, int]:
        """Get token usage for this agent"""
        return self.token_tracker.get_usage(self.id)
