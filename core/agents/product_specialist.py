import json
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod
from fuzzywuzzy import fuzz
from .base_agent import BaseAgent
import os
from datetime import datetime
import re
import time

from .specialist_agent import SpecialistAgent
from .tools.basic_search_product_tool import BasicSearchProductTool
from .tools.id_search_product_tool import IdSearchProductTool


class ProductSpecialistAgent(SpecialistAgent):

    def __init__(self, llm, data_path: str = "../../data/", memory_manager=None, count_tokens_fn=None):
        config = {
            'id': 'product_specialist',
            'role': 'specialist',
            'system_prompt': '',
            'max_tokens': 150,
            'temperature': 0.2
        }
        super().__init__()

        # Load products and tag schema (existing logic)
        self.data_path = data_path
        self.products = self._load_products()
        self.tag_schema = self._load_tag_schema()

        # Data source configuration (for future PostgreSQL integration)
        self.data_source_type = 'json'  # 'json' or 'postgresql'

        # Initialize tools
        self.tools = [
            IdSearchProductTool(self),
            BasicSearchProductTool(self)
        ]
        self.tool_registry = {tool.name: tool for tool in self.tools}

        # Tool selection strategy
        self.tool_selection_strategy = 'llm_guided'  # 'llm_guided' or 'rule_based'

        # Performance tracking
        self.query_count = 0
        self.total_query_time = 0.0

        print(f"[ProductSpecialistAgent] Initialized with {len(self.tools)} tools and {len(self.products)} products")

    def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:

        # start_time = time.time()
        # self.query_count += 1

        try:
            # Extract input data
            query = input_data.get('specialized_query', '')
            customer_id = input_data.get('customer_id', 'default_customer')
            conversation_data = input_data.get('conversation_data', {})

            # print(f"[ProductSpecialistAgent] Processing query: {query}")

            # Analyze query and select appropriate tools
            tool_plan = self._analyze_and_plan_tools(query, customer_id, conversation_data)

            # Execute tool plan
            execution_results = self._execute_tool_plan(tool_plan, query, customer_id, conversation_data)

            # Generate final response
            final_response = self._generate_final_response(execution_results, query, customer_id)

            # Update memory if available
            if self.memory_manager:
                self._update_memory(customer_id, query, execution_results, final_response)

            execution_time = time.time() - start_time
            self.total_query_time += execution_time

            return {
                'success': True,
                'response': final_response,
                'agent_id': self.id,
                'execution_time': execution_time,
                'tools_used': [step['tool'] for step in tool_plan],
                'token_usage': self.get_token_usage()
            }

        except Exception as e:
            execution_time = time.time() - start_time
            print(f"[ProductSpecialistAgent] Error in execute: {str(e)}")

            return {
                'success': False,
                'error': str(e),
                'agent_id': self.id,
                'execution_time': execution_time
            }

    def _analyze_and_plan_tools(self, query: str, customer_id: str, conversation_data: Dict) -> List[Dict]:
        """
        Analyze query and create execution plan for tools using LLM guidance.
        Uses intelligent tool selection instead of hard-coded rules.
        """
        # Prepare context for LLM analysis
        context_info = self._prepare_tool_selection_context(query, customer_id, conversation_data)

        # Use LLM to select appropriate tools
        if self.tool_selection_strategy == 'llm_guided':
            tool_plan = self._llm_guided_tool_selection(query, context_info)
        else:
            # Fallback to rule-based selection
            tool_plan = self._rule_based_tool_selection(query, context_info)

        return tool_plan

    def _prepare_tool_selection_context(self, query: str, customer_id: str, conversation_data: Dict) -> Dict:
        """Prepare context information for tool selection."""
        return {
            'is_follow_up': self._is_follow_up_query(query, conversation_data),
            'has_product_context': bool(conversation_data.get('product_context')),
            'conversation_length': len(conversation_data.get('conversation_history', [])),
            'previous_products': conversation_data.get('previous_products', []),
            'customer_preferences': conversation_data.get('customer_preferences', {}),
            'query_intent': self._analyze_query_intent(query),
            'query_complexity': self._assess_query_complexity(query),
            'customer_id': customer_id
        }

    def _llm_guided_tool_selection(self, query: str, context: Dict) -> List[Dict]:
        """Use LLM to intelligently select tools based on query and context."""

        # Create tool selection prompt
        tools_description = self._get_tools_description()

        prompt = f"""
Você é um especialista em seleção de ferramentas para busca de produtos. Analise a consulta do usuário e o contexto para selecionar as ferramentas mais apropriadas.

CONSULTA DO USUÁRIO: {query}

CONTEXTO:
- É follow-up: {context['is_follow_up']}
- Tem contexto de produtos: {context['has_product_context']}
- Produtos anteriores: {len(context['previous_products'])} produtos
- Intenção da consulta: {context['query_intent']}
- Complexidade: {context['query_complexity']}

FERRAMENTAS DISPONÍVEIS:
{tools_description}

INSTRUÇÕES:
1. Selecione as ferramentas necessárias na ordem de execução
2. Para cada ferramenta, especifique os parâmetros principais
3. Considere se é busca básica, detalhada, por ID, etc.
4. Para follow-ups, sempre considere contexto primeiro
5. Para buscas complexas, pode usar múltiplas ferramentas

RESPONDA EM JSON:
{{
    "tools": [
        {{
            "tool": "nome_da_ferramenta",
            "params": {{"param1": "valor1", "param2": "valor2"}},
            "reason": "motivo para usar esta ferramenta",
            "priority": 1
        }}
    ],
    "search_strategy": "basic|detailed|comparison|follow_up",
    "expected_result_type": "product_list|single_product|comparison|context_aware"
}}

JSON:"""

        try:
            response = self._call_llm(prompt, context.get('customer_id', 'system'))

            # Parse LLM response
            tool_plan_data = json.loads(response.strip())

            # Convert to internal format
            tool_plan = []
            for tool_info in tool_plan_data.get('tools', []):
                tool_plan.append({
                    'tool': tool_info['tool'],
                    'params': tool_info['params'],
                    'reason': tool_info['reason'],
                    'priority': tool_info.get('priority', 1),
                    'search_strategy': tool_plan_data.get('search_strategy', 'basic'),
                    'expected_result_type': tool_plan_data.get('expected_result_type', 'product_list')
                })

            return sorted(tool_plan, key=lambda x: x['priority'])

        except Exception as e:
            print(f"[ProductSpecialistAgent] LLM tool selection failed: {e}")
            # Fallback to rule-based selection
            return self._rule_based_tool_selection(query, context)

    def _get_tools_description(self) -> str:
        """Get formatted description of all available tools."""
        descriptions = []
        for tool in self.tools:
            descriptions.append(f"- {tool.name}: {tool.description}")

        return "\n".join(descriptions)

    def _rule_based_tool_selection(self, query: str, context: Dict) -> List[Dict]:
        """Fallback rule-based tool selection."""
        tool_plan = []

        if context['is_follow_up']:
            # For follow-ups, use existing logic
            tool_plan.append({
                'tool': 'basic_product_search',
                'params': {'query': query, 'max_results': 5},
                'reason': 'Follow-up query detected, using basic search',
                'priority': 1
            })
        else:
            # Determine search type based on query analysis
            search_type = self._determine_search_type(query)

            if search_type == 'detailed':
                tool_plan.append({
                    'tool': 'detailed_product_search',
                    'params': {'query': query, 'detail_level': 'detailed'},
                    'reason': 'Detailed product information requested',
                    'priority': 1
                })
            else:
                tool_plan.append({
                    'tool': 'basic_product_search',
                    'params': {'query': query, 'max_results': 5},
                    'reason': 'Basic product search',
                    'priority': 1
                })

        return tool_plan

    def _execute_tool_plan(self, tool_plan: List[Dict], query: str, customer_id: str, conversation_data: Dict) -> List[
        Dict]:
        """Execute the planned tools in sequence."""
        execution_results = []
        context = {}

        for step in tool_plan:
            tool_name = step['tool']
            params = step['params']

            if tool_name in self.tool_registry:
                tool = self.tool_registry[tool_name]

                # Execute tool
                result = tool.execute(**params)

                # Store result and update context
                execution_results.append({
                    'tool': tool_name,
                    'params': params,
                    'result': result,
                    'reason': step['reason']
                })

                # Update context for next tools
                if 'results' in result:
                    context['last_search_results'] = result['results']

        return execution_results

    def _generate_final_response(self, execution_results: List[Dict], query: str, customer_id: str) -> Dict[str, Any]:
        """Generate final response with structured product information."""

        # Extract products from execution results
        products_found = self._extract_products_from_results(execution_results)

        # If we have products, format them according to the existing interface
        if products_found:
            # Convert to the expected format for compatibility
            formatted_results = []
            for product in products_found:
                formatted_results.append({
                    "product": product,
                    "confidence": product.get('confidence', 0.8)
                })

            return {
                "query": query,
                "results": formatted_results,
                'customer_id': customer_id,
                'agent_id': 'product_specialist',
                'status': 'multi_product' if len(formatted_results) > 1 else 'single_product'
            }
        else:
            return {
                "query": query,
                "results": [],
                'customer_id': customer_id,
                'agent_id': 'product_specialist',
                'status': 'no_products_found'
            }

    def _extract_products_from_results(self, execution_results: List[Dict]) -> List[Dict]:
        """Extract products from execution results."""
        products = []

        for result in execution_results:
            tool_result = result['result']

            if tool_result.get('success'):
                if 'results' in tool_result:
                    # From search tools
                    products.extend(tool_result['results'])
                elif 'product' in tool_result:
                    # From get_product_by_id tool
                    products.append(tool_result['product'])

        return products

    def _update_memory(self, customer_id: str, query: str, execution_results: List[Dict], final_response: Dict):
        """Update memory manager with conversation state and product context."""
        if self.memory_manager:
            # Extract product references from results
            product_references = self._extract_product_references(execution_results)

            # Update conversation context
            self.memory_manager.update_conversation_context(
                customer_id=customer_id,
                query=query,
                response=final_response,
                products_discussed=product_references,
                tools_used=[result['tool'] for result in execution_results]
            )

    def _extract_product_references(self, execution_results: List[Dict]) -> List[Dict]:
        """Extract product references for memory storage."""
        references = []

        for result in execution_results:
            tool_result = result['result']

            if tool_result.get('success') and 'results' in tool_result:
                for product in tool_result['results']:
                    references.append({
                        'id': product.get('id'),
                        'name': product.get('name'),
                        'category': product.get('category')
                    })

        return references

    def _log_tool_usage(self, tool_name: str, input_params: dict, output: dict, execution_time: float):
        """Log tool usage for debugging and analytics."""
        print(
            f"[ProductSpecialistAgent] Tool: {tool_name} | Time: {execution_time:.3f}s | Success: {output.get('success', False)}")

    def _load_products(self) -> List[Dict]:
        """Load products from JSON file."""
        try:
            with open(f"{self.data_path}products.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
                products = data.get("products", [])
                print(f"✅ Loaded {len(products)} products from {self.data_path}products.json")
                return products
        except FileNotFoundError:
            print(f"❌ Erro: Arquivo de produtos não encontrado em {self.data_path}")
            return []
        except Exception as e:
            print(f"❌ Erro ao carregar produtos: {e}")
            return []

    def _load_tag_schema(self) -> Dict[str, List[str]]:
        schema_path = os.path.join(self.data_path, "tag_schema.json")
        try:
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema = json.load(f)
                print(f"✅ Loaded tag schema from {schema_path}")
                return schema
        except FileNotFoundError:
            print(f"⚠️ tag_schema.json not found, using all keys from first product as tags.")
            if self.products:
                keys = list(self.products[0].keys())
                return {"universal": keys}
            return {"universal": []}
        except Exception as e:
            print(f"❌ Erro ao carregar tag_schema: {e}")
            return {"universal": []}

    def _get_relevant_tags(self, product: Dict) -> List[str]:
        tags = set(self.tag_schema.get("universal", []))
        category = product.get("category", "").lower()
        if category and category in self.tag_schema:
            tags.update(self.tag_schema[category])
        return list(tags)

    def _tag_match(self, query: str, product: Dict, tags: List[str]) -> bool:
        query_lower = query.lower()
        for tag in tags:
            value = str(product.get(tag, "")).lower()
            if value and value in query_lower:
                return True
        return False

    def _fuzzy_match(self, query: str, product: Dict, tags: List[str]) -> float:
        query_lower = query.lower()
        scores = []
        for tag in tags:
            value = str(product.get(tag, ""))
            if value:
                scores.append(fuzz.token_set_ratio(query_lower, value.lower()) / 100)
        return max(scores) if scores else 0.0

    def _analyze_query_intent(self, query: str) -> str:
        """Analyze query to determine user intent."""
        query_lower = query.lower()

        if any(word in query_lower for word in ['comparar', 'diferença', 'melhor', 'vs']):
            return 'comparison'
        elif any(word in query_lower for word in ['detalhes', 'especificações', 'características']):
            return 'detailed_info'
        elif any(word in query_lower for word in ['preço', 'custo', 'valor', 'barato']):
            return 'price_focused'
        elif any(word in query_lower for word in ['categoria', 'tipo', 'seção']):
            return 'category_browse'
        else:
            return 'general_search'

    def _assess_query_complexity(self, query: str) -> str:
        """Assess the complexity of the query."""
        word_count = len(query.split())

        if word_count <= 3:
            return 'simple'
        elif word_count <= 8:
            return 'medium'
        else:
            return 'complex'

    def _determine_search_type(self, query: str) -> str:
        """Determine type of search needed based on query."""
        query_lower = query.lower()

        if any(word in query_lower for word in ['detalhes', 'especificações', 'características', 'completo']):
            return 'detailed'
        elif any(word in query_lower for word in ['comparar', 'diferença', 'melhor']):
            return 'comparison'
        else:
            return 'basic'

    def _search_products_basic(self, query: str, category: str = None, max_results: int = 5) -> List[Dict]:
        """Basic product search using existing fuzzy matching logic."""
        # Use existing search logic from the original implementation
        search_terms = [query.lower()] + self._generate_synonyms(query, "system")
        tag_matched_products = []

        for product in self.products:
            if category and product.get('category', '').lower() != category.lower():
                continue
            tags = self._get_relevant_tags(product)
            if any(self._tag_match(term, product, tags) for term in search_terms):
                tag_matched_products.append(product)

        # If no tag matches, fallback to all products
        candidates = tag_matched_products if tag_matched_products else [
            p for p in self.products
            if not category or p.get('category', '').lower() == category.lower()
        ]

        # Fuzzy matching and scoring
        scored_products = []
        for product in candidates:
            tags = self._get_relevant_tags(product)
            score = max(self._fuzzy_match(term, product, tags) for term in search_terms)
            if score > 0.1:
                scored_products.append((product, score))

        scored_products.sort(key=lambda x: x[1], reverse=True)

        # Return top results with scores
        results = []
        for product, score in scored_products[:max_results]:
            product_copy = product.copy()
            product_copy['confidence'] = round(score, 2)
            results.append(product_copy)

        return results

    def _search_products_detailed(self, query: str, detail_level: str = "detailed") -> List[Dict]:
        """Detailed product search using existing logic."""
        # Use basic search but return more detailed information
        basic_results = self._search_products_basic(query, max_results=5)

        detailed_results = []
        for product in basic_results:
            # Add all available product information for detailed search
            tags = self._get_relevant_tags(product)
            detailed_product = {tag: product.get(tag, None) for tag in tags}

            # Add all other fields for maximum detail
            for k, v in product.items():
                if k not in detailed_product:
                    detailed_product[k] = v

            detailed_results.append(detailed_product)

        return detailed_results

    def _get_product_by_id(self, product_id: str, detail_level: str = "full") -> Optional[Dict]:
        """Get product by ID using existing logic."""
        for product in self.products:
            if product.get('id') == product_id:
                if detail_level == "basic":
                    tags = self._get_relevant_tags(product)
                    return {tag: product.get(tag, None) for tag in tags}
                else:
                    return product.copy()
        return None

    def _is_follow_up_query(self, query: str, conversation_data: Dict) -> bool:
        """Determine if query is a follow-up (existing logic)."""
        return conversation_data.get('is_follow_up', False)

    def _generate_synonyms(self, query: str, customer_id: str = "unknown_customer") -> List[str]:
        """Generate synonyms and related terms for a query using the LLM, matching the prompt style of the query manager and response agent."""
        prompt = f"""
                Gere uma lista de sinônimos e termos relacionados para a seguinte consulta de busca de produtos. 
                Responda APENAS com uma lista separada por vírgulas, afim de facilitar a busca em um banco de dados.
                Retorne APENAS 5 sinônimos por produto.

                Consulta: {query}
                """
        # Before calling the LLM, log the prompt
        if not customer_id:
            print("[ProductSpecialistAgent] WARNING: customer_id not set for logging, using 'unknown_customer'.")
            customer_id = "unknown_customer"
        log_dir = os.path.join(os.path.dirname(__file__), f'../../logs/{customer_id}')
        os.makedirs(log_dir, exist_ok=True)
        log_path = os.path.join(log_dir, 'product_specialist_prompts.log')
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(
                f"\n---\n[{datetime.now().isoformat()}] customer_id={customer_id} | query={query}\nPROMPT:\n{prompt}\n")
        response = self._call_llm(prompt)
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(f"RESPONSE:\n{response}\n")
        # Parse the response as a comma-separated string
        return [s.strip() for s in response.split(",") if s.strip()]

    def process_query(self, query: str, customer_id: str = "default_customer", conversation_data: Dict = None) -> Dict[
        str, Any]:
        """
        Process a product-related query using the new tool-based architecture.
        Maintains backward compatibility with the existing interface.
        """
        # Prepare input data for the new execute method
        input_data = {
            'specialized_query': conversation_data.get('specialized_query') if conversation_data else query,
            'customer_id': customer_id,
            'conversation_data': conversation_data or {},
            'product_context': conversation_data.get('product_context', {}) if conversation_data else {},
            'is_follow_up': conversation_data.get('is_follow_up', False) if conversation_data else False,
            'clarification_needed': conversation_data.get('clarification_needed',
                                                          False) if conversation_data else False,
            'clarification_question': conversation_data.get('clarification_question',
                                                            None) if conversation_data else None,
            'relevant_products': conversation_data.get('relevant_products', []) if conversation_data else []
        }

        # Handle special cases for backward compatibility
        specialized_query = input_data['specialized_query']
        clarification_needed = input_data['clarification_needed']
        clarification_question = input_data['clarification_question']
        is_follow_up = input_data['is_follow_up']
        relevant_products = input_data['relevant_products']
        product_context = input_data['product_context']

        print(f"[ProductSpecialistAgent] Specialized Query Used: {specialized_query}")
        print(f"[ProductSpecialistAgent] Query: {specialized_query} | Follow-up: {is_follow_up}")

        # If clarification is needed, return a clarifying message
        if clarification_needed:
            return {
                "query": clarification_question or "Por favor, especifique a qual produto ou categoria você se refere.",
                "results": [],
                'customer_id': customer_id,
                'agent_id': 'product_specialist',
                'status': 'clarification_needed'
            }

        # Enhanced follow-up handling using STM context
        if is_follow_up:
            return self._handle_follow_up_with_context(specialized_query, conversation_data, customer_id)

        # If relevant_products is a non-empty list, return details for all
        if relevant_products and isinstance(relevant_products, list):
            return self._handle_relevant_products(relevant_products, product_context, customer_id, specialized_query)

        # Use the new tool-based execution for regular queries
        result = self.execute(input_data)

        if result.get('success'):
            return result['response']
        else:
            # Fallback to legacy search if tool-based approach fails
            return self._legacy_search_fallback(specialized_query, customer_id, product_context)

    def _handle_relevant_products(self, relevant_products: List, product_context: Dict, customer_id: str,
                                  specialized_query: str) -> Dict[str, Any]:
        """Handle case where relevant products are provided."""
        requested_category = product_context.get('category', '').lower()
        detail_level = product_context.get('detail_level', 'basic')

        filtered_products = []
        for prod in relevant_products:
            p = prod.get('product', prod)
            # If only product_name is present, treat as a search term
            if 'product_name' in p and not p.get('id'):
                # Search for products matching the name (case-insensitive, partial match)
                name = p['product_name'].lower()
                matches = [prod for prod in self.products if
                           name in prod.get('name', '').lower() or name in prod.get('category', '').lower()]
                filtered_products.extend(matches)
            else:
                if requested_category and p.get('category', '').lower() != requested_category:
                    continue
                filtered_products.append(p)

        results = []
        for product in filtered_products:
            tags = self._get_relevant_tags(product)
            if detail_level == 'basic':
                product_struct = {tag: product.get(tag, None) for tag in tags}
            else:
                product_struct = {tag: product.get(tag, None) for tag in tags}
                for k, v in product.items():
                    if k not in product_struct:
                        product_struct[k] = v
            results.append({
                "product": product_struct,
                "confidence": 1.0
            })

        if not results:
            return {
                "query": specialized_query,
                "results": [],
                'customer_id': customer_id,
                'agent_id': 'product_specialist',
                'status': 'no_products_found'
            }

        return {
            "query": f"Detalhes para {len(results)} produto(s)",
            "results": results,
            'customer_id': customer_id,
            'agent_id': 'product_specialist',
            'status': 'multi_product' if len(results) > 1 else 'single_product'
        }

    def _legacy_search_fallback(self, query: str, customer_id: str, product_context: Dict) -> Dict[str, Any]:
        """Fallback to legacy search logic if tool-based approach fails."""
        detail_level = product_context.get('detail_level', 'basic')
        requested_category = product_context.get('category', '').lower()

        # Tag-based presearch
        search_terms = [query.lower()] + self._generate_synonyms(query, customer_id)
        tag_matched_products = []
        for product in self.products:
            if requested_category and product.get('category', '').lower() != requested_category:
                continue
            tags = self._get_relevant_tags(product)
            if any(self._tag_match(term, product, tags) for term in search_terms):
                tag_matched_products.append(product)

        # If no tag matches, fallback to all products
        candidates = tag_matched_products if tag_matched_products else [
            p for p in self.products
            if not requested_category or p.get('category', '').lower() == requested_category
        ]

        # Fuzzy matching and scoring
        scored_products = []
        for product in candidates:
            tags = self._get_relevant_tags(product)
            score = max(self._fuzzy_match(term, product, tags) for term in search_terms)
            if score > 0.1:
                scored_products.append((product, score))

        scored_products.sort(key=lambda x: x[1], reverse=True)

        # Prepare results
        return self._prepare_structured_results(scored_products, customer_id, detail_level)

    def _extract_products_from_query(self, query: str) -> list:
        """Extract product names and IDs from the query string."""
        # Find all IDs in the format (ID: xxx)
        ids = re.findall(r'ID:\s*(\w+)', query)
        # Find all product names (words before (ID: xxx))
        names = re.findall(r'([\w\s\-]+)\s*\(ID:', query)
        return {'ids': ids, 'names': [n.strip() for n in names]}

    def _fallback_search(self, query: str, customer_id: str) -> Dict[str, Any]:
        """Fallback search for follow-up queries: match all products by name/ID in query."""
        extracted = self._extract_products_from_query(query)
        ids = set(extracted['ids'])
        names = set(n.lower() for n in extracted['names'])
        matched_products = []
        for product in self.products:
            if (product.get('id') in ids) or (product.get('name', '').lower() in names):
                matched_products.append(product)
        results = []
        for product in matched_products:
            results.append({
                "product": product,
                "confidence": 1.0
            })
        return {
            "query": query,
            "results": results,
            'customer_id': customer_id,
            'agent_id': 'product_specialist',
            'status': 'multi_product' if len(results) > 1 else 'single_product'
        }

    def _handle_follow_up_with_context(self, specialized_query: str, conversation_data: Dict, customer_id: str) -> Dict[
        str, Any]:
        """Handle follow-up queries by matching product_id or name from product_context, then fallback to fuzzy/tag matching."""
        print(f"[ProductSpecialistAgent] Handling follow-up with context: {specialized_query}")
        product_context = conversation_data.get('product_context', {}) if conversation_data else {}
        product_id = product_context.get('product_id')
        product_name = product_context.get('product_name')
        requested_category = product_context.get('category', '').lower() if product_context else ''
        relevant_products = conversation_data.get('relevant_products', []) if conversation_data else []
        # If relevant_products is a non-empty list, return details for all
        if relevant_products and isinstance(relevant_products, list):
            filtered_products = []
            for prod in relevant_products:
                p = prod.get('product', prod)
                # If only product_name is present, treat as a search term
                if 'product_name' in p and not p.get('id'):
                    # Search for products matching the name (case-insensitive, partial match)
                    name = p['product_name'].lower()
                    matches = [prod for prod in self.products if
                               name in prod.get('name', '').lower() or name in prod.get('category', '').lower()]
                    filtered_products.extend(matches)
                else:
                    if requested_category and p.get('category', '').lower() != requested_category:
                        continue
                    filtered_products.append(p)
            results = []
            for product in filtered_products:
                tags = self._get_relevant_tags(product)
                product_struct = {tag: product.get(tag, None) for tag in tags}
                for k, v in product.items():
                    if k not in product_struct:
                        product_struct[k] = v
                results.append({
                    "product": product_struct,
                    "confidence": 1.0
                })
            if not results:
                return {
                    "query": specialized_query,
                    "results": [],
                    'customer_id': customer_id,
                    'agent_id': 'product_specialist',
                    'status': 'no_products_found'
                }
            return {
                "query": f"Detalhes para {len(results)} produto(s)",
                "results": results,
                'customer_id': customer_id,
                'agent_id': 'product_specialist',
                'status': 'multi_product' if len(results) > 1 else 'single_product'
            }
        # If product_id or product_name is present, match and return
        if product_id or product_name:
            for product in self.products:
                if (product_id and product.get('id') == product_id) or (
                        product_name and product.get('name') == product_name):
                    tags = self._get_relevant_tags(product)
                    product_struct = {tag: product.get(tag, None) for tag in tags}
                    for k, v in product.items():
                        if k not in product_struct:
                            product_struct[k] = v
                    return {
                        "query": specialized_query,
                        "results": [{"product": product_struct, "confidence": 1.0}],
                        'customer_id': customer_id,
                        'agent_id': 'product_specialist',
                        'status': 'single_product'
                    }
        # Fallback: parse all product names/IDs in query
        return self._fallback_search(specialized_query, customer_id)

    def _prepare_structured_results(self, scored_products: List, customer_id: str, detail_level: str = 'basic') -> Dict[
        str, Any]:
        results = []
        for product, score in scored_products[:5]:
            tags = self._get_relevant_tags(product)
            if detail_level == 'basic':
                # Only universal + category tags, not all specs
                product_struct = {tag: product.get(tag, None) for tag in tags}
            else:
                # All available details for the product
                product_struct = {tag: product.get(tag, None) for tag in tags}
                # Optionally, add all other fields for max detail
                for k, v in product.items():
                    if k not in product_struct:
                        product_struct[k] = v
            results.append({
                "product": product_struct,
                "confidence": round(score, 2)
            })
        print("PRODUCT_SPECIALIST", results)
        return {
            "query": "Busca estruturada de produtos",
            "results": results,
            'customer_id': customer_id,
            'agent_id': 'product_specialist',
            'exact_match_found': None,
            'status': 'structured'
        }

    # Keep the old execute method at the end for backward compatibility
    def execute_legacy(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Legacy execute method for backward compatibility."""
        query = input_data.get('specialized_query', '')
        customer_id = input_data.get('customer_id', 'default_customer')
        return self.process_query(query, customer_id, input_data)
