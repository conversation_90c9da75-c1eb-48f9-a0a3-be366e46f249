import sys
import os
from dotenv import load_dotenv
from core.utils.config_loader import ConfigLoader
from core.utils.llm_manager import LlmManager
from core.agents.search_agent import SearchAgent

def main():
    # Load .env file for environment variables
    dotenv_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', '.env')
    load_dotenv(dotenv_path, verbose=True)

    # Load configuration (mimic chatBotWrapper.py)
    config_loader = ConfigLoader()
    llm_config = config_loader.load_config("config/llm_config.yaml")['llm']
    # Expand env vars in primary_llm and fallback_llm
    for key in ["primary_llm", "fallback_llm"]:
        val = llm_config.get(key)
        if isinstance(val, str) and val.startswith("${") and val.endswith("}"):
            env_key = val[2:-1]
            llm_config[key] = os.environ.get(env_key, val)
    agents_config = config_loader.load_config("config/agents.yaml")['agents']
    search_agent_config = config_loader.load_config("config/search_agent_config.yaml")
    merged_config = {**agents_config['search_agent'], **search_agent_config}

    # Initialize LLM
    llm_factory = LlmManager(llm_config)
    llm = llm_factory.get_llm_with_fallback()
    provider = getattr(llm, 'provider', None)
    model = getattr(llm, 'model', None) or getattr(llm, 'model_name', None)
    print(f"🔍 Using LLM: provider={provider or type(llm).__name__}, model={model}")

    # Initialize SearchAgent with resolved LLM
    agent = SearchAgent(merged_config, llm, mode="json")
    print("\n[SearchAgent CLI] Type your query and press Enter. Type 'exit' or 'quit' to stop.\n")
    customer_id = "cli_user"
    while True:
        try:
            query = input("Query: ").strip()
            if query.lower() in ("exit", "quit"): break
            input_data = {"query": query, "customer_id": customer_id}
            response = agent.execute(input_data)
            print("\nAgent Response:")
            print(response["result"])
            print("\n---\n")
        except KeyboardInterrupt:
            print("\nExiting.")
            break
        except Exception as e:
            print(f"[Error] {e}\n---\n")

if __name__ == "__main__":
    main() 