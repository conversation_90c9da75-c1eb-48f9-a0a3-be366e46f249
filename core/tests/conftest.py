import sys
import os

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..')
sys.path.insert(0, os.path.abspath(project_root))

import pytest
from dotenv import load_dotenv
from core.utils.config_loader import ConfigLoader

# Load environment variables from .env file
load_dotenv("config/.env")

# Register pytest markers to avoid warnings
def pytest_configure(config):
    """Register custom markers"""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "llm: LLM-related tests")
    config.addinivalue_line("markers", "agent: Agent-related tests")
    config.addinivalue_line("markers", "slow: Slow running tests")
    config.addinivalue_line("markers", "real_api: Real API connection tests")

@pytest.fixture(scope="module")
def real_llm_config():
    """Real LLM configuration for testing"""
    config_loader = ConfigLoader()
    return config_loader.get_llm_config("config/llm_config.yaml")

@pytest.fixture(scope="module")
def real_agent_config():
    """Real agent configuration for testing"""
    return {
        'id': 'test_agent',
        'role': 'Test Agent',
        'system_prompt': "You are a test agent. Respond with 'Test response'.",
        'max_tokens': 100
    } 