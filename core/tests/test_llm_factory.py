import sys
import os

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..')
sys.path.insert(0, project_root)

import pytest
from dotenv import load_dotenv
from core.utils.llm_manager import LlmManager
from core.utils.config_loader import ConfigLoader

import logging
logging.basicConfig(level=logging.INFO)

# Load environment variables from .env file
load_dotenv("config/.env")

@pytest.fixture(scope="module")
def real_llm_config():
    config_loader = ConfigLoader()
    config = config_loader.get_llm_config("config/llm_config.yaml")
    # Expand env vars in primary_llm and fallback_llm
    llm_section = config.get("llm", config)
    for key in ["primary_llm", "fallback_llm"]:
        val = llm_section.get(key)
        if isinstance(val, str) and val.startswith("${") and val.endswith("}"):
            env_key = val[2:-1]
            llm_section[key] = os.environ.get(env_key, val)
    return config

from langchain_core.messages import AIMessage

class TestLLMFactory:
    """Test suite for LLMFactory class with real API calls"""
    
    @pytest.mark.unit
    @pytest.mark.llm
    def test_llm_factory_initialization(self, real_llm_config):
        factory = LlmManager(real_llm_config)
        assert factory.config == real_llm_config
    
    @pytest.mark.unit
    @pytest.mark.llm
    def test_get_primary_llm_groq(self, real_llm_config):
        llm_section = real_llm_config.get("llm", real_llm_config)
        primary_key = llm_section.get("primary_llm")
        llms = llm_section.get("llms") or real_llm_config.get("llms")
        if not llms or primary_key not in llms or llms[primary_key]["provider"] != "groq":
            pytest.skip("Primary LLM is not Groq in config.")
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        assert hasattr(llm, 'invoke')
        result = llm.invoke("Say hello in one word.")
        if hasattr(result, "content"):
            assert isinstance(result.content, str) and len(result.content) > 0
        else:
            assert isinstance(result, str) and len(result) > 0

    @pytest.mark.unit
    @pytest.mark.llm
    def test_get_primary_llm_google_gemini(self, real_llm_config):
        llm_section = real_llm_config.get("llm", real_llm_config)
        primary_key = llm_section.get("primary_llm")
        llms = llm_section.get("llms") or real_llm_config.get("llms")
        if not llms or primary_key not in llms or llms[primary_key]["provider"] != "google_gemini":
            pytest.skip("Primary LLM is not Google Gemini in config.")
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        assert hasattr(llm, 'invoke')
        # Accept .model or .model_name
        model_val = getattr(llm, 'model', None) or getattr(llm, 'model_name', None)
        assert model_val == 'gemini-2.0-flash'

    @pytest.mark.unit
    @pytest.mark.llm
    def test_get_primary_llm_mistral(self, real_llm_config):
        llm_section = real_llm_config.get("llm", real_llm_config)
        primary_key = llm_section.get("primary_llm")
        llms = llm_section.get("llms") or real_llm_config.get("llms")
        if not llms or primary_key not in llms or llms[primary_key]["provider"] != "mistralai":
            pytest.skip("Primary LLM is not Mistral in config.")
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        assert hasattr(llm, 'invoke')
        model_val = getattr(llm, 'model', None) or getattr(llm, 'model_name', None)
        assert model_val == 'mistral-medium'
    
    @pytest.mark.unit
    @pytest.mark.llm
    def test_get_fallback_llm(self, real_llm_config):
        llm_section = real_llm_config.get("llm", real_llm_config)
        fallback_key = llm_section.get("fallback_llm")
        llms = llm_section.get("llms") or real_llm_config.get("llms")
        if not llms or fallback_key not in llms:
            pytest.skip("Fallback LLM not found in config.")
        factory = LlmManager(real_llm_config)
        llm = factory.get_fallback_llm()
        assert hasattr(llm, 'invoke')
        model_val = getattr(llm, 'model', None) or getattr(llm, 'model_name', None)
        assert model_val == llms[fallback_key]['model_name']
    
    @pytest.mark.unit
    @pytest.mark.llm
    def test_get_primary_llm_missing_api_key(self, real_llm_config):
        # Use a config with a non-existent API key
        config = {
            'llm': {
                'primary_llm': 'gemini',
                'llms': {
                    'gemini': {
                'provider': 'google_gemini',
                'api_key_env_var': 'NON_EXISTENT_API_KEY',
                        'model_name': 'gemini-2.0-flash',
                        'tokenizer_model_name': 'gpt2',
                    }
                }
            }
        }
        factory = LlmManager(config)
        with pytest.raises(ValueError, match="API key for google_gemini not found"):
            factory.get_primary_llm()
    
    @pytest.mark.unit
    @pytest.mark.llm
    def test_get_primary_llm_unsupported_provider(self, real_llm_config):
        config = {
            'llm': {
                'primary_llm': 'unsupported',
                'llms': {
                    'unsupported': {
                'provider': 'unsupported_provider',
                'api_key_env_var': 'TEST_API_KEY',
                        'model_name': 'test-model',
                        'tokenizer_model_name': 'gpt2',
                    }
                }
            }
        }
        factory = LlmManager(config)
        with pytest.raises(ValueError, match="Unsupported LLM provider"):
            factory.get_primary_llm()
    
    @pytest.mark.unit
    @pytest.mark.llm
    def test_get_llm_with_fallback_success(self, real_llm_config):
        factory = LlmManager(real_llm_config)
        llm = factory.get_llm_with_fallback()
        assert hasattr(llm, 'invoke')
        # Accept .model or .model_name
        model_val = getattr(llm, 'model', None) or getattr(llm, 'model_name', None)
        llm_section = real_llm_config.get("llm", real_llm_config)
        primary_key = llm_section.get("primary_llm")
        llms = llm_section.get("llms") or real_llm_config.get("llms")
        assert model_val == llms[primary_key]['model_name']
    
    @pytest.mark.unit
    @pytest.mark.llm
    def test_llm_factory_with_custom_env_file(self, real_llm_config):
        factory = LlmManager(real_llm_config, "config/.env")
        assert factory.config == real_llm_config
    
    @pytest.mark.unit
    @pytest.mark.llm
    def test_llm_factory_environment_variables(self, real_llm_config):
        # Ensure environment variables are loaded
        assert os.getenv('GOOGLE_API_KEY') is not None or os.getenv('GROQ_API_KEY') is not None or os.getenv('HUGGINGFACE_API_KEY') is not None
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        assert hasattr(llm, 'invoke')
        model_val = getattr(llm, 'model', None) or getattr(llm, 'model_name', None)
        llm_section = real_llm_config.get("llm", real_llm_config)
        primary_key = llm_section.get("primary_llm")
        llms = llm_section.get("llms") or real_llm_config.get("llms")
        assert model_val == llms[primary_key]['model_name']
    
    @pytest.mark.unit
    def test_transformers_tokenizer_count(self, real_llm_config):
        from core.utils.llm_manager import LlmManager
        import time
        prompt = "Hello, how are you?"
        response = "I'm fine, thank you!"
        start = time.time()
        tokens_prompt = LlmManager.transformers_count_tokens(prompt, model_name="gpt2")
        tokens_response = LlmManager.transformers_count_tokens(response, model_name="gpt2")
        elapsed = time.time() - start
        print(f"[TEST] Transformers tokenizer loaded and counted in {elapsed:.2f}s. Prompt tokens: {tokens_prompt}, Response tokens: {tokens_response}")
        assert tokens_prompt > 0
        assert tokens_response > 0
        assert elapsed < 10

    @pytest.mark.unit
    @pytest.mark.llm
    def test_get_primary_llm_groq_missing_api_key(self, real_llm_config):
        groq_config = {
            'llm': {
                'primary_llm': 'groq',
                'llms': {
                    'groq': {
                        'provider': 'groq',
                        'api_key_env_var': 'NON_EXISTENT_GROQ_KEY',
                        'model_name': 'qwen-qwq-32b',
                        'tokenizer_model_name': 'gpt2',
                    }
                }
            }
        }
        factory = LlmManager(groq_config)
        with pytest.raises(ValueError, match="API key for groq not found"):
            factory.get_primary_llm()

    @pytest.mark.unit
    @pytest.mark.llm
    def test_get_primary_llm_groq_unsupported_model(self, real_llm_config):
        if os.getenv('GROQ_API_KEY') is None:
            pytest.skip("GROQ_API_KEY not set in environment.")
        groq_config = {
            'llm': {
                'primary_llm': 'groq',
                'llms': {
                    'groq': {
                        'provider': 'groq',
                        'api_key_env_var': 'GROQ_API_KEY',
                        'model_name': 'nonexistent-model-1234',
                        'tokenizer_model_name': 'gpt2',
                    }
                }
            }
        }
        factory = LlmManager(groq_config)
        llm = factory.get_primary_llm()
        with pytest.raises(Exception):
            llm.invoke("Hello?") 