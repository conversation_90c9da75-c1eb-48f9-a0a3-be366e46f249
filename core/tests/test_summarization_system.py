#!/usr/bin/env python3
"""
Test script for the conversation summarization system
Tests the SummarizationAgent, enhanced MemoryManager, and integration with Orchestrator
"""

import json
import sys
import os

# Add the project root to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..')
sys.path.insert(0, project_root)

from core.utils.config_loader import Config<PERSON>oader
from core.utils.llm_manager import LlmManager
from core.agents.orchestrator import OrchestratorAgent
from core.agents.summarization_agent import SummarizationAgent
from core.utils.memory_manager import MemoryManager

def test_summarization_system():
    """Test the complete summarization system"""
    
    try:
        # Load configuration
        config_loader = ConfigLoader()
        llm_config = config_loader.load_config("config/llm_config.yaml")["llm"]
        # Expand env vars in primary_llm and fallback_llm
        for key in ["primary_llm", "fallback_llm"]:
            val = llm_config.get(key)
            if isinstance(val, str) and val.startswith("${") and val.endswith("}"):
                env_key = val[2:-1]
                llm_config[key] = os.environ.get(env_key, val)
        agents_config = config_loader.load_config("config/agents.yaml")["agents"]
        seller_config = config_loader.load_config("config/seller_config.yaml")["seller"]
        
        # Initialize LLM
        llm_factory = LlmManager(llm_config)
        llm = llm_factory.get_llm_with_fallback()
        # Log the LLM being used
        llm_config_full = llm_factory.config
        llm_section = llm_config_full.get("llm", llm_config_full)
        primary_key = llm_section.get("primary_llm")
        llms = llm_section.get("llms") or llm_config_full.get("llms")
        if llms and primary_key in llms:
            provider = llms[primary_key].get("provider")
            model = llms[primary_key].get("model_name")
            print(f"🔍 Using LLM: provider={provider}, model={model}")
        else:
            print(f"🔍 Using LLM: class={getattr(llm, '__class__', type(llm)).__name__}, model={getattr(llm, 'model_name', None)}")
        
        # Initialize components
        orchestrator = OrchestratorAgent(agents_config.get('orchestrator', {}), llm)
        summarization_agent = SummarizationAgent(agents_config['summarization_agent'], llm)
        memory_manager = MemoryManager()
        
        # Test customer ID
        test_customer_id = "test_summarization_customer"
        
        # Clear any existing memory
        memory_manager.clear_memory(test_customer_id)
        
        print("🧪 Testing Conversation Summarization System...")
        print("=" * 80)
        
        # Test conversation flow that should trigger summarization
        test_conversations = [
            {
                "customer_id": test_customer_id,
                "query": "Quais smartphones vocês têm disponíveis?",
                "description": "First interaction - product inquiry"
            },
            {
                "customer_id": test_customer_id,
                "query": "Fale-me mais sobre o primeiro da lista",
                "description": "Follow-up - detailed product info"
            },
            {
                "customer_id": test_customer_id,
                "query": "Obrigado pela ajuda, vou comprar",
                "description": "End of conversation - should trigger summarization"
            }
        ]
        
        print("\n🔄 Running conversation flow...")
        for i, conversation in enumerate(test_conversations, 1):
            print(f"\n📝 Conversation {i}/3: {conversation['description']}")
            print(f"❓ Query: {conversation['query']}")
            print("-" * 60)
            
            try:
                response = orchestrator.execute(conversation)
                
                if response.get('success', False):
                    print("✅ Response received:")
                    print(f"🤖 Task Type: {response.get('task_type', 'Unknown')}")
                    print(f"🔄 Is Follow-up: {response.get('is_follow_up', 'Unknown')}")
                    print(f"🔧 Agents Executed: {', '.join(response.get('agents_executed', []))}")
                    print(f"💬 Final Response: {response.get('final_response', 'No response')[:150]}...")
                    
                    # Check if summarization was triggered
                    if response.get('final_response', '').find('Resumo da conversa salvo') != -1:
                        print("✅ Conversation summarization triggered!")
                    elif i == 3:  # Last conversation should trigger summarization
                        print("⚠️  Summarization not triggered in final conversation")
                else:
                    print(f"❌ Error: {response.get('error', 'Unknown error')}")
                
            except Exception as e:
                print(f"❌ Exception: {str(e)}")
        
        # Test LTM retrieval and context
        print(f"\n📊 Testing LTM and Context Retrieval...")
        print("-" * 60)
        
        # Get conversation context
        summary_settings = seller_config.get('summary_settings', {})
        conversation_context = memory_manager.get_conversation_context(test_customer_id, summary_settings)
        
        print(f"📋 Retrieved {len(conversation_context)} conversation summaries:")
        for i, interaction in enumerate(conversation_context, 1):
            print(f"  {i}. {interaction.get('interaction_type', 'unknown')}: {interaction.get('summary', 'N/A')[:100]}...")
        
        # Get relationship context
        relationship_context = memory_manager.get_customer_relationship_context(test_customer_id, summary_settings)
        
        print(f"\n👤 Customer Relationship Context:")
        print(f"  Status: {relationship_context.get('relationship_status', 'unknown')}")
        print(f"  Total Interactions: {relationship_context.get('total_interactions', 0)}")
        print(f"  Preferences: {relationship_context.get('preferences', [])}")
        print(f"  Common Intents: {relationship_context.get('common_intents', [])}")
        
        # Test different privacy levels
        print(f"\n🔒 Testing Privacy Levels...")
        print("-" * 60)
        
        privacy_levels = ['minimal', 'standard', 'detailed']
        for privacy_level in privacy_levels:
            print(f"\n📋 Testing {privacy_level} privacy level:")
            
            # Create test summary with different privacy levels
            test_summary = {
                'conversation_id': f'test_conv_{privacy_level}',
                'customer_id': test_customer_id,
                'interaction_type': 'product_inquiry',
                'products_discussed': [
                    {
                        'id': 'smartphone_001',
                        'name': 'Samsung Galaxy S21',
                        'category': 'smartphone',
                        'price': 'R$ 2.999,00',
                        'description': 'Smartphone Samsung Galaxy S21'
                    }
                ],
                'key_queries': ['Quais smartphones?'],
                'customer_intent': 'product_research',
                'resolution': 'customer_satisfied',
                'summary': 'Cliente pesquisou smartphones Galaxy',
                'follow_up_required': False,
                'next_action': 'none',
                'customer_insights': {
                    'preferences': ['smartphones', 'Samsung'],
                    'concerns': ['price'],
                    'budget_indicator': 'medium'
                }
            }
            
            # Filter based on privacy level
            filtered_summary = memory_manager._filter_interaction_by_privacy(test_summary, privacy_level)
            
            print(f"  Original fields: {list(test_summary.keys())}")
            print(f"  Filtered fields: {list(filtered_summary.keys())}")
            
            # Check if pricing is included based on privacy level
            if 'products_discussed' in filtered_summary:
                products = filtered_summary['products_discussed']
                if products and isinstance(products[0], dict):
                    has_price = 'price' in products[0]
                    has_price_range = 'price_range' in products[0]
                    print(f"  Pricing info: {'price' if has_price else 'price_range' if has_price_range else 'none'}")
        
        # Test direct summarization agent
        print(f"\n🤖 Testing Direct SummarizationAgent...")
        print("-" * 60)
        
        # Create test conversation data
        test_conversation_data = {
            'customer_id': test_customer_id,
            'query': 'Quais smartphones vocês têm?',
            'task_type': 'product_query',
            'is_follow_up': False,
            'final_response': 'Temos Samsung Galaxy S21 e iPhone 13 disponíveis. Posso ajudar com mais detalhes?',
            'product_info': [
                {
                    'product': {
                        'id': 'smartphone_001',
                        'name': 'Samsung Galaxy S21',
                        'category': 'smartphone',
                        'price': 'R$ 2.999,00',
                        'description': 'Smartphone Samsung Galaxy S21'
                    }
                }
            ],
            'agents_executed': ['product_specialist', 'response_generator'],
            'reasoning': 'Product inquiry detected, both agents required'
        }
        
        # Test summarization with different detail levels
        detail_levels = ['low', 'medium', 'high']
        for detail_level in detail_levels:
            print(f"\n📝 Testing {detail_level} detail level:")
            
            # Create test config
            test_config = {
                'detail_level': detail_level,
                'privacy_level': 'standard',
                'max_summary_length': 200
            }
            
            try:
                summary = summarization_agent.generate_conversation_summary(test_conversation_data, test_config)
                
                if summary:
                    print(f"  ✅ Summary generated: {summary.get('summary', 'N/A')[:100]}...")
                    print(f"  📊 Detail level: {summary.get('detail_level', 'unknown')}")
                    print(f"  🔒 Privacy level: {summary.get('privacy_level', 'unknown')}")
                    print(f"  📦 Products: {len(summary.get('products_discussed', []))}")
                else:
                    print(f"  ❌ Failed to generate summary")
                    
            except Exception as e:
                print(f"  ❌ Error: {str(e)}")
        
        print(f"\n" + "=" * 80)
        print("🎉 Summarization system test completed!")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_summarization_system() 