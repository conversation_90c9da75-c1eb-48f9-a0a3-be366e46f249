import pytest
import os
from dotenv import load_dotenv
from core.utils.llm_manager import LlmManager
from core.agents.test_agent import LLMTestAgent
from core.utils.config_loader import ConfigLoader

# Load environment variables from .env file
load_dotenv("config/.env")

class TestRealAPI:
    """Integration tests for real API connections"""
    
    @pytest.fixture(scope="class")
    def real_llm_config(self):
        """Load real LLM configuration from config files"""
        config_loader = ConfigLoader()
        return config_loader.get_llm_config("config/llm_config.yaml")
    
    @pytest.fixture(scope="class")
    def real_agent_config(self):
        """Create real agent configuration for testing"""
        return {
            'id': 'real_test_agent',
            'role': 'Real Test Agent',
            'system_prompt': 'You are a test agent for real API testing. Provide accurate and helpful responses.',
            'max_tokens': 150,
            'temperature': 0.3
        }
    
    @pytest.mark.integration
    @pytest.mark.real_api
    @pytest.mark.slow
    def test_real_google_gemini_connection(self, real_llm_config, real_agent_config):
        """Test real connection to Google Gemini API"""
        # Check if Google API key is available
        if not os.getenv('GOOGLE_API_KEY'):
            pytest.skip("GOOGLE_API_KEY not available in environment")
        
        try:
            # Initialize LLM factory with real config
            factory = LlmManager(real_llm_config)
            llm = factory.get_primary_llm()
            
            # Initialize test agent
            agent = LLMTestAgent(real_agent_config, llm)
            
            # Test basic connectivity
            result = agent.test_llm_connectivity()
            
            assert result['test_type'] == 'connectivity'
            assert result['success'] is True
            assert result['agent_id'] == 'real_test_agent'
            assert 'token_usage' in result
            
            print(f"Google Gemini Response: {result['response']}")
            
        except Exception as e:
            pytest.fail(f"Google Gemini API test failed: {str(e)}")
    
    @pytest.mark.integration
    @pytest.mark.real_api
    @pytest.mark.slow
    def test_real_mistral_connection(self, real_llm_config, real_agent_config):
        """Test real connection to Mistral API"""
        # Check if Mistral API key is available
        if not os.getenv('HUGGINGFACE_API_KEY'):
            pytest.skip("HUGGINGFACE_API_KEY not available in environment")
        
        try:
            # Initialize LLM factory with real config
            factory = LlmManager(real_llm_config)
            llm = factory.get_fallback_llm()
            
            # Initialize test agent
            agent = LLMTestAgent(real_agent_config, llm)
            
            # Test basic connectivity
            result = agent.test_llm_connectivity()
            
            assert result['test_type'] == 'connectivity'
            assert result['success'] is True
            assert result['agent_id'] == 'real_test_agent'
            assert 'token_usage' in result
            
            print(f"Mistral Response: {result['response']}")
            
        except Exception as e:
            pytest.fail(f"Mistral API test failed: {str(e)}")
    
    @pytest.mark.integration
    @pytest.mark.real_api
    @pytest.mark.slow
    def test_real_llm_fallback_mechanism(self, real_llm_config, real_agent_config):
        """Test real LLM fallback mechanism"""
        # Check if at least one API key is available
        if not (os.getenv('GOOGLE_API_KEY') or os.getenv('HUGGINGFACE_API_KEY')):
            pytest.skip("No API keys available for fallback testing")
        
        try:
            # Initialize LLM factory with real config
            factory = LlmManager(real_llm_config)
            llm = factory.get_llm_with_fallback()
            
            # Initialize test agent
            agent = LLMTestAgent(real_agent_config, llm)
            
            # Test basic connectivity
            result = agent.test_llm_connectivity()
            
            assert result['test_type'] == 'connectivity'
            assert result['success'] is True
            assert result['agent_id'] == 'real_test_agent'
            assert 'token_usage' in result
            
            print(f"Fallback LLM Response: {result['response']}")
            
        except Exception as e:
            pytest.fail(f"LLM fallback test failed: {str(e)}")
    
    @pytest.mark.integration
    @pytest.mark.real_api
    @pytest.mark.slow
    def test_real_llm_tasks(self, real_llm_config, real_agent_config):
        """Test real LLM with various tasks"""
        # Check if at least one API key is available
        if not (os.getenv('GOOGLE_API_KEY') or os.getenv('HUGGINGFACE_API_KEY')):
            pytest.skip("No API keys available for task testing")
        
        try:
            # Initialize LLM factory with real config
            factory = LlmManager(real_llm_config)
            llm = factory.get_llm_with_fallback()
            
            # Initialize test agent
            agent = LLMTestAgent(real_agent_config, llm)
            
            # Test echo task
            echo_result = agent.execute({
                'task': 'echo',
                'text': 'Hello from real API test'
            })
            
            assert echo_result['task'] == 'echo'
            assert echo_result['input_text'] == 'Hello from real API test'
            assert echo_result['agent_id'] == 'real_test_agent'
            assert 'token_usage' in echo_result
            print(f"Echo Response: {echo_result['response']}")
            
            # Test translate task
            translate_result = agent.execute({
                'task': 'translate',
                'text': 'Hello World'
            })
            
            assert translate_result['task'] == 'translate'
            assert translate_result['input_text'] == 'Hello World'
            assert translate_result['agent_id'] == 'real_test_agent'
            assert 'token_usage' in translate_result
            print(f"Translate Response: {translate_result['response']}")
            
            # Test summarize task
            summarize_result = agent.execute({
                'task': 'summarize',
                'text': 'This is a longer text that should be summarized by the LLM. It contains multiple sentences and should demonstrate the summarization capability of the language model.'
            })
            
            assert summarize_result['task'] == 'summarize'
            assert summarize_result['agent_id'] == 'real_test_agent'
            assert 'token_usage' in summarize_result
            print(f"Summarize Response: {summarize_result['response']}")
            
        except Exception as e:
            pytest.fail(f"Real LLM tasks test failed: {str(e)}")
    
    @pytest.mark.integration
    @pytest.mark.real_api
    @pytest.mark.slow
    def test_real_llm_response_quality(self, real_llm_config, real_agent_config):
        """Test real LLM response quality"""
        # Check if at least one API key is available
        if not (os.getenv('GOOGLE_API_KEY') or os.getenv('HUGGINGFACE_API_KEY')):
            pytest.skip("No API keys available for quality testing")
        
        try:
            # Initialize LLM factory with real config
            factory = LlmManager(real_llm_config)
            llm = factory.get_llm_with_fallback()
            
            # Initialize test agent
            agent = LLMTestAgent(real_agent_config, llm)
            
            # Test quality with various prompts
            test_cases = [
                {
                    'input': 'What is the capital of France?',
                    'expected_keywords': ['Paris', 'France', 'capital']
                },
                {
                    'input': 'Explain what is artificial intelligence',
                    'expected_keywords': ['artificial', 'intelligence', 'AI', 'machine', 'learning']
                },
                {
                    'input': 'What is 2 + 2?',
                    'expected_keywords': ['4', 'four', 'equals', 'sum']
                }
            ]
            
            result = agent.test_response_quality(test_cases)
            
            assert result['test_type'] == 'quality'
            assert result['total_tests'] == 3
            assert result['passed_tests'] >= 2  # At least 2 out of 3 should pass
            assert result['agent_id'] == 'real_test_agent'
            assert 'token_usage' in result
            
            print(f"Quality Test Results: {result['passed_tests']}/{result['total_tests']} passed")
            for i, test_result in enumerate(result['results']):
                print(f"Test {i+1}: {'PASS' if test_result['success'] else 'FAIL'}")
                if test_result['success']:
                    print(f"  Matched keywords: {test_result['matched_keywords']}")
                else:
                    print(f"  Error: {test_result.get('error', 'No keywords matched')}")
            
        except Exception as e:
            pytest.fail(f"Real LLM quality test failed: {str(e)}")
    
    @pytest.mark.integration
    @pytest.mark.real_api
    @pytest.mark.slow
    def test_real_llm_token_tracking(self, real_llm_config, real_agent_config):
        """Test real LLM token tracking"""
        # Check if at least one API key is available
        if not (os.getenv('GOOGLE_API_KEY') or os.getenv('HUGGINGFACE_API_KEY')):
            pytest.skip("No API keys available for token tracking testing")
        
        try:
            # Initialize LLM factory with real config
            factory = LlmManager(real_llm_config)
            llm = factory.get_llm_with_fallback()
            
            # Initialize test agent
            agent = LLMTestAgent(real_agent_config, llm)
            
            # Execute multiple tasks to test token tracking
            initial_usage = agent.get_token_usage()
            
            # Execute first task
            result1 = agent.execute({
                'task': 'echo',
                'text': 'First test message'
            })
            
            usage_after_first = agent.get_token_usage()
            
            # Execute second task
            result2 = agent.execute({
                'task': 'translate',
                'text': 'Second test message'
            })
            
            usage_after_second = agent.get_token_usage()
            
            # Verify token usage is tracked
            assert 'token_usage' in result1
            assert 'token_usage' in result2
            assert 'real_test_agent' in usage_after_second.get('agent_breakdown', {})
            
            # Verify token counts increase
            agent_usage = usage_after_second['agent_breakdown']['real_test_agent']
            assert agent_usage['calls'] >= 2
            
            print(f"Token Usage After First Call: {usage_after_first}")
            print(f"Token Usage After Second Call: {usage_after_second}")
            
        except Exception as e:
            pytest.fail(f"Real LLM token tracking test failed: {str(e)}") 