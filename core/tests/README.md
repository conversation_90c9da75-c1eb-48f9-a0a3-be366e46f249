# LangChainAgent Testing Suite

This directory contains comprehensive tests for the LangChainAgent project, focusing on testing the LLM factory functionality and agent integration.

## Test Structure

### Test Files

- **`conftest.py`**: Pytest configuration and common fixtures
- **`test_llm_factory.py`**: Unit tests for LLM factory functionality
- **`test_agent.py`**: Unit tests for the TestAgent class
- **`test_integration.py`**: Integration tests for LLM factory with TestAgent

### Test Agent

The `TestAgent` class (located in `../agents/test_agent.py`) is a custom agent designed specifically for testing LLM functionality. It provides:

- **Basic Tasks**: echo, translate, summarize, count_words
- **Connectivity Testing**: Verify LLM connectivity
- **Quality Testing**: Test response quality with expected keywords
- **Token Tracking**: Monitor token usage across operations

## Running Tests

### Using the Test Runner Script

The easiest way to run tests is using the `run_tests.py` script in the project root:

```bash
# Run all tests
python run_tests.py

# Run only unit tests
python run_tests.py --type unit

# Run only integration tests
python run_tests.py --type integration

# Run only LLM-related tests
python run_tests.py --type llm

# Run only agent-related tests
python run_tests.py --type agent

# Run with coverage report
python run_tests.py --coverage

# Run with verbose output
python run_tests.py --verbose

# Run only fast tests (skip integration)
python run_tests.py --fast
```

### Using Pytest Directly

You can also run tests directly with pytest:

```bash
# Run all tests
pytest core/tests/

# Run specific test file
pytest core/tests/test_llm_factory.py

# Run tests with specific markers
pytest core/tests/ -m unit
pytest core/tests/ -m integration
pytest core/tests/ -m llm
pytest core/tests/ -m agent

# Run with coverage
pytest core/tests/ --cov=core --cov-report=html
```

## Test Categories

### Unit Tests (`@pytest.mark.unit`)

Test individual components in isolation:

- **LLM Factory Tests**: Test provider initialization, configuration, error handling
- **TestAgent Tests**: Test agent functionality, task execution, validation
- **Mock-based**: Use mocked LLM instances for fast, reliable testing

### Integration Tests (`@pytest.mark.integration`)

Test component interactions:

- **LLM Factory + TestAgent**: End-to-end testing of LLM integration
- **Fallback Mechanisms**: Test primary/fallback LLM switching
- **Token Tracking**: Verify token usage across multiple operations
- **Error Handling**: Test error scenarios and recovery

### LLM Tests (`@pytest.mark.llm`)

Specifically test LLM-related functionality:

- Provider initialization (Google Gemini, Mistral, OpenAI)
- API key management
- Fallback mechanisms
- Configuration validation

### Agent Tests (`@pytest.mark.agent`)

Test agent-specific functionality:

- Task execution
- Response validation
- Token tracking
- Error handling

## Test Fixtures

Common test fixtures are defined in `conftest.py`:

- **`mock_llm_config`**: Mock LLM configuration
- **`mock_agent_config`**: Mock agent configuration
- **`mock_llm`**: Mock LLM instance
- **`temp_env_file`**: Temporary environment file
- **`temp_memory_dirs`**: Temporary memory directories
- **`sample_conversation_data`**: Sample conversation data

## Test Coverage

The test suite covers:

### LLM Factory
- ✅ Provider initialization (Google Gemini, Mistral, OpenAI)
- ✅ Configuration loading and validation
- ✅ API key management
- ✅ Fallback mechanisms
- ✅ Error handling for missing configs/keys
- ✅ Environment variable handling

### TestAgent
- ✅ Agent initialization and configuration
- ✅ Task execution (echo, translate, summarize, count_words)
- ✅ Response validation
- ✅ LLM connectivity testing
- ✅ Response quality testing
- ✅ Token usage tracking
- ✅ Error handling

### Integration
- ✅ LLM factory with TestAgent integration
- ✅ Multiple agent instances
- ✅ Token tracking across operations
- ✅ Fallback mechanism testing
- ✅ Environment variable scenarios

## Adding New Tests

### For LLM Factory

1. Add test methods to `TestLLMFactory` class
2. Use appropriate markers (`@pytest.mark.unit`, `@pytest.mark.llm`)
3. Mock external dependencies (LLM providers)
4. Test both success and failure scenarios

### For TestAgent

1. Add test methods to `TestTestAgent` class
2. Use appropriate markers (`@pytest.mark.unit`, `@pytest.mark.agent`)
3. Test different task types and validation scenarios
4. Verify token tracking functionality

### For Integration

1. Add test methods to `TestLLMIntegration` class
2. Use `@pytest.mark.integration` marker
3. Test real component interactions
4. Verify end-to-end functionality

## Best Practices

1. **Use Mocks**: Mock external dependencies for fast, reliable tests
2. **Test Edge Cases**: Include error scenarios and boundary conditions
3. **Use Fixtures**: Leverage common fixtures for consistent test data
4. **Clear Assertions**: Make test assertions clear and specific
5. **Descriptive Names**: Use descriptive test method names
6. **Documentation**: Include docstrings explaining test purpose

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure you're running tests from the project root
2. **Missing Dependencies**: Install test dependencies: `pip install pytest pytest-mock pytest-cov responses`
3. **Path Issues**: Use absolute paths or run from project root
4. **Mock Issues**: Ensure mocks are properly configured for your test scenario

### Debug Mode

Run tests with verbose output for debugging:

```bash
python run_tests.py --verbose
pytest core/tests/ -v -s
```

## Coverage Reports

Generate coverage reports to identify untested code:

```bash
python run_tests.py --coverage
```

This will generate:
- Terminal coverage report
- HTML coverage report in `htmlcov/` directory

## Continuous Integration

The test suite is designed to work with CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Run Tests
  run: |
    pip install -r requirements.txt
    python run_tests.py --coverage
``` 