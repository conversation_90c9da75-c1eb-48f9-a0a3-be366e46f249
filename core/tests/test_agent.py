import pytest
import os
from dotenv import load_dotenv
from core.utils.llm_manager import LlmManager
from core.agents.test_agent import LLMTestAgent
from core.utils.config_loader import ConfigLoader

# Load environment variables from .env file
load_dotenv("config/.env")

@pytest.fixture(scope="module")
def real_llm_config():
    config_loader = ConfigLoader()
    return config_loader.get_llm_config("config/llm_config.yaml")

@pytest.fixture(scope="module")
def real_agent_config():
    return {
        'id': 'test_agent',
        'role': 'Test Agent',
        'system_prompt': "You are a test agent. Respond with 'Test response'.",
        'max_tokens': 100
    }

class TestTestAgent:
    @pytest.mark.unit
    @pytest.mark.agent
    def test_test_agent_echo_task(self, real_llm_config, real_agent_config):
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        agent = LLMTestAgent(real_agent_config, llm)
        result = agent.execute({'task': 'echo', 'text': 'Hello World'})
        assert 'Hello World' in result['response']

    @pytest.mark.unit
    @pytest.mark.agent
    def test_test_agent_translate_task(self, real_llm_config, real_agent_config):
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        agent = LLMTestAgent(real_agent_config, llm)
        result = agent.execute({'task': 'translate', 'text': 'Hello'})
        assert any(word in result['response'].lower() for word in ['olá', 'ola'])

    @pytest.mark.unit
    @pytest.mark.agent
    def test_test_agent_summarize_task(self, real_llm_config, real_agent_config):
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        agent = LLMTestAgent(real_agent_config, llm)
        result = agent.execute({'task': 'summarize', 'text': 'This is a long text that needs to be summarized.'})
        assert 'summar' in result['response'].lower()

    @pytest.mark.unit
    @pytest.mark.agent
    def test_test_agent_count_words_task(self, real_llm_config, real_agent_config):
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        agent = LLMTestAgent(real_agent_config, llm)
        result = agent.execute({'task': 'count_words', 'text': 'This text has five words'})
        assert result['response'].strip().isdigit()

    @pytest.mark.unit
    @pytest.mark.agent
    def test_test_agent_custom_task(self, real_llm_config, real_agent_config):
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        agent = LLMTestAgent(real_agent_config, llm)
        result = agent.execute({'task': 'custom_task', 'text': 'Custom input'})
        assert isinstance(result['response'], str)

    @pytest.mark.unit
    @pytest.mark.agent
    def test_test_agent_with_validation(self, real_llm_config, real_agent_config):
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        agent = LLMTestAgent(real_agent_config, llm)
        result = agent.execute({'task': 'echo', 'text': 'Test text', 'expected_response': 'Test text'})
        assert result['validation']['matches'] is True

    @pytest.mark.unit
    @pytest.mark.agent
    def test_test_agent_validation_failure(self, real_llm_config, real_agent_config):
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        agent = LLMTestAgent(real_agent_config, llm)
        result = agent.execute({'task': 'echo', 'text': 'Test text', 'expected_response': 'Expected response'})
        assert result['validation']['matches'] is False 