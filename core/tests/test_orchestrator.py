#!/usr/bin/env python3
"""
Test script for the optimized orchestrator functionality
"""

import json
import sys
import os

# Add the project root to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..')
sys.path.insert(0, project_root)

from core.utils.config_loader import ConfigLoader
from core.utils.llm_manager import LlmManager
from core.agents.orchestrator import OrchestratorAgent

def test_orchestrator():
    """Test the optimized orchestrator with different task types"""
    
    try:
        # Load configuration
        config_loader = ConfigLoader()
        llm_config = config_loader.load_config("config/llm_config.yaml")["llm"]
        agents_config = config_loader.load_config("config/agents.yaml")["agents"]
        
        # Initialize LLM
        llm_factory = LlmManager(llm_config)
        llm = llm_factory.get_llm_with_fallback()
        
        # Initialize orchestrator with optimized config
        orchestrator_config = agents_config.get('orchestrator', {})
        orchestrator_config['max_tokens'] = 500  # Conservative limit for testing
        orchestrator = OrchestratorAgent(orchestrator_config, llm)
        
        # Test queries for different task types
        test_cases = [
            {
                "customer_id": "test_customer_1",
                "query": "Quais smartphones vocês têm disponíveis?",
                "expected_task": "product_query"
            },
            {
                "customer_id": "test_customer_2", 
                "query": "Qual o horário de funcionamento da loja?",
                "expected_task": "store_info"
            },
            {
                "customer_id": "test_customer_3",
                "query": "Preciso de ajuda com um produto defeituoso",
                "expected_task": "support"
            },
            {
                "customer_id": "test_customer_1",
                "query": "Obrigado pela ajuda anterior",
                "expected_task": "general"
            },
            {
                "customer_id": "test_customer_4",
                "query": "Preciso de mais detalhes sobre o primeiro produto",
                "expected_task": "product_query"
            }
        ]
        
        print("🧪 Testing Optimized Orchestrator Agent...")
        print("=" * 70)
        
        total_tokens = 0
        total_time = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 Test Case {i}/5:")
            print(f"👤 Customer ID: {test_case['customer_id']}")
            print(f"❓ Query: {test_case['query']}")
            print(f"🎯 Expected Task: {test_case['expected_task']}")
            print("-" * 50)
            
            try:
                response = orchestrator.execute(test_case)
                
                if response.get('success', False):
                    print("✅ Response received:")
                    print(f"🤖 Task Type: {response.get('task_type', 'Unknown')}")
                    print(f"🔧 Agents Used: {', '.join(response.get('agents_executed', []))}")
                    print(f"💬 Final Response: {response.get('final_response', 'No response')[:100]}...")
                    print(f"🔢 Tokens Used: {response.get('tokens_used', 0)}/{response.get('tokens_budget', 0)}")
                    print(f"⏱️  Execution Time: {response.get('execution_time', 0):.3f}s")
                    
                    # Track metrics
                    total_tokens += response.get('tokens_used', 0)
                    total_time += response.get('execution_time', 0)
                    
                    # Check if task classification is working
                    actual_task = response.get('task_type', 'unknown')
                    expected_task = test_case['expected_task']
                    if actual_task == expected_task:
                        print(f"✅ Task classification: CORRECT ({actual_task})")
                    else:
                        print(f"⚠️  Task classification: MISMATCH (expected: {expected_task}, got: {actual_task})")
                    
                else:
                    print(f"❌ Error: {response.get('error', 'Unknown error')}")
                
            except Exception as e:
                print(f"❌ Exception: {str(e)}")
        
        # Performance summary
        print(f"\n" + "=" * 70)
        print("📈 PERFORMANCE SUMMARY")
        print("=" * 70)
        print(f"🔢 Total Tokens Used: {total_tokens}")
        print(f"⏱️  Total Execution Time: {total_time:.3f}s")
        print(f"📊 Average Tokens per Query: {total_tokens/len(test_cases):.1f}")
        print(f"⚡ Average Time per Query: {total_time/len(test_cases):.3f}s")
        
        if total_tokens > 0:
            efficiency = max(0, 100 - (total_tokens / (500 * len(test_cases)) * 100))
            print(f"🎯 Token Efficiency: {efficiency:.1f}%")
        
        print("\n🎉 Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_orchestrator() 