#!/usr/bin/env python3
"""
Test script for the refactored conversation flow
Tests the proper agent selection and detail levels for first turns vs follow-ups
"""

import json
import sys
import os

# Add the project root to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..')
sys.path.insert(0, project_root)

from core.utils.config_loader import Config<PERSON>oa<PERSON>
from core.utils.llm_manager import LlmManager
from core.agents.orchestrator import OrchestratorAgent

def test_conversation_flow():
    """Test the refactored conversation flow with proper agent selection"""
    
    try:
        # Load configuration
        config_loader = ConfigLoader()
        llm_config = config_loader.load_config("config/llm_config.yaml")["llm"]
        agents_config = config_loader.load_config("config/agents.yaml")["agents"]
        
        # Initialize LLM
        llm_factory = LlmManager(llm_config)
        llm = llm_factory.get_llm_with_fallback()
        
        # Initialize orchestrator
        orchestrator_config = agents_config.get('orchestrator', {})
        orchestrator_config['max_tokens'] = 1000  # Increased for detailed responses
        orchestrator = OrchestratorAgent(orchestrator_config, llm)
        
        # Test conversation flow
        conversation_turns = [
            {
                "customer_id": "test_customer_flow",
                "query": "Quais smartphones vocês têm disponíveis?",
                "expected_agents": ["product_specialist", "response_generator"],
                "expected_detail": "basic",
                "description": "First turn - new product query"
            },
            {
                "customer_id": "test_customer_flow",
                "query": "Fale-me mais sobre o primeiro da lista",
                "expected_agents": ["response_generator"],
                "expected_detail": "detailed",
                "description": "Follow-up - detailed info about specific product"
            },
            {
                "customer_id": "test_customer_flow", 
                "query": "Ele tem 5G?",
                "expected_agents": ["response_generator"],
                "expected_detail": "detailed",
                "description": "Follow-up - specific feature question"
            },
            {
                "customer_id": "test_customer_flow",
                "query": "Quero ver notebooks",
                "expected_agents": ["product_specialist", "response_generator"],
                "expected_detail": "basic",
                "description": "New product category - restart process"
            },
            {
                "customer_id": "test_customer_flow",
                "query": "Obrigado pela ajuda",
                "expected_agents": ["response_generator"],
                "expected_detail": "general",
                "description": "End of conversation"
            },
            {
                "customer_id": "test_customer_flow",
                "query": "Qual o endereço da loja?",
                "expected_agents": ["response_generator"],
                "expected_detail": "store_info",
                "description": "Store info - address"
            },
            {
                "customer_id": "test_customer_flow",
                "query": "Quais são os horários de funcionamento?",
                "expected_agents": ["response_generator"],
                "expected_detail": "store_info",
                "description": "Store info - opening hours"
            },
            {
                "customer_id": "test_customer_flow",
                "query": "Como funciona a política de devolução?",
                "expected_agents": ["response_generator"],
                "expected_detail": "store_info",
                "description": "Store info - return policy"
            },
            {
                "customer_id": "test_customer_flow",
                "query": "Quais formas de pagamento vocês aceitam?",
                "expected_agents": ["response_generator"],
                "expected_detail": "store_info",
                "description": "Store info - payment methods"
            },
            {
                "customer_id": "test_customer_flow",
                "query": "A loja é acessível para pessoas com deficiência?",
                "expected_agents": ["response_generator"],
                "expected_detail": "store_info",
                "description": "Store info - accessibility"
            }
        ]
        
        print("🧪 Testing Refactored Conversation Flow...")
        print("=" * 80)
        
        total_tokens = 0
        total_time = 0
        
        for i, turn in enumerate(conversation_turns, 1):
            print(f"\n🔄 Turn {i}/5:")
            print(f"👤 Customer ID: {turn['customer_id']}")
            print(f"❓ Query: {turn['query']}")
            print(f"📋 Description: {turn['description']}")
            print(f"🎯 Expected Agents: {turn['expected_agents']}")
            print(f"📊 Expected Detail: {turn['expected_detail']}")
            print("-" * 60)
            
            try:
                response = orchestrator.execute(turn)
                
                if response.get('success', False):
                    print("✅ Response received:")
                    print(f"🤖 Task Type: {response.get('task_type', 'Unknown')}")
                    print(f"🔄 Is Follow-up: {response.get('is_follow_up', 'Unknown')}")
                    print(f"🔧 Agents Executed: {', '.join(response.get('agents_executed', []))}")
                    print(f"💬 Final Response: {response.get('final_response', 'No response')[:150]}...")
                    print(f"🔢 Tokens Used: {response.get('tokens_used', 0)}/{response.get('tokens_budget', 0)}")
                    print(f"⏱️  Execution Time: {response.get('execution_time', 0):.3f}s")
                    
                    # Track metrics
                    total_tokens += response.get('tokens_used', 0)
                    total_time += response.get('execution_time', 0)
                    
                    # Validate agent selection
                    actual_agents = response.get('agents_executed', [])
                    expected_agents = turn['expected_agents']
                    
                    if set(actual_agents) == set(expected_agents):
                        print(f"✅ Agent selection: CORRECT ({actual_agents})")
                    else:
                        print(f"⚠️  Agent selection: MISMATCH (expected: {expected_agents}, got: {actual_agents})")
                    
                    # Validate follow-up detection
                    is_follow_up = response.get('is_follow_up', False)
                    if i == 1:
                        if not is_follow_up:
                            print(f"✅ Follow-up detection: CORRECT (first turn)")
                        else:
                            print(f"⚠️  Follow-up detection: INCORRECT (first turn marked as follow-up)")
                    elif i in [2, 3]:
                        if is_follow_up:
                            print(f"✅ Follow-up detection: CORRECT (follow-up turn)")
                        else:
                            print(f"⚠️  Follow-up detection: INCORRECT (follow-up not detected)")
                    elif i == 4:
                        if not is_follow_up:
                            print(f"✅ Follow-up detection: CORRECT (new product category)")
                        else:
                            print(f"⚠️  Follow-up detection: INCORRECT (new category marked as follow-up)")
                    
                else:
                    print(f"❌ Error: {response.get('error', 'Unknown error')}")
                
            except Exception as e:
                print(f"❌ Exception: {str(e)}")
        
        # Performance summary
        print(f"\n" + "=" * 80)
        print("📈 CONVERSATION FLOW PERFORMANCE SUMMARY")
        print("=" * 80)
        print(f"🔢 Total Tokens Used: {total_tokens}")
        print(f"⏱️  Total Execution Time: {total_time:.3f}s")
        print(f"📊 Average Tokens per Turn: {total_tokens/len(conversation_turns):.1f}")
        print(f"⚡ Average Time per Turn: {total_time/len(conversation_turns):.3f}s")
        
        if total_tokens > 0:
            efficiency = max(0, 100 - (total_tokens / (1000 * len(conversation_turns)) * 100))
            print(f"🎯 Token Efficiency: {efficiency:.1f}%")
        
        print("\n🎉 Conversation flow test completed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_conversation_flow() 