import pytest
import os
from dotenv import load_dotenv
from core.utils.llm_manager import LlmManager
from core.agents.test_agent import LLMTestAgent
from core.utils.config_loader import ConfigLoader

# Load environment variables from .env file
load_dotenv("config/.env")

@pytest.fixture(scope="module")
def real_llm_config():
    config_loader = ConfigLoader()
    return config_loader.get_llm_config("config/llm_config.yaml")

@pytest.fixture(scope="module")
def real_agent_config():
    return {
        'id': 'test_agent',
        'role': 'Test Agent',
        'system_prompt': "You are a test agent. Respond with 'Test response'.",
        'max_tokens': 100
    }

class TestLLMIntegration:
    """Integration tests for LLM factory with real agents"""
    
    @pytest.mark.integration
    @pytest.mark.llm
    def test_llm_factory_with_test_agent_google(self, real_llm_config, real_agent_config):
        """Test LLM factory integration with LLMTestAgent using Google Gemini"""
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        
        agent = LLMTestAgent(real_agent_config, llm)
        result = agent.execute({'task': 'echo', 'text': 'Hello World'})
        
        assert 'Hello World' in result['response']
        assert result['agent_id'] == 'test_agent'
        assert 'token_usage' in result
    
    @pytest.mark.integration
    @pytest.mark.llm
    def test_llm_factory_with_test_agent_mistral(self, real_llm_config, real_agent_config):
        """Test LLM factory integration with LLMTestAgent using Mistral"""
        factory = LlmManager(real_llm_config)
        llm = factory.get_fallback_llm()
        
        agent = LLMTestAgent(real_agent_config, llm)
        result = agent.execute({'task': 'echo', 'text': 'Hello World'})
        
        assert 'Hello World' in result['response']
        assert result['agent_id'] == 'test_agent'
        assert 'token_usage' in result
    
    @pytest.mark.integration
    @pytest.mark.llm
    def test_llm_factory_fallback_with_test_agent(self, real_llm_config, real_agent_config):
        """Test LLM factory fallback mechanism with test agent"""
        factory = LlmManager(real_llm_config)
        
        # Test fallback mechanism
        llm = factory.get_llm_with_fallback()
        
        agent = LLMTestAgent(real_agent_config, llm)
        result = agent.execute({'task': 'echo', 'text': 'Test fallback'})
        
        assert isinstance(result['response'], str)
        assert result['agent_id'] == 'test_agent'
        assert 'token_usage' in result
    
    @pytest.mark.integration
    @pytest.mark.llm
    def test_llm_factory_connectivity_test(self, real_llm_config, real_agent_config):
        """Test LLM factory connectivity with test agent"""
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        
        agent = LLMTestAgent(real_agent_config, llm)
        result = agent.test_llm_connectivity()
        
        assert result['test_type'] == 'connectivity'
        assert result['success'] is True
        assert result['agent_id'] == 'test_agent'
        assert 'token_usage' in result
    
    @pytest.mark.integration
    @pytest.mark.llm
    def test_llm_factory_quality_test(self, real_llm_config, real_agent_config):
        """Test LLM factory quality with test agent"""
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        
        agent = LLMTestAgent(real_agent_config, llm)
        
        test_cases = [
            {'input': 'What is AI?', 'expected_keywords': ['artificial', 'intelligence']},
            {'input': 'Explain machine learning', 'expected_keywords': ['machine', 'learning']}
        ]
        
        result = agent.test_response_quality(test_cases)
        
        assert result['test_type'] == 'quality'
        assert result['total_tests'] == 2
        assert result['passed_tests'] >= 1  # At least one should pass
        assert result['agent_id'] == 'test_agent'
        assert 'token_usage' in result
    
    @pytest.mark.integration
    @pytest.mark.llm
    def test_llm_factory_token_tracking(self, real_llm_config, real_agent_config):
        """Test LLM factory token tracking with test agent"""
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        
        agent = LLMTestAgent(real_agent_config, llm)
        result = agent.execute({'task': 'echo', 'text': 'Test token tracking'})
        
        assert 'token_usage' in result
        token_usage = agent.get_token_usage()
        assert token_usage is not None
        assert 'test_agent' in token_usage.get('agent_breakdown', {})
    
    @pytest.mark.integration
    @pytest.mark.llm
    def test_llm_factory_multiple_agents(self, real_llm_config):
        """Test LLM factory with multiple agents sharing the same LLM"""
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        
        # Create multiple agents with different configs
        agent1_config = {
            'id': 'agent1',
            'role': 'Agent 1',
            'system_prompt': 'You are agent 1.',
            'max_tokens': 100
        }
        
        agent2_config = {
            'id': 'agent2',
            'role': 'Agent 2',
            'system_prompt': 'You are agent 2.',
            'max_tokens': 100
        }
        
        agent1 = LLMTestAgent(agent1_config, llm)
        agent2 = LLMTestAgent(agent2_config, llm)
        
        # Test both agents work
        result1 = agent1.execute({'task': 'echo', 'text': 'Agent 1 test'})
        result2 = agent2.execute({'task': 'echo', 'text': 'Agent 2 test'})
        
        assert result1['agent_id'] == 'agent1'
        assert result2['agent_id'] == 'agent2'
        assert 'Agent 1 test' in result1['response']
        assert 'Agent 2 test' in result2['response']
    
    @pytest.mark.integration
    @pytest.mark.llm
    def test_llm_factory_error_handling(self, real_llm_config, real_agent_config):
        """Test LLM factory error handling with test agent"""
        factory = LlmManager(real_llm_config)
        
        # Test with invalid config
        invalid_config = {'primary_llm': {'provider': 'invalid_provider'}}
        
        with pytest.raises(ValueError, match="Unsupported LLM provider"):
            factory = LlmManager(invalid_config)
            factory.get_primary_llm()
    
    @pytest.mark.integration
    @pytest.mark.llm
    def test_llm_factory_configuration_validation(self, real_llm_config):
        """Test LLM factory configuration validation"""
        # Test missing primary LLM config
        invalid_config = {'fallback_llm': real_llm_config['fallback_llm']}
        
        with pytest.raises(ValueError, match="Primary LLM configuration not found"):
            factory = LlmManager(invalid_config)
            factory.get_primary_llm()
    
    @pytest.mark.integration
    @pytest.mark.llm
    def test_llm_factory_environment_variables(self, real_llm_config, real_agent_config):
        """Test LLM factory with environment variables"""
        # Ensure environment variables are loaded
        assert os.getenv('GOOGLE_API_KEY') is not None
        
        factory = LlmManager(real_llm_config)
        llm = factory.get_primary_llm()
        
        agent = LLMTestAgent(real_agent_config, llm)
        result = agent.execute({'task': 'echo', 'text': 'Environment test'})
        
        assert 'Environment test' in result['response']
        assert result['agent_id'] == 'test_agent' 