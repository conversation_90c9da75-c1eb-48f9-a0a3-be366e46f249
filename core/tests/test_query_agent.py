#!/usr/bin/env python3
"""
Comprehensive test script for the QueryAgent
Tests all possible scenarios including task classification, follow-up detection, 
conversation termination, and agent selection
"""

import json
import sys
import os
import unittest
from unittest.mock import Mock, patch

# Add the project root to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..')
sys.path.insert(0, project_root)

from core.utils.config_loader import ConfigLoader
from core.utils.llm_manager import LlmManager
from core.agents.query_agent import QueryAgent
from core.utils.memory_manager import MemoryManager

class TestQueryAgent(unittest.TestCase):
    """Comprehensive test suite for QueryAgent"""
    
    def setUp(self):
        """Set up test environment"""
        # Load configuration
        config_loader = ConfigLoader()
        llm_config = config_loader.load_config("config/llm_config.yaml")["llm"]
        agents_config = config_loader.load_config("config/agents.yaml")["agents"]
        
        # Initialize LLM
        llm_factory = LlmManager(llm_config)
        self.llm = llm_factory.get_llm_with_fallback()
        
        # Initialize QueryAgent
        self.query_agent = QueryAgent(agents_config['query_agent'], self.llm)
        
        # Initialize MemoryManager
        self.memory_manager = MemoryManager()
        
        # Test customer ID
        self.test_customer_id = "test_query_agent_customer"
        
        # Clear any existing memory
        self.memory_manager.clear_memory(self.test_customer_id)
    
    def test_new_product_queries(self):
        """Test new product queries that should call both agents"""
        test_cases = [
            {
                "query": "Quais smartphones vocês têm disponíveis?",
                "expected_task": "product_query",
                "expected_follow_up": False,
                "expected_agents": ["product_specialist", "response_generator"],
                "description": "Basic smartphone query"
            },
            {
                "query": "Quero ver notebooks da marca Dell",
                "expected_task": "product_query", 
                "expected_follow_up": False,
                "expected_agents": ["product_specialist", "response_generator"],
                "description": "Specific brand query"
            },
            {
                "query": "Mostre-me fones de ouvido",
                "expected_task": "product_query",
                "expected_follow_up": False,
                "expected_agents": ["product_specialist", "response_generator"],
                "description": "Headphones query"
            },
            {
                "query": "Preciso de um tablet",
                "expected_task": "product_query",
                "expected_follow_up": False,
                "expected_agents": ["product_specialist", "response_generator"],
                "description": "Tablet query"
            }
        ]
        
        print("\n🧪 Testing New Product Queries...")
        for i, test_case in enumerate(test_cases, 1):
            with self.subTest(f"New Product Query {i}: {test_case['description']}"):
                print(f"\n📝 Test {i}: {test_case['query']}")
                
                analysis = self.query_agent.analyze_query(
                    test_case["query"], 
                    self.test_customer_id, 
                    self.memory_manager
                )
                
                self.assertIsNotNone(analysis, "Analysis should not be None")
                self.assertEqual(analysis.get('task_type'), test_case['expected_task'])
                self.assertEqual(analysis.get('is_follow_up'), test_case['expected_follow_up'])
                self.assertEqual(set(analysis.get('required_agents', [])), set(test_case['expected_agents']))
                self.assertFalse(analysis.get('end_of_conversation', False))
                
                print(f"✅ Task: {analysis.get('task_type')} | Follow-up: {analysis.get('is_follow_up')} | Agents: {analysis.get('required_agents')}")
    
    def test_follow_up_queries(self):
        """Test follow-up queries that should only call response_generator"""
        # First, create some product context in memory
        mock_product_info = [
            {
                "product": {
                    "id": "smartphone_001",
                    "name": "Samsung Galaxy S21",
                    "category": "smartphone",
                    "price": "R$ 2.999,00",
                    "description": "Smartphone Samsung Galaxy S21"
                }
            },
            {
                "product": {
                    "id": "smartphone_002", 
                    "name": "iPhone 13",
                    "category": "smartphone",
                    "price": "R$ 4.999,00",
                    "description": "Smartphone Apple iPhone 13"
                }
            }
        ]
        
        # Save product info to memory
        conversation_data = {
            "product_info": mock_product_info,
            "query": "Quais smartphones vocês têm?",
            "task_type": "product_query"
        }
        self.memory_manager.save_short_term_memory(self.test_customer_id, conversation_data)
        
        test_cases = [
            {
                "query": "Fale-me mais sobre o primeiro da lista",
                "expected_task": "product_query",
                "expected_follow_up": True,
                "expected_agents": ["response_generator"],
                "description": "Ordinal reference follow-up"
            },
            {
                "query": "Ele tem 5G?",
                "expected_task": "product_query",
                "expected_follow_up": True,
                "expected_agents": ["response_generator"],
                "description": "Feature question follow-up"
            },
            {
                "query": "Qual o preço do segundo?",
                "expected_task": "product_query",
                "expected_follow_up": True,
                "expected_agents": ["response_generator"],
                "description": "Price question follow-up"
            },
            {
                "query": "Mais detalhes sobre o Samsung",
                "expected_task": "product_query",
                "expected_follow_up": True,
                "expected_agents": ["response_generator"],
                "description": "Brand-specific follow-up"
            }
        ]
        
        print("\n🧪 Testing Follow-up Queries...")
        for i, test_case in enumerate(test_cases, 1):
            with self.subTest(f"Follow-up Query {i}: {test_case['description']}"):
                print(f"\n📝 Test {i}: {test_case['query']}")
                
                analysis = self.query_agent.analyze_query(
                    test_case["query"],
                    self.test_customer_id,
                    self.memory_manager
                )
                
                self.assertIsNotNone(analysis, "Analysis should not be None")
                self.assertEqual(analysis.get('task_type'), test_case['expected_task'])
                self.assertEqual(analysis.get('is_follow_up'), test_case['expected_follow_up'])
                self.assertEqual(set(analysis.get('required_agents', [])), set(test_case['expected_agents']))
                self.assertFalse(analysis.get('end_of_conversation', False))
                
                print(f"✅ Task: {analysis.get('task_type')} | Follow-up: {analysis.get('is_follow_up')} | Agents: {analysis.get('required_agents')}")
    
    def test_conversation_termination(self):
        """Test conversation termination detection"""
        test_cases = [
            {
                "query": "Obrigado pela ajuda",
                "expected_task": "general",
                "expected_follow_up": False,
                "expected_agents": ["response_generator"],
                "expected_termination": True,
                "description": "Gratitude expression"
            },
            {
                "query": "Valeu, entendi tudo",
                "expected_task": "general",
                "expected_follow_up": False,
                "expected_agents": ["response_generator"],
                "expected_termination": True,
                "description": "Understanding confirmation"
            },
            {
                "query": "Perfeito, vou comprar",
                "expected_task": "general",
                "expected_follow_up": False,  # Purchase decisions are new queries, not follow-ups
                "expected_agents": ["response_generator"],
                "expected_termination": True,
                "description": "Purchase decision"
            },
            {
                "query": "Tchau, até logo",
                "expected_task": "general",
                "expected_follow_up": False,
                "expected_agents": ["response_generator"],
                "expected_termination": True,
                "description": "Farewell"
            },
            {
                "query": "Beleza, tudo certo",
                "expected_task": "general",
                "expected_follow_up": False,
                "expected_agents": ["response_generator"],
                "expected_termination": True,
                "description": "Confirmation"
            }
        ]
        
        print("\n🧪 Testing Conversation Termination...")
        for i, test_case in enumerate(test_cases, 1):
            with self.subTest(f"Termination {i}: {test_case['description']}"):
                print(f"\n📝 Test {i}: {test_case['query']}")
                
                analysis = self.query_agent.analyze_query(
                    test_case["query"],
                    self.test_customer_id,
                    self.memory_manager
                )
                
                self.assertIsNotNone(analysis, "Analysis should not be None")
                self.assertEqual(analysis.get('task_type'), test_case['expected_task'])
                self.assertEqual(analysis.get('is_follow_up'), test_case['expected_follow_up'])
                self.assertEqual(set(analysis.get('required_agents', [])), set(test_case['expected_agents']))
                self.assertEqual(analysis.get('end_of_conversation'), test_case['expected_termination'])
                
                print(f"✅ Task: {analysis.get('task_type')} | Termination: {analysis.get('end_of_conversation')} | Agents: {analysis.get('required_agents')}")
    
    def test_support_queries(self):
        """Test support-related queries"""
        test_cases = [
            {
                "query": "Preciso de ajuda com um produto defeituoso",
                "expected_task": "support",
                "expected_follow_up": False,
                "expected_agents": ["product_specialist", "response_generator"],
                "description": "Product defect support - needs product identification"
            },
            {
                "query": "Como faço para trocar um item?",
                "expected_task": "support",
                "expected_follow_up": False,
                "expected_agents": ["response_generator"],
                "description": "Return/exchange support - process only"
            },
            {
                "query": "Quero reclamar sobre um produto",
                "expected_task": "support",
                "expected_follow_up": False,
                "expected_agents": ["response_generator"],
                "description": "Complaint support - process only"
            }
        ]
        
        print("\n🧪 Testing Support Queries...")
        for i, test_case in enumerate(test_cases, 1):
            with self.subTest(f"Support Query {i}: {test_case['description']}"):
                print(f"\n📝 Test {i}: {test_case['query']}")
                
                analysis = self.query_agent.analyze_query(
                    test_case["query"],
                    self.test_customer_id,
                    self.memory_manager
                )
                
                self.assertIsNotNone(analysis, "Analysis should not be None")
                self.assertEqual(analysis.get('task_type'), test_case['expected_task'])
                self.assertEqual(analysis.get('is_follow_up'), test_case['expected_follow_up'])
                self.assertEqual(set(analysis.get('required_agents', [])), set(test_case['expected_agents']))
                
                print(f"✅ Task: {analysis.get('task_type')} | Follow-up: {analysis.get('is_follow_up')} | Agents: {analysis.get('required_agents')}")
    
    def test_store_info_queries(self):
        """Test store information queries"""
        test_cases = [
            {
                "query": "Qual o horário de funcionamento da loja?",
                "expected_task": "store_info",
                "expected_follow_up": False,
                "expected_agents": ["response_generator"],
                "description": "Store hours"
            },
            {
                "query": "Onde fica a loja?",
                "expected_task": "store_info",
                "expected_follow_up": False,
                "expected_agents": ["response_generator"],
                "description": "Store location"
            },
            {
                "query": "Qual o telefone de contato?",
                "expected_task": "store_info",
                "expected_follow_up": False,
                "expected_agents": ["response_generator"],
                "description": "Contact information"
            }
        ]
        
        print("\n🧪 Testing Store Info Queries...")
        for i, test_case in enumerate(test_cases, 1):
            with self.subTest(f"Store Info Query {i}: {test_case['description']}"):
                print(f"\n📝 Test {i}: {test_case['query']}")
                
                analysis = self.query_agent.analyze_query(
                    test_case["query"],
                    self.test_customer_id,
                    self.memory_manager
                )
                
                self.assertIsNotNone(analysis, "Analysis should not be None")
                self.assertEqual(analysis.get('task_type'), test_case['expected_task'])
                self.assertEqual(analysis.get('is_follow_up'), test_case['expected_follow_up'])
                self.assertEqual(set(analysis.get('required_agents', [])), set(test_case['expected_agents']))
                
                print(f"✅ Task: {analysis.get('task_type')} | Follow-up: {analysis.get('is_follow_up')} | Agents: {analysis.get('required_agents')}")
    
    def test_edge_cases(self):
        """Test edge cases and ambiguous queries"""
        test_cases = [
            {
                "query": "Oi",
                "expected_task": "general",
                "expected_follow_up": False,
                "expected_agents": ["response_generator"],
                "description": "Simple greeting"
            },
            {
                "query": "Como você está?",
                "expected_task": "general",
                "expected_follow_up": False,
                "expected_agents": ["response_generator"],
                "description": "Casual question"
            },
            {
                "query": "",
                "expected_task": "general",
                "expected_follow_up": False,
                "expected_agents": ["response_generator"],
                "description": "Empty query"
            },
            {
                "query": "Produto",
                "expected_task": "product_query",
                "expected_follow_up": False,
                "expected_agents": ["product_specialist", "response_generator"],
                "description": "Single word product query"
            }
        ]
        
        print("\n🧪 Testing Edge Cases...")
        for i, test_case in enumerate(test_cases, 1):
            with self.subTest(f"Edge Case {i}: {test_case['description']}"):
                print(f"\n📝 Test {i}: '{test_case['query']}'")
                
                analysis = self.query_agent.analyze_query(
                    test_case["query"],
                    self.test_customer_id,
                    self.memory_manager
                )
                
                self.assertIsNotNone(analysis, "Analysis should not be None")
                self.assertEqual(analysis.get('task_type'), test_case['expected_task'])
                self.assertEqual(analysis.get('is_follow_up'), test_case['expected_follow_up'])
                self.assertEqual(set(analysis.get('required_agents', [])), set(test_case['expected_agents']))
                
                print(f"✅ Task: {analysis.get('task_type')} | Follow-up: {analysis.get('is_follow_up')} | Agents: {analysis.get('required_agents')}")
    
    def test_new_product_category_after_follow_up(self):
        """Test that new product categories restart the process"""
        # First, create product context in memory
        mock_product_info = [
            {
                "product": {
                    "id": "smartphone_001",
                    "name": "Samsung Galaxy S21",
                    "category": "smartphone",
                    "price": "R$ 2.999,00"
                }
            }
        ]
        
        conversation_data = {
            "product_info": mock_product_info,
            "query": "Quais smartphones vocês têm?",
            "task_type": "product_query"
        }
        self.memory_manager.save_short_term_memory(self.test_customer_id, conversation_data)
        
        test_cases = [
            {
                "query": "Quero ver notebooks agora",
                "expected_task": "product_query",
                "expected_follow_up": False,  # Should be False for new category
                "expected_agents": ["product_specialist", "response_generator"],
                "description": "New product category after smartphone query"
            },
            {
                "query": "Mostre-me fones de ouvido",
                "expected_task": "product_query",
                "expected_follow_up": False,  # Should be False for new category
                "expected_agents": ["product_specialist", "response_generator"],
                "description": "Different product category"
            }
        ]
        
        print("\n🧪 Testing New Product Categories...")
        for i, test_case in enumerate(test_cases, 1):
            with self.subTest(f"New Category {i}: {test_case['description']}"):
                print(f"\n📝 Test {i}: {test_case['query']}")
                
                analysis = self.query_agent.analyze_query(
                    test_case["query"],
                    self.test_customer_id,
                    self.memory_manager
                )
                
                self.assertIsNotNone(analysis, "Analysis should not be None")
                self.assertEqual(analysis.get('task_type'), test_case['expected_task'])
                self.assertEqual(analysis.get('is_follow_up'), test_case['expected_follow_up'])
                self.assertEqual(set(analysis.get('required_agents', [])), set(test_case['expected_agents']))
                
                print(f"✅ Task: {analysis.get('task_type')} | Follow-up: {analysis.get('is_follow_up')} | Agents: {analysis.get('required_agents')}")

def run_query_agent_tests():
    """Run all QueryAgent tests"""
    print("🧪 Running Comprehensive QueryAgent Tests...")
    print("=" * 80)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestQueryAgent)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 80)
    print("📊 QUERY AGENT TEST SUMMARY")
    print("=" * 80)
    print(f"✅ Tests Run: {result.testsRun}")
    print(f"❌ Failures: {len(result.failures)}")
    print(f"⚠️  Errors: {len(result.errors)}")
    print(f"🎯 Success Rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n⚠️  ERRORS:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_query_agent_tests()
    sys.exit(0 if success else 1) 