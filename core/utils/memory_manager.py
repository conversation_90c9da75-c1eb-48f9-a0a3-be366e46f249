import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import time

class MemoryManager:
    def __init__(self, short_term_dir: str = "memory/short_term", 
                 long_term_dir: str = "memory/long_term"):
        self.short_term_dir = Path(short_term_dir)
        self.long_term_dir = Path(long_term_dir)
        
        # Create directories if they don't exist
        self.short_term_dir.mkdir(parents=True, exist_ok=True)
        self.long_term_dir.mkdir(parents=True, exist_ok=True)
    
    def save_short_term_memory(self, customer_id: str, conversation_data: Dict[str, Any]):
        """Save conversation data to short-term memory with compression"""
        file_path = self.short_term_dir / f"{customer_id}_current.json"

        # Compress conversation data to reduce size
        compressed_data = self._compress_conversation_data(conversation_data)

        memory_data = {
            "customer_id": customer_id,
            "timestamp": datetime.now().isoformat(),
            "conversation": compressed_data
        }

        print(f"[MemoryManager] Saving STM for customer {customer_id}")
        print(f"[MemoryManager] STM data keys: {list(compressed_data.keys())}")
        print(f"[MemoryManager] Conversation history: {len(compressed_data.get('conversation_history', []))} turns")

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(memory_data, f, indent=2, ensure_ascii=False)

        print(f"[MemoryManager] STM saved to {file_path}")
    
    def get_short_term_memory(self, customer_id: str) -> Dict[str, Any]:
        """Retrieve short-term memory for customer"""
        file_path = self.short_term_dir / f"{customer_id}_current.json"
        
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"[MemoryManager] Loaded STM for customer {customer_id}")
                return data
        
        print(f"[MemoryManager] No STM found for customer {customer_id}")
        return {}
    
    def save_long_term_memory(self, customer_id: str, data: Any) -> None:
        """Save data to long-term memory with structured format"""
        try:
            # Load existing LTM
            ltm_data = self.get_long_term_memory(customer_id)
            
            # If data is already a structured summary, save it directly
            if isinstance(data, dict) and 'conversation_id' in data:
                # This is a conversation summary
                ltm_data.append(data)
                print(f"[MemoryManager] Saved conversation summary to LTM: {data.get('conversation_id', 'unknown')}")
            else:
                # This is a simple summary string (legacy format)
                summary_entry = {
                    'conversation_id': f"legacy_{int(time.time())}_{customer_id}",
                    'timestamp': datetime.now().isoformat(),
                    'customer_id': customer_id,
                    'summary': str(data),
                    'interaction_type': 'legacy',
                    'products_discussed': [],
                    'key_queries': [],
                    'customer_intent': 'unknown',
                    'resolution': 'unknown',
                    'follow_up_required': False,
                    'next_action': 'none',
                    'privacy_level': 'standard',
                    'detail_level': 'low'
                }
                ltm_data.append(summary_entry)
                print(f"[MemoryManager] Saved legacy summary to LTM: {summary_entry['conversation_id']}")
            
            # Save to file
            self._save_ltm_to_file(customer_id, ltm_data)
            
        except Exception as e:
            print(f"[MemoryManager] Error saving LTM: {e}")
    
    def get_long_term_memory(self, customer_id: str) -> List[Dict[str, Any]]:
        """Retrieve long-term memory for customer"""
        file_path = self.long_term_dir / f"{customer_id}_history.json"
        
        print(f"[MemoryManager] Looking for LTM file: {file_path}")
        print(f"[MemoryManager] File exists: {file_path.exists()}")
        
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"[MemoryManager] Loaded LTM for customer {customer_id} ({len(data)} entries)")
                print(f"[MemoryManager] LTM entries: {data}")
                return data
        
        print(f"[MemoryManager] No LTM found for customer {customer_id}")
        return []
    
    def clear_short_term_memory(self, customer_id: str):
        """Clear short-term memory after conversation ends"""
        file_path = self.short_term_dir / f"{customer_id}_current.json"
        if file_path.exists():
            file_path.unlink()
            print(f"[MemoryManager] Cleared STM for customer {customer_id}")
    
    def generate_conversation_summary(self, conversation_data: Dict[str, Any]) -> str:
        """Generate a simple summary of the conversation"""
        query = conversation_data.get('query', '')
        response = conversation_data.get('final_response', '')
        
        summary = f"Cliente perguntou: {query[:100]}..."
        if response:
            summary += f" Resposta fornecida sobre: {response[:50]}..."
        
        return summary

    def _compress_conversation_data(self, conversation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Compress conversation data to reduce memory usage and improve performance"""
        compressed = conversation_data.copy()

        # Limit conversation history to last 3 turns + summary
        conversation_history = conversation_data.get('conversation_history', [])
        if len(conversation_history) > 3:
            # Keep last 3 turns
            recent_turns = conversation_history[-3:]

            # Create summary of older turns
            older_turns = conversation_history[:-3]
            summary = self._create_conversation_summary(older_turns)

            compressed['conversation_history'] = recent_turns
            compressed['conversation_summary'] = summary
            compressed['total_turns'] = len(conversation_history)

            print(f"[MemoryManager] Compressed {len(conversation_history)} turns to 3 recent + summary")

        # Keep only essential product info (remove verbose descriptions)
        if 'product_info' in compressed:
            compressed['product_info'] = self._compress_product_info(compressed['product_info'])

        # Remove verbose fields that can be regenerated
        fields_to_remove = ['context_notes', 'reasoning', 'specialized_query']
        for field in fields_to_remove:
            compressed.pop(field, None)

        return compressed

    def _create_conversation_summary(self, turns: List[Dict[str, Any]]) -> str:
        """Create a concise summary of conversation turns"""
        if not turns:
            return ""

        topics = []
        products_mentioned = set()

        for turn in turns:
            query = turn.get('query', '')
            if query:
                # Extract key topics
                if any(word in query.lower() for word in ['smartphone', 'celular', 'telefone']):
                    topics.append('smartphones')
                elif any(word in query.lower() for word in ['notebook', 'laptop', 'computador']):
                    topics.append('notebooks')
                elif any(word in query.lower() for word in ['tablet']):
                    topics.append('tablets')
                elif any(word in query.lower() for word in ['preço', 'valor', 'custo']):
                    topics.append('preços')
                elif any(word in query.lower() for word in ['comparar', 'diferença']):
                    topics.append('comparações')

        unique_topics = list(set(topics))
        if unique_topics:
            return f"Discussão anterior sobre: {', '.join(unique_topics)}"
        else:
            return f"Conversa anterior com {len(turns)} interações"

    def _compress_product_info(self, product_info: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Compress product information to keep only essential fields"""
        compressed_products = []

        for product in product_info:
            # Keep only essential fields
            essential_fields = ['id', 'name', 'category', 'price', 'description']
            compressed_product = {
                field: product.get(field)
                for field in essential_fields
                if product.get(field) is not None
            }
            compressed_products.append(compressed_product)

        return compressed_products

    def clear_memory(self, customer_id: str):
        """
        Deletes the short-term and long-term memory files for a specific customer.
        """
        stm_file = self.short_term_dir / f"{customer_id}_current.json"
        ltm_file = self.long_term_dir / f"{customer_id}_history.json"
        
        print(f"[MemoryManager] Clearing memory for customer {customer_id}...")
        
        try:
            if stm_file.exists():
                stm_file.unlink()
                print(f"[MemoryManager] Deleted STM file: {stm_file}")
        except OSError as e:
            print(f"[MemoryManager] Error deleting STM file {stm_file}: {e}")

        try:
            if ltm_file.exists():
                ltm_file.unlink()
                print(f"[MemoryManager] Deleted LTM file: {ltm_file}")
        except OSError as e:
            print(f"[MemoryManager] Error deleting LTM file {ltm_file}: {e}")

        print(f"[MemoryManager] Memory cleared for customer {customer_id}.")

    def _save_ltm_to_file(self, customer_id: str, data: List[Dict[str, Any]]) -> None:
        """Internal method to save long-term memory data to file."""
        file_path = self.long_term_dir / f"{customer_id}_history.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"[MemoryManager] LTM saved to {file_path} (total entries: {len(data)})")

    def get_conversation_context(self, customer_id: str, seller_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Get conversation context for the QueryAgent based on seller configuration.
        
        Args:
            customer_id: Customer identifier
            seller_config: Seller configuration with context settings
            
        Returns:
            List of recent conversation summaries for context
        """
        try:
            # Get configuration settings
            context_interactions = seller_config.get('context_interactions', 3)
            privacy_level = seller_config.get('privacy_level', 'standard')
            
            # Load LTM
            ltm_data = self.get_long_term_memory(customer_id)
            
            if not ltm_data:
                print(f"[MemoryManager] No LTM found for customer {customer_id}")
                return []
            
            # Get the last N interactions
            recent_interactions = ltm_data[-context_interactions:] if len(ltm_data) > context_interactions else ltm_data
            
            # Filter based on privacy level
            filtered_interactions = []
            for interaction in recent_interactions:
                filtered_interaction = self._filter_interaction_by_privacy(interaction, privacy_level)
                if filtered_interaction:
                    filtered_interactions.append(filtered_interaction)
            
            print(f"[MemoryManager] Retrieved {len(filtered_interactions)} recent interactions for context")
            return filtered_interactions
            
        except Exception as e:
            print(f"[MemoryManager] Error retrieving conversation context: {e}")
            return []
    
    def _filter_interaction_by_privacy(self, interaction: Dict[str, Any], privacy_level: str) -> Dict[str, Any]:
        """Filter interaction data based on privacy level"""
        if not isinstance(interaction, dict):
            return interaction
        
        filtered_interaction = interaction.copy()
        
        if privacy_level == "minimal":
            # Keep only essential information
            essential_fields = ['conversation_id', 'timestamp', 'interaction_type', 'resolution']
            filtered_interaction = {k: v for k, v in filtered_interaction.items() if k in essential_fields}
            
        elif privacy_level == "standard":
            # Remove personal data but keep product and query information
            personal_fields = ['customer_insights', 'personal_data']
            for field in personal_fields:
                filtered_interaction.pop(field, None)
            
            # Filter product information
            if 'products_discussed' in filtered_interaction:
                filtered_products = []
                for product in filtered_interaction['products_discussed']:
                    if isinstance(product, dict):
                        filtered_product = {
                            'id': product.get('id'),
                            'name': product.get('name'),
                            'category': product.get('category')
                        }
                        # Remove pricing for standard privacy
                        if 'price' in product:
                            filtered_product['price_range'] = 'available'  # Generic indicator
                        filtered_products.append(filtered_product)
                filtered_interaction['products_discussed'] = filtered_products
        
        # For detailed privacy level, keep everything
        return filtered_interaction
    
    def get_customer_relationship_context(self, customer_id: str, seller_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get comprehensive customer relationship context for personalization.
        
        Args:
            customer_id: Customer identifier
            seller_config: Seller configuration
            
        Returns:
            Customer relationship context with insights
        """
        try:
            # Get all LTM data
            ltm_data = self.get_long_term_memory(customer_id)
            
            if not ltm_data:
                return {
                    'customer_id': customer_id,
                    'relationship_status': 'new_customer',
                    'total_interactions': 0,
                    'preferences': [],
                    'common_intents': [],
                    'last_interaction': None
                }
            
            # Analyze customer relationship
            total_interactions = len(ltm_data)
            relationship_status = self._determine_relationship_status(total_interactions)
            
            # Extract preferences and patterns
            preferences = self._extract_customer_preferences(ltm_data)
            common_intents = self._extract_common_intents(ltm_data)
            
            # Get last interaction
            last_interaction = ltm_data[-1] if ltm_data else None
            
            context = {
                'customer_id': customer_id,
                'relationship_status': relationship_status,
                'total_interactions': total_interactions,
                'preferences': preferences,
                'common_intents': common_intents,
                'last_interaction': last_interaction,
                'interaction_history': ltm_data[-5:] if len(ltm_data) >= 5 else ltm_data  # Last 5 interactions
            }
            
            print(f"[MemoryManager] Generated relationship context for {customer_id}: {relationship_status}")
            return context
            
        except Exception as e:
            print(f"[MemoryManager] Error generating relationship context: {e}")
            return {
                'customer_id': customer_id,
                'relationship_status': 'unknown',
                'total_interactions': 0,
                'preferences': [],
                'common_intents': [],
                'last_interaction': None
            }
    
    def _determine_relationship_status(self, total_interactions: int) -> str:
        """Determine customer relationship status based on interaction count"""
        if total_interactions == 0:
            return 'new_customer'
        elif total_interactions <= 2:
            return 'returning_customer'
        elif total_interactions <= 5:
            return 'regular_customer'
        else:
            return 'loyal_customer'
    
    def _extract_customer_preferences(self, ltm_data: List[Dict[str, Any]]) -> List[str]:
        """Extract customer preferences from interaction history"""
        preferences = set()
        
        for interaction in ltm_data:
            # Extract product categories
            products = interaction.get('products_discussed', [])
            for product in products:
                if isinstance(product, dict) and 'category' in product:
                    preferences.add(f"category:{product['category']}")
            
            # Extract customer insights
            insights = interaction.get('customer_insights', {})
            if isinstance(insights, dict):
                customer_prefs = insights.get('preferences', [])
                if isinstance(customer_prefs, list):
                    preferences.update(customer_prefs)
        
        return list(preferences)
    
    def _extract_common_intents(self, ltm_data: List[Dict[str, Any]]) -> List[str]:
        """Extract common customer intents from interaction history"""
        intents = {}
        
        for interaction in ltm_data:
            intent = interaction.get('customer_intent', 'unknown')
            intents[intent] = intents.get(intent, 0) + 1
        
        # Return most common intents
        sorted_intents = sorted(intents.items(), key=lambda x: x[1], reverse=True)
        return [intent for intent, count in sorted_intents[:3]]  # Top 3 intents