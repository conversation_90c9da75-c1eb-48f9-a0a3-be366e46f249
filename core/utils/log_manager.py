import json
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import time


class LogManager:

    def __init__(self):
        # Performance tracking
        self.query_count = 0
        self.total_query_time = 0.0

    # Funcoes de Log do ProductSpecialist
    def _log_tool_usage(self, tool_name: str, input_params: dict, output: dict, execution_time: float):
        """Log tool usage for debugging and analytics."""
        print(
            f"[ProductSpecialistAgent] Tool: {tool_name} | Time: {execution_time:.3f}s | Success: {output.get('success', False)}")

    def _get_log_path(self, customer_id: str) -> str:
        """Get the log file path for a customer."""
        if not customer_id:
            customer_id = "unknown_customer"
        log_dir = os.path.join(os.path.dirname(__file__), f'../../logs/{customer_id}')
        os.makedirs(log_dir, exist_ok=True)
        return os.path.join(log_dir, 'product_specialist_prompts.log')

    def _log_execution_start(self, query: str, customer_id: str, conversation_data: Dict):
        """Log the start of execution with input details."""
        log_path = self._get_log_path(customer_id)
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(f"\n{'=' * 80}\n")
            f.write(f"[{datetime.now().isoformat()}] PRODUCT SPECIALIST EXECUTION START\n")
            f.write(f"Customer ID: {customer_id}\n")
            f.write(f"Query: {query}\n")
            f.write(f"Is Follow-up: {conversation_data.get('is_follow_up', False)}\n")
            f.write(f"Product Context: {conversation_data.get('product_context', {})}\n")
            f.write(f"{'=' * 80}\n")

    def _log_tool_plan(self, tool_plan: List[Dict], customer_id: str):
        """Log the tool execution plan."""
        log_path = self._get_log_path(customer_id)
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(f"\n--- TOOL PLAN ---\n")
            for i, step in enumerate(tool_plan, 1):
                f.write(f"Step {i}: {step['tool']} - {step['reason']}\n")
                f.write(f"  Parameters: {step['params']}\n")
            f.write(f"Total tools planned: {len(tool_plan)}\n")

    def _log_tool_execution_results(self, execution_results: List[Dict], customer_id: str):
        """Log detailed results from each tool execution."""
        log_path = self._get_log_path(customer_id)
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(f"\n--- TOOL EXECUTION RESULTS ---\n")
            for i, result in enumerate(execution_results, 1):
                tool_name = result.get('tool', 'Unknown')
                success = result.get('result', {}).get('success', False)
                execution_time = result.get('execution_time', 0)
                original_params = result.get('original_params', {})
                validated_params = result.get('params', {})

                f.write(f"\nTool {i}: {tool_name}\n")
                f.write(f"  Success: {success}\n")
                f.write(f"  Execution Time: {execution_time:.3f}s\n")
                f.write(f"  Original Parameters: {original_params}\n")
                f.write(f"  Validated Parameters: {validated_params}\n")

                if success:
                    tool_result = result.get('result', {})
                    if 'results' in tool_result:
                        products = tool_result['results']
                        f.write(f"  Products Found: {len(products)}\n")
                        for j, product in enumerate(products[:3], 1):  # Log first 3 products
                            f.write(
                                f"    {j}. {product.get('name', 'Unknown')} (ID: {product.get('id', 'N/A')}) - R$ {product.get('price', 'N/A')}\n")
                        if len(products) > 3:
                            f.write(f"    ... and {len(products) - 3} more products\n")
                    elif 'product' in tool_result:
                        product = tool_result['product']
                        f.write(f"  Product: {product.get('name', 'Unknown')} (ID: {product.get('id', 'N/A')})\n")
                    else:
                        f.write(f"  Result: {tool_result}\n")
                else:
                    error = result.get('result', {}).get('error', 'Unknown error')
                    f.write(f"  Error: {error}\n")

    def _log_final_response(self, final_response: Dict, customer_id: str):
        """Log the final response that will be sent to the customer."""
        log_path = self._get_log_path(customer_id)
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(f"\n--- FINAL CUSTOMER RESPONSE ---\n")
            f.write(f"Response Type: {type(final_response)}\n")
            if isinstance(final_response, dict):
                if 'query' in final_response:
                    f.write(f"Query Response: {final_response['query']}\n")
                if 'results' in final_response:
                    f.write(f"Products in Response: {len(final_response['results'])}\n")
                    for i, product in enumerate(final_response['results'][:5], 1):
                        f.write(f"  {i}. {product.get('name', 'Unknown')} - R$ {product.get('price', 'N/A')}\n")
                f.write(f"Full Response: {final_response}\n")
            else:
                f.write(f"Response: {final_response}\n")

    def _log_execution_summary(self, execution_time: float, tools_summary: str, customer_id: str):
        """Log execution summary and performance metrics."""
        log_path = self._get_log_path(customer_id)
        with open(log_path, 'a', encoding='utf-8') as f:
            f.write(f"\n--- EXECUTION SUMMARY ---\n")
            f.write(f"Total Execution Time: {execution_time:.3f}s\n")
            f.write(f"Tools Used: {tools_summary}\n")
            f.write(f"Query Count (Session): {self.query_count}\n")
            f.write(f"Average Query Time: {self.total_query_time / self.query_count:.3f}s\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
            f.write(f"{'=' * 80}\n\n")
