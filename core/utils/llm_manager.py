import json
import os
import re
from typing import Any, Dict, List
from dotenv import load_dotenv
from langchain_mistralai import ChatMistralAI
from langchain_google_genai import GoogleGenerativeAI
from langchain_openai import OpenAI
from langchain_groq import ChatGroq
import time
import logging


class LlmManager:
    def __init__(self, config: dict, env_file_path: str = "config/.env"):
        """
        Initialize the LLMFactory with configurations.
        :param config: A dictionary containing LLM configurations.
        """
        self.config = config
        load_dotenv(dotenv_path=env_file_path)  # Load environment variables
        self.transformers_tokenizer = None
        self.transformers_tokenizer_loaded = False
        self.transformers_tokenizer_load_time = None
        self.logger = logging.getLogger("LLMFactory")
        self.use_transformers_tokenizer = False
        # Check for flag in config
        llm_section = self.config.get("llm", self.config)
        if llm_section.get("use_transformers_tokenizer", False):
            self.use_transformers_tokenizer = True
            self._load_transformers_tokenizer()

    def _load_transformers_tokenizer(self):
        start = time.time()
        try:
            from transformers import AutoTokenizer
            # Use tokenizer_model_name if available, else fallback to model_name
            model_name = None
            if self.config.get("primary_llm"):
                model_name = self.config["primary_llm"].get("tokenizer_model_name") or self.config["primary_llm"].get(
                    "model_name")
            if not model_name and self.config.get("fallback_llm"):
                model_name = self.config["fallback_llm"].get("tokenizer_model_name") or self.config["fallback_llm"].get(
                    "model_name")
            if not model_name:
                model_name = "gpt2"  # fallback
            self.transformers_tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.transformers_tokenizer_loaded = True
            self.transformers_tokenizer_load_time = time.time() - start
            self.logger.info(
                f"[LLMFactory] Loaded transformers tokenizer for '{model_name}' in {self.transformers_tokenizer_load_time:.2f}s (tokenizer_model_name field).")
        except Exception as e:
            self.logger.error(f"[LLMFactory] Failed to load transformers tokenizer: {e}")
            self.transformers_tokenizer_loaded = False

    def count_tokens(self, text: str) -> int:
        preview = text[:80].replace('\n', ' ') + ('...' if len(text) > 80 else '')
        model_name = None
        if self.config.get("primary_llm"):
            model_name = self.config["primary_llm"].get("tokenizer_model_name") or self.config["primary_llm"].get(
                "model_name")
        if not model_name and self.config.get("fallback_llm"):
            model_name = self.config["fallback_llm"].get("tokenizer_model_name") or self.config["fallback_llm"].get(
                "model_name")
        if not model_name:
            model_name = "gpt2"
        if self.use_transformers_tokenizer and self.transformers_tokenizer_loaded:
            try:
                tokens = self.transformers_tokenizer.encode(text)
                self.logger.info(
                    f"[LLMFactory] [transformers] Model: {model_name} | Text: '{preview}' | Tokens: {len(tokens)}")
                return len(tokens)
            except Exception as e:
                self.logger.error(f"[LLMFactory] Error using transformers tokenizer: {e}")
        self.logger.info(f"[LLMFactory] [fallback] Text: '{preview}' | Tokens: {max(1, len(text.split()))}")
        return max(1, len(text.split()))

    def _resolve_llm_config(self, llm_key_or_dict, llms_section):
        """
        Resolve the LLM config. If llm_key_or_dict is a string, look it up in llms_section.
        If it's already a dict, return as is (for backward compatibility).
        """
        if isinstance(llm_key_or_dict, str):
            if llms_section and llm_key_or_dict in llms_section:
                return llms_section[llm_key_or_dict]
            else:
                raise ValueError(f"LLM key '{llm_key_or_dict}' not found in llms section.")
        return llm_key_or_dict

    def get_primary_llm(self) -> Any:
        """
        Retrieve the primary LLM based on configuration.
        """
        llm_section = self.config.get("llm", self.config)
        # Support llms under llm or at the top level
        llms_section = None
        if isinstance(llm_section, dict) and "llms" in llm_section:
            llms_section = llm_section["llms"]
        elif "llms" in self.config:
            llms_section = self.config["llms"]
        primary_llm_key_or_dict = llm_section.get("primary_llm")
        llm_config = self._resolve_llm_config(primary_llm_key_or_dict, llms_section)
        if not llm_config:
            raise ValueError("Primary LLM configuration not found.")
        provider = llm_config.get("provider")
        api_key_env_var = llm_config.get("api_key_env_var")
        model_name = llm_config.get("model_name")

        # Check provider support first
        if provider == "openai":
            api_key = os.getenv(api_key_env_var)
            if not api_key:
                raise ValueError(f"API key for {provider} not found in environment variables.")
            return OpenAI(temperature=0.2, openai_api_key=api_key, model_name=model_name)
        elif provider == "google_gemini":
            api_key = os.getenv(api_key_env_var)
            if not api_key:
                raise ValueError(f"API key for {provider} not found in environment variables.")
            return GoogleGenerativeAI(api_key=api_key, model=model_name)
        elif provider == "mistralai":
            api_key = os.getenv(api_key_env_var)
            if not api_key:
                raise ValueError(f"API key for {provider} not found in environment variables.")
            return ChatMistralAI(model=model_name, temperature=0.2, mistral_api_key=api_key)
        elif provider == "groq":
            api_key = os.getenv(api_key_env_var)
            if not api_key:
                raise ValueError(f"API key for {provider} not found in environment variables.")
            return ChatGroq(model=model_name, temperature=0.2, groq_api_key=api_key)
        else:
            raise ValueError(f"Unsupported LLM provider: {provider}")

    def get_fallback_llm(self) -> Any:
        """
        Retrieve the fallback LLM based on configuration.
        """
        llm_section = self.config.get("llm", self.config)
        # Support llms under llm or at the top level
        llms_section = None
        if isinstance(llm_section, dict) and "llms" in llm_section:
            llms_section = llm_section["llms"]
        elif "llms" in self.config:
            llms_section = self.config["llms"]
        fallback_llm_key_or_dict = llm_section.get("fallback_llm")
        llm_config = self._resolve_llm_config(fallback_llm_key_or_dict, llms_section)
        if not llm_config:
            raise ValueError("Fallback LLM configuration not found.")
        provider = llm_config.get("provider")
        api_key_env_var = llm_config.get("api_key_env_var")
        model_name = llm_config.get("model_name")

        # Check provider support first
        if provider == "openai":
            api_key = os.getenv(api_key_env_var)
            if not api_key:
                raise ValueError(f"API key for {provider} not found in environment variables.")
            return OpenAI(temperature=0.2, openai_api_key=api_key, model_name=model_name)
        elif provider == "google_gemini":
            api_key = os.getenv(api_key_env_var)
            if not api_key:
                raise ValueError(f"API key for {provider} not found in environment variables.")
            return GoogleGenerativeAI(api_key=api_key, model=model_name, max_retries=5, temperature=0.2)
        elif provider == "mistralai":
            api_key = os.getenv(api_key_env_var)
            if not api_key:
                raise ValueError(f"API key for {provider} not found in environment variables.")
            return ChatMistralAI(model=model_name, temperature=0.2, mistral_api_key=api_key, max_retries=5,
                                 max_tokens=1000)
        elif provider == "groq":
            api_key = os.getenv(api_key_env_var)
            if not api_key:
                raise ValueError(f"API key for {provider} not found in environment variables.")
            return ChatGroq(model=model_name, temperature=0.2, groq_api_key=api_key)
        else:
            raise ValueError(f"Unsupported LLM provider: {provider}")

    def get_llm_with_fallback(self) -> Any:
        """
        Get the primary LLM, with fallback logic in case of failure.
        """
        try:
            return self.get_primary_llm()
        except Exception as e:
            print(f"Primary LLM failed: {e}. Switching to fallback LLM...")
            return self.get_fallback_llm()

    @staticmethod
    def transformers_count_tokens(text: str, model_name: str = "gpt2") -> int:
        """Static method for testing transformers token counting."""
        try:
            from transformers import AutoTokenizer
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            tokens = tokenizer.encode(text)
            return len(tokens)
        except Exception as e:
            print(f"[LLMFactory] Error in static transformers_count_tokens: {e}")
            return max(1, len(text.split()))

    def _extract_all_products_from_history(self, short_term_memory: Dict) -> List[str]:
        """Extract all product IDs mentioned in conversation history."""
        product_ids = set()

        # Get from products_in_focus
        if 'products_in_focus' in short_term_memory:
            for product in short_term_memory['products_in_focus']:
                if 'id' in product:
                    product_ids.add(product['id'])

        # Get from conversation history
        if 'conversation_history' in short_term_memory:
            for turn in short_term_memory['conversation_history']:
                if 'products_in_focus' in turn:
                    for product in turn['products_in_focus']:
                        if 'id' in product:
                            product_ids.add(product['id'])

        # Get from product_info if available
        if 'product_info' in short_term_memory:
            for product in short_term_memory['product_info']:
                if 'id' in product:
                    product_ids.add(product['id'])

        return list(product_ids)

    def _fix_json_issues(self, json_str: str) -> str:
        """Fix common JSON issues that cause parsing failures."""
        # Fix unquoted null-like values
        json_str = re.sub(r':\s*Nenhum\b', ': null', json_str)
        json_str = re.sub(r':\s*N/A\b', ': null', json_str)
        json_str = re.sub(r':\s*None\b', ': null', json_str)
        json_str = re.sub(r':\s*undefined\b', ': null', json_str)

        # Fix unquoted boolean values
        json_str = re.sub(r':\s*True\b', ': true', json_str)
        json_str = re.sub(r':\s*False\b', ': false', json_str)

        # Fix trailing commas
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*]', ']', json_str)

        return json_str

    def _parse_llm_response(self, response: str, short_term_memory: Dict = None) -> Dict[str, Any]:
        """Parse LLM response with robust JSON extraction"""
        try:
            # Clean the response - remove markdown code blocks
            cleaned_response = response.strip()

            # Remove markdown code blocks if present
            if cleaned_response.startswith('```json'):
                cleaned_response = cleaned_response.replace('```json', '').replace('```', '').strip()
            elif cleaned_response.startswith('```'):
                cleaned_response = cleaned_response.replace('```', '').strip()

            # Try to find JSON in the response using regex as fallback
            json_match = re.search(r'\{.*\}', cleaned_response, re.DOTALL)
            if json_match:
                cleaned_response = json_match.group()

            # Fix common JSON issues before parsing
            cleaned_response = self._fix_json_issues(cleaned_response)

            # Parse the JSON
            analysis = json.loads(cleaned_response)

            # Enhance specialized queries with product context for follow-ups
            if short_term_memory and analysis.get('is_follow_up') and 'specialized_queries' in analysis:
                all_product_ids = self._extract_all_products_from_history(short_term_memory)
                if all_product_ids and 'product_specialist' in analysis['specialized_queries']:
                    # Enhance the product specialist query with specific IDs
                    current_query = analysis['specialized_queries']['product_specialist']
                    if 'tablets' in current_query.lower() or 'câmera' in current_query.lower():
                        ids_str = ', '.join(all_product_ids)
                        analysis['specialized_queries']['product_specialist'] = f"{current_query} - IDs: {ids_str}"

            # Validate required fields
            required_fields = ['task_type', 'is_follow_up', 'required_agents', 'reasoning', 'context_notes']
            for field in required_fields:
                if field not in analysis:
                    print(f"[QueryAgent] Missing required field: {field}")
                    if field == 'context_notes':
                        analysis['context_notes'] = 'No context notes provided.'
                    else:
                        return None

            return analysis

        except json.JSONDecodeError as e:
            print(f"[QueryAgent] Error parsing JSON: {e}")
            print(f"[QueryAgent] Raw response: {response}")
            return None
        except Exception as e:
            print(f"[QueryAgent] Unexpected error parsing response: {e}")
            return None
