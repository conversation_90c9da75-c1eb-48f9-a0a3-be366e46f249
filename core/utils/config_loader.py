import yaml
from typing import Dict, Any
import os
import json

class ConfigLoader:
    @staticmethod
    def load_config(config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise Exception(f"Configuration file not found: {config_path}")
        except yaml.YAMLError as e:
            raise Exception(f"Error parsing YAML file {config_path}: {e}")
    
    @staticmethod
    def get_agent_config(agent_id: str, config_path: str = "../../config/agents.yaml") -> Dict[str, Any]:
        """Get configuration for specific agent"""
        config = ConfigLoader.load_config(config_path)
        
        if agent_id not in config.get('agents', {}):
            raise Exception(f"Agent configuration not found: {agent_id}")
        
        return config['agents'][agent_id]
    
    @staticmethod
    def get_llm_config(config_path: str = "../../config/llm_config.yaml") -> Dict[str, Any]:
        """Get LLM configuration (primary and fallback)"""
        config = ConfigLoader.load_config(config_path)
        
        if 'llm' not in config:
            raise Exception("LLM configuration not found in the configuration file.")
        
        return config['llm']
    
    @staticmethod
    def get_seller_config(config_path: str = "../../config/seller_config.yaml") -> Dict[str, Any]:
        """Get seller configuration with summary and privacy settings"""
        config = ConfigLoader.load_config(config_path)
        
        if 'seller' not in config:
            raise Exception("Seller configuration not found in the configuration file.")
        
        return config['seller']
    
    @staticmethod
    def get_summary_settings(config_path: str = "../../config/seller_config.yaml") -> Dict[str, Any]:
        """Get summary settings from seller configuration"""
        seller_config = ConfigLoader.get_seller_config(config_path)
        return seller_config.get('summary_settings', {})
    
    @staticmethod
    def get_conversation_settings(config_path: str = "../../config/seller_config.yaml") -> Dict[str, Any]:
        """Get conversation settings from seller configuration"""
        seller_config = ConfigLoader.get_seller_config(config_path)
        return seller_config.get('conversation_settings', {})
    
    @staticmethod
    def get_privacy_settings(config_path: str = "../../config/seller_config.yaml") -> Dict[str, Any]:
        """Get privacy settings from seller configuration"""
        seller_config = ConfigLoader.get_seller_config(config_path)
        return seller_config.get('privacy_settings', {})
    
    @staticmethod
    def validate_agent_config(agent_config: Dict[str, Any], agent_id: str) -> bool:
        """Validate that agent configuration has required fields"""
        required_fields = ['id', 'role', 'system_prompt']
        missing_fields = [field for field in required_fields if field not in agent_config]
        
        if missing_fields:
            print(f"Warning: Agent {agent_id} missing required fields: {missing_fields}")
            return False
        
        return True
    
    @staticmethod
    def get_config_with_fallback(config_path: str, fallback_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """Load configuration with fallback if file doesn't exist"""
        try:
            return ConfigLoader.load_config(config_path)
        except Exception as e:
            if fallback_config:
                print(f"Warning: Could not load {config_path}, using fallback config: {e}")
                return fallback_config
            else:
                raise e
    
    @staticmethod
    def get_seller_json(json_path: str = "../../data/seller.json") -> Dict[str, Any]:
        """Load seller information from seller.json file"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                return json.load(f)["seller"]
        except FileNotFoundError:
            raise Exception(f"Seller JSON file not found: {json_path}")
        except json.JSONDecodeError as e:
            raise Exception(f"Error parsing seller JSON file {json_path}: {e}")