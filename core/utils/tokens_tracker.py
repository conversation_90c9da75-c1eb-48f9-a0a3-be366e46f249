import json
from datetime import datetime
from pathlib import Path

class TokenTracker:
    def __init__(self):
        self.tokens_dir = Path('memory/tokens')
        self.tokens_dir.mkdir(parents=True, exist_ok=True)

    def add_usage(self, customer_id: str, agent_id: str, input_tokens: int, output_tokens: int):
        """Append a token usage record to the customer's token file."""
        file_path = self.tokens_dir / f"{customer_id}.json"
        if file_path.exists():
            with open(file_path, 'r') as f:
                data = json.load(f)
        else:
            data = []
        usage_entry = {
            "timestamp": datetime.now().isoformat(),
            "agent_id": agent_id,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "total_tokens": input_tokens + output_tokens
        }
        data.append(usage_entry)
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)

    def get_usage(self, agent_id: str = None) -> dict:
        """Get token usage statistics for a specific agent or all agents."""
        total_input = 0
        total_output = 0
        total_tokens = 0

        # Aggregate usage from all customer files
        for file_path in self.tokens_dir.glob("*.json"):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)

                for entry in data:
                    if agent_id is None or entry.get('agent_id') == agent_id:
                        total_input += entry.get('input_tokens', 0)
                        total_output += entry.get('output_tokens', 0)
                        total_tokens += entry.get('total_tokens', 0)
            except (json.JSONDecodeError, FileNotFoundError):
                continue

        return {
            'input_tokens': total_input,
            'output_tokens': total_output,
            'total_tokens': total_tokens
        }

    def get_usage(self, agent_id: str = None) -> dict:
        """Get token usage statistics for a specific agent or all agents."""
        total_input = 0
        total_output = 0
        total_tokens = 0

        # Aggregate usage from all customer files
        for file_path in self.tokens_dir.glob("*.json"):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)

                for entry in data:
                    if agent_id is None or entry.get('agent_id') == agent_id:
                        total_input += entry.get('input_tokens', 0)
                        total_output += entry.get('output_tokens', 0)
                        total_tokens += entry.get('total_tokens', 0)
            except (json.JSONDecodeError, FileNotFoundError):
                continue

        return {
            'input_tokens': total_input,
            'output_tokens': total_output,
            'total_tokens': total_tokens
        }