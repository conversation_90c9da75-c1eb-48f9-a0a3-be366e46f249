#!/usr/bin/env python3
"""
Migration script to upgrade Context Agent from text matching to RAG
- Enables pgvector extension
- Adds vector column to context_memory table
- Migrates existing data to use embeddings
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.database import engine, SessionLocal
from api.util.embedding_util import EmbeddingUtil
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def enable_pgvector():
    """Enable pgvector extension"""
    print("🔧 Enabling pgvector extension...")
    
    try:
        with engine.connect() as conn:
            conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
            conn.commit()
        print("✅ pgvector extension enabled")
        return True
    except Exception as e:
        print(f"❌ Error enabling pgvector: {e}")
        return False

def add_vector_column():
    """Add vector column to context_memory table"""
    print("🔧 Adding vector column to context_memory table...")
    
    try:
        with engine.connect() as conn:
            # Check if column already exists
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'context_memory' 
                AND column_name = 'embedding'
            """)).fetchone()
            
            if result:
                print("✅ Vector column already exists")
                return True
            
            # Add the vector column
            conn.execute(text("ALTER TABLE context_memory ADD COLUMN embedding vector(384)"))
            
            # Drop the old embedding_json column if it exists
            conn.execute(text("ALTER TABLE context_memory DROP COLUMN IF EXISTS embedding_json"))
            
            conn.commit()
        
        print("✅ Vector column added successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error adding vector column: {e}")
        return False

def migrate_existing_data():
    """Generate embeddings for existing context data"""
    print("🔧 Migrating existing context data to use embeddings...")
    
    try:
        db = SessionLocal()
        embedding_util = EmbeddingUtil()
        
        if not embedding_util.is_available():
            print("⚠️  Embedding model not available, skipping data migration")
            return True
        
        # Get contexts without embeddings
        result = db.execute(text("""
            SELECT id, query_text, response_text 
            FROM context_memory 
            WHERE embedding IS NULL
            LIMIT 100
        """))
        
        contexts_to_update = result.fetchall()
        
        if not contexts_to_update:
            print("✅ No existing data to migrate")
            return True
        
        print(f"📊 Found {len(contexts_to_update)} contexts to migrate")
        
        # Process in batches
        updated_count = 0
        for context in contexts_to_update:
            try:
                # Prepare text for embedding
                text_to_embed = context.query_text
                if context.response_text:
                    text_to_embed = f"{context.query_text} {context.response_text}"
                
                # Generate embedding
                embedding = embedding_util.generate_embedding(text_to_embed)
                
                if embedding:
                    # Update the record
                    db.execute(text("""
                        UPDATE context_memory 
                        SET embedding = :embedding 
                        WHERE id = :context_id
                    """), {
                        "embedding": str(embedding),
                        "context_id": context.id
                    })
                    updated_count += 1
                    
                    if updated_count % 10 == 0:
                        print(f"📈 Migrated {updated_count} contexts...")
                        
            except Exception as e:
                logger.warning(f"Failed to migrate context {context.id}: {e}")
                continue
        
        db.commit()
        db.close()
        
        print(f"✅ Successfully migrated {updated_count} contexts to use embeddings")
        return True
        
    except Exception as e:
        print(f"❌ Error migrating existing data: {e}")
        return False

def create_vector_index():
    """Create index on vector column for faster similarity search"""
    print("🔧 Creating vector similarity index...")
    
    try:
        with engine.connect() as conn:
            # Create HNSW index for fast similarity search
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_context_memory_embedding_cosine 
                ON context_memory 
                USING hnsw (embedding vector_cosine_ops)
            """))
            conn.commit()
        
        print("✅ Vector similarity index created")
        return True
        
    except Exception as e:
        print(f"❌ Error creating vector index: {e}")
        return False

def test_rag_functionality():
    """Test the upgraded RAG functionality"""
    print("🧪 Testing RAG functionality...")
    
    try:
        from api.service.context_service import ContextService
        
        db = SessionLocal()
        context_service = ContextService(db)
        
        # Test embedding generation
        if context_service.use_embeddings:
            print("✅ RAG system is active (using semantic search)")
            
            # Test context retrieval
            contexts = context_service.get_context("test_customer", "iPhone preço")
            print(f"✅ Semantic search returned {len(contexts)} results")
            
        else:
            print("⚠️  RAG system using text fallback (embeddings not available)")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error testing RAG functionality: {e}")
        return False

def main():
    """Run the complete migration to RAG"""
    print("🚀 Context Agent RAG Migration")
    print("=" * 50)
    
    steps = [
        ("Enable pgvector extension", enable_pgvector),
        ("Add vector column", add_vector_column),
        ("Migrate existing data", migrate_existing_data),
        ("Create vector index", create_vector_index),
        ("Test RAG functionality", test_rag_functionality)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"❌ Migration failed at step: {step_name}")
            sys.exit(1)
    
    print("\n🎉 RAG Migration completed successfully!")
    print("\n📊 Your Context Agent now uses:")
    print("   ✅ Semantic similarity search with sentence-transformers")
    print("   ✅ pgvector for fast vector operations")
    print("   ✅ Enhanced context injection with relevance scoring")
    print("   ✅ Graceful fallback to text matching if needed")

if __name__ == "__main__":
    main()
