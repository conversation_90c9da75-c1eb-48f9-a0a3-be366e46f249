import logging

from fastapi import Depends, FastAPI, HTTPException
from pydantic import BaseModel
from sqlalchemy.orm import Session

from api.service.chat_service import ChatService
from api.database import get_db
from api.service.product_service import ProductService

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="API de Produtos",
    description="API para gerenciar produtos com chat",
    version="1.0.0",
    docs_url="/docs",  # Força o caminho do Swagger
    redoc_url="/redoc",  # Força o caminho do ReDoc
    openapi_url="/openapi.json"
)


# origins = [
#     "http://localhost:4200",  # Angular local
#     "https://seu-dominio-angular.com",  # produção
# ]
#
# app.add_middleware(
#     CORSMiddleware,
#     allow_origins=origins,  # ou ["*"] para liberar geral (não recomendado em produção)
#     allow_credentials=True,
#     allow_methods=["*"],
#     allow_headers=["*"],
# )


class PerguntaRequest(BaseModel):
    cliente_id: str
    texto: str


@app.get("/")
def index():
    return {"hello": "World"}


# ENDPOINT ADICIONAL PARA TESTAR APENAS O BANCO
@app.get("/test/database")
def test_database(db: Session = Depends(get_db)):
    """
    Endpoint para testar conexão com banco
    """
    try:
        # Teste simples de conexão
        result = db.execute("SELECT 1 as test")
        test_value = result.scalar()

        return {
            "status": "ok",
            "database_connection": "success",
            "test_query": test_value
        }
    except Exception as e:
        logger.error(f"Erro ao testar banco: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/chat/mensagem")
def processa_mensagem(pergunta_request: PerguntaRequest, db: Session = Depends(get_db)):
    try:
        chat_service = ChatService(db)
        chat_service.processar_pergunta("Tem smartphone?", None)
        product_service = ProductService(db)
        return product_service.find_all_by_text("Smartphones")
        # produto = product_repository.get_product_by_id(db, "Smartphones")
        # print(produto)
        # return {"status": "ok"}
    except Exception as e:
        import traceback
        print("Erro no endpoint /chat/mensagem:")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))
