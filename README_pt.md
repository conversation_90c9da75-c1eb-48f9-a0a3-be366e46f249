# LangChainAgent — Visão Geral do Projeto

## Descrição do Projeto

LangChainAgent é um sistema de IA conversacional modular e de nível profissional para atendimento ao cliente em português, construído sobre o framework LangChain. Possui arquitetura multiagente, gerenciamento avançado de memória e flexibilidade robusta de provedores LLM, entregando respostas inteligentes e contextuais para consultas de produtos e suporte.

## Visão Geral da Arquitetura

### Componentes Principais
- **OrchestratorAgent**: Coordenador central, roteia consultas, gerencia o fluxo e aciona a sumarização.
- **QueryAgent**: Analisa consultas (usando LLM e contexto de memória) para determinar tipo de tarefa, status de follow-up, agentes necessários e queries especializadas.
- **ProductSpecialistAgent**: Lida com consultas de produtos usando fuzzy matching, geração de sinônimos (via LLM) e tratamento de follow-up contextual.
- **ResponseGeneratorAgent**: Formata e gera respostas amigáveis ao cliente, com estratégias diferentes para primeiras interações e follow-ups.
- **SummarizationAgent**: Gera resumos estruturados da conversa para memória de longo prazo, configurável por privacidade e nível de detalhe.
- **MemoryManager**: Gerencia memória de curto e longo prazo, filtragem de privacidade e recuperação de contexto.
- **LLMFactory**: Provedor LLM configurável com fallback (Google Gemini, Mistral, OpenAI).
- **TokenTracker**: Controle de uso de tokens por agente e sessão.

## Estrutura do Projeto

```
LangChainAgent/
├── main.py                 # Ponto de entrada da aplicação
├── requirements.txt        # Dependências Python
├── config/                 # Arquivos de configuração (agentes, LLM, vendedor)
├── core/                   # Lógica central: agentes, utilitários, testes
├── memory/                 # Armazenamento de memória de curto e longo prazo
├── data/                   # Arquivos de dados de produtos
├── docs/                   # Documentação
```

## Principais Funcionalidades

### 1. Orquestração Multiagente
- **OrchestratorAgent**: Analisa consultas, determina agentes necessários, gerencia fluxo e aciona sumarização.
- **QueryAgent**: Usa LLM e contexto de memória para classificar consultas, detectar follow-ups e gerar queries especializadas para cada agente.
- **ProductSpecialistAgent**: Fuzzy matching (`fuzzywuzzy`), geração de sinônimos (LLM), ranking contextual e tratamento de follow-up (referências ordinais, correspondência de produto/categoria).
- **ResponseGeneratorAgent**: Formata respostas para primeiras interações e follow-ups, garantindo clareza, honestidade e foco no cliente.
- **SummarizationAgent**: Produz resumos estruturados e privativos para memória de longo prazo e analytics.

### 2. Processamento Inteligente de Consultas
- **Fuzzy Matching**: Encontra produtos relevantes mesmo com consultas imperfeitas.
- **Geração de Sinônimos**: Termos alternativos via LLM.
- **Consciência de Contexto**: Mantém e utiliza STM e LTM para respostas precisas e ricas em contexto.
- **Follow-up**: Detecta e processa follow-ups usando contexto de memória e referências ordinais.

### 3. Sistema de Gerenciamento de Memória
- **Memória de Curto Prazo (STM)**: Armazena contexto atual da conversa em arquivos JSON, limpo após o término.
- **Memória de Longo Prazo (LTM)**: Armazena resumos estruturados da conversa, com filtragem de privacidade e recuperação de contexto para consultas futuras.
- **Filtragem de Privacidade**: Resumos e contexto filtrados conforme configuração do vendedor.

### 4. Flexibilidade de Provedor LLM
- **LLMFactory**: Suporta Google Gemini, Mistral e OpenAI, com fallback e configuração YAML.
- **Variáveis de Ambiente**: Todas as chaves de API são gerenciadas de forma segura via variáveis de ambiente.

### 5. Controle de Uso de Tokens
- **TokenTracker**: Monitora uso de tokens por agente e sessão, com logs persistentes para análise.

### 6. Sistema de Configuração
- **Baseado em YAML**: Configuração de agentes, LLM e vendedor em arquivos YAML.
- **Configuração do Vendedor**: Controla sumarização, privacidade e contexto.

### 7. Testes
- **Testes reais com LLM**: Sem mocks; todos os testes usam LLMs reais e chaves de API.
- **Cobertura**: Lógica dos agentes, integração, memória, tratamento de erros e fluxo de conversa.
- **Marcadores**: Pytest markers customizados para saída limpa.

### 8. Tratamento de Erros
- **Fallback de LLM**: Troca automática para provedor secundário.
- **Validação de Configuração**: Erros claros para configurações ausentes ou inválidas.
- **Erros de Memória**: Tratamento elegante de problemas de sistema de arquivos.
- **Falhas de Agente**: Respostas de fallback e logs robustos.

### 9. Segurança
- **Sem Segredos no Código**: Dados sensíveis apenas em variáveis de ambiente.
- **Filtragem de Privacidade**: Memória e resumos filtrados conforme nível de privacidade.

## Detalhes dos Agentes

### OrchestratorAgent
- Coordenador central, roteia consultas, gerencia fluxo e aciona sumarização.
- Integra QueryAgent, ProductSpecialistAgent, ResponseGeneratorAgent e SummarizationAgent.
- Usa análise baseada em LLM e fallback por regras.

### QueryAgent
- Analisa consultas usando LLM e contexto de memória.
- Determina tipo de tarefa, status de follow-up, agentes necessários e queries especializadas.
- Detecta fim de conversa e gera notas de contexto.

### ProductSpecialistAgent
- Lida com consultas de produtos usando fuzzy matching e geração de sinônimos.
- Ranqueia produtos por relevância, suporta follow-ups contextuais (referências ordinais, correspondência de produto/categoria).
- Retorna resultados com scores de confiança e nível de detalhe (básico/detalhado).

### ResponseGeneratorAgent
- Formata e gera respostas amigáveis ao cliente.
- Estratégias diferentes para primeiras interações e follow-ups.
- Transparente sobre informações ausentes, incentiva novas perguntas.

### SummarizationAgent
- Gera resumos estruturados e privativos para LTM.
- Configurável pelo vendedor para nível de detalhe e privacidade.
- Resumos incluem info de produto, intenção do cliente, resolução e insights.

## Gerenciamento de Memória
- **STM**: Arquivos JSON em `memory/short_term/`, limpos após a conversa.
- **LTM**: Arquivos JSON em `memory/long_term/`, armazena resumos estruturados, filtrados por privacidade e usados como contexto em consultas futuras.
- **Recuperação de Contexto**: Número de interações passadas configurável pelo vendedor.

## Sistema de Configuração
- **llm_config.yaml**: Provedor LLM, modelo e variável de ambiente da chave API.
- **agents.yaml**: Papéis dos agentes, prompts, temperatura e limites de tokens.
- **seller_config.yaml**: Sumarização, privacidade e contexto.

## Dependências
- `langchain`, `langchain-core`, `langchain-community`, `langchain[google-genai]`, `langchain[mistralai]`, `langchain[openai]`, `langgraph`, `openai`, `pydantic`, `pyyaml`, `python-dotenv`, `pytest`, `pytest-mock`, `pytest-cov`, `responses`, `fuzzywuzzy`, `scikit-fuzzy`, `numpy`, `scipy`, `networkx`, `python-Levenshtein`

## Exemplo de Uso

```python
# Inicialize o bot conversacional
bot = ConversationalBot()

# Processe uma consulta do cliente
resultado = bot.process_query(
    customer_id="cliente_001",
    query="Quais smartphones vocês têm disponíveis?"
)

# Manipule a resposta
if resultado['success']:
    print(f"Resposta: {resultado['response']}")
    print(f"Uso de Tokens: {resultado['token_usage']}")
else:
    print(f"Erro: {resultado['error']}")
```

## Melhorias Futuras
- Memória baseada em banco de dados (substituir arquivos)
- Cache avançado (ex: Redis)
- Dashboard de analytics para uso de tokens e performance
- Suporte multilíngue
- Interface web (API REST, frontend)
- Suporte a Docker
- Testes e documentação expandidos

## Conclusão

LangChainAgent é um sistema de IA conversacional robusto, extensível e pronto para produção para atendimento ao cliente em português, com:
- Design modular e multiagente
- Gerenciamento avançado de memória e contexto
- Suporte flexível a provedores LLM
- Testes reais e cobertura de casos do mundo real
- Boas práticas de segurança e privacidade

Pronto para ser expandido e implantado em ambientes exigentes de atendimento ao cliente. 