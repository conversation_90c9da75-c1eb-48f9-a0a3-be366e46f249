#!/usr/bin/env python3
"""
Setup script to restart PostgreSQL with pgvector support
This will update your database to support vector operations
"""

import subprocess
import sys
import time
import os

def run_command(command, description):
    """Run a shell command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed")
            return True
        else:
            print(f"❌ {description} failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {description} failed: {e}")
        return False

def check_docker():
    """Check if Docker is running"""
    return run_command("docker --version", "Checking Docker")

def stop_containers():
    """Stop existing containers"""
    return run_command("docker-compose down", "Stopping existing containers")

def start_containers():
    """Start containers with pgvector support"""
    return run_command("docker-compose up -d", "Starting containers with pgvector")

def wait_for_postgres():
    """Wait for PostgreSQL to be ready"""
    print("⏳ Waiting for PostgreSQL to be ready...")
    max_attempts = 30
    for attempt in range(max_attempts):
        try:
            result = subprocess.run(
                "docker exec postgres pg_isready -U postgres", 
                shell=True, 
                capture_output=True, 
                text=True
            )
            if result.returncode == 0:
                print("✅ PostgreSQL is ready")
                return True
        except:
            pass
        
        time.sleep(2)
        print(f"   Attempt {attempt + 1}/{max_attempts}...")
    
    print("❌ PostgreSQL failed to start")
    return False

def test_pgvector():
    """Test if pgvector extension is available"""
    print("🧪 Testing pgvector extension...")
    try:
        from api.database import engine
        from sqlalchemy import text
        
        with engine.connect() as conn:
            conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
            conn.commit()
        
        print("✅ pgvector extension is working")
        return True
        
    except Exception as e:
        print(f"❌ pgvector test failed: {e}")
        return False

def main():
    """Main setup process"""
    print("🚀 Setting up PostgreSQL with pgvector support")
    print("=" * 60)
    
    steps = [
        ("Check Docker", check_docker),
        ("Stop existing containers", stop_containers),
        ("Start containers with pgvector", start_containers),
        ("Wait for PostgreSQL", wait_for_postgres),
        ("Test pgvector extension", test_pgvector)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"❌ Setup failed at step: {step_name}")
            print("\n💡 Troubleshooting tips:")
            print("   - Make sure Docker is running")
            print("   - Check if ports 5432 and 6379 are available")
            print("   - Try: docker-compose down && docker-compose up -d")
            sys.exit(1)
    
    print("\n🎉 PostgreSQL with pgvector is ready!")
    print("\n📊 What's now available:")
    print("   ✅ PostgreSQL 16 with pgvector extension")
    print("   ✅ Vector similarity search capabilities")
    print("   ✅ Ready for RAG migration")
    
    print("\n🚀 Next steps:")
    print("   1. Run: python migrate_to_rag.py")
    print("   2. Run: python test_context_agent.py")

if __name__ == "__main__":
    main()
