#!/usr/bin/env python3
"""
Test script for Context Agent RAG System
Tests semantic similarity and vector-based context retrieval
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.database import SessionLocal
from api.service.context_service import ContextService
from api.domain.context_model import ContextMemory
import uuid

def test_context_service():
    """Test RAG-enhanced context service functionality"""
    print("🧪 Testing Context Agent RAG System...")

    # Create database session
    db = SessionLocal()
    context_service = ContextService(db)

    try:
        # Test 0: Check RAG capabilities
        print("\n0️⃣ Checking RAG capabilities...")
        print(f"✅ Using embeddings: {context_service.use_embeddings}")
        print(f"✅ Similarity threshold: {context_service.similarity_threshold}")
        if context_service.use_embeddings:
            model_info = context_service.embedding_util.get_model_info()
            print(f"✅ Embedding model: {model_info['model_name']}")
            print(f"✅ Embedding dimensions: {model_info['embedding_dim']}")

        # Test 1: Context Capture with Embeddings
        print("\n1️⃣ Testing semantic context capture...")
        customer_id = "rag_test_customer_456"

        # Capture multiple related contexts
        test_contexts = [
            ("Tem iphones disponíveis?", "Sim, temos iPhone 13, iPhone 14 e iPhone 15 disponíveis."),
            ("Qual o preço do iPhone 13?", "O iPhone 13 custa R$ 3.500 à vista."),
            ("iPhone 14 tem desconto?", "O iPhone 14 está com 10% de desconto, custando R$ 4.050."),
            ("Preciso de um smartphone barato", "Temos opções mais econômicas como iPhone SE por R$ 2.200.")
        ]

        captured_ids = []
        for query, response in test_contexts:
            capture_response = context_service.capture(
                customer_id=customer_id,
                agent_type="manager_agent",
                query=query,
                response=response
            )
            captured_ids.append(capture_response.context_id)
            print(f"   📝 Captured: {query[:30]}... (ID: {capture_response.context_id[:8]}...)")

        # Test 2: Semantic Retrieval
        print("\n2️⃣ Testing semantic context retrieval...")

        # Test semantic queries
        semantic_queries = [
            "Quanto custa o iPhone mais novo?",  # Should match iPhone 14/15 pricing
            "Celular mais barato disponível",     # Should match iPhone SE
            "Modelos de iPhone em estoque"        # Should match availability question
        ]

        for query in semantic_queries:
            print(f"\n   🔍 Query: {query}")
            contexts = context_service.get_context(customer_id, query)

            print(f"   ✅ Found {len(contexts)} semantic matches")
            for i, ctx in enumerate(contexts, 1):
                similarity_emoji = "🔥" if ctx['similarity'] > 0.8 else "✨" if ctx['similarity'] > 0.6 else "💡"
                print(f"      {i}. {similarity_emoji} {ctx['query'][:40]}... (sim: {ctx['similarity']:.3f})")

        # Test 3: Enhanced Context Injection
        print("\n3️⃣ Testing enhanced context injection...")
        base_prompt = "Você é um assistente de vendas. Responda sobre opções de iPhone."
        contexts = context_service.get_context(customer_id, "iPhone preços modelos")
        enhanced_prompt = context_service.inject_context(base_prompt, contexts)

        print(f"✅ Enhanced prompt length: {len(enhanced_prompt)} chars")
        print(f"📄 Enhanced prompt preview:")
        print(enhanced_prompt[:300] + "..." if len(enhanced_prompt) > 300 else enhanced_prompt)

        # Test 4: Context Stats
        print("\n4️⃣ Testing context statistics...")
        stats = context_service.get_context_stats(customer_id)
        print(f"✅ Context stats: {stats}")

        # Test 5: Similarity Comparison
        print("\n5️⃣ Testing similarity comparison...")
        if context_service.use_embeddings:
            # Test embedding similarity
            embedding1 = context_service.embedding_util.generate_embedding("iPhone preço")
            embedding2 = context_service.embedding_util.generate_embedding("Quanto custa iPhone")
            embedding3 = context_service.embedding_util.generate_embedding("Pizza delivery")

            if embedding1 and embedding2 and embedding3:
                sim_related = context_service.embedding_util.compute_similarity(embedding1, embedding2)
                sim_unrelated = context_service.embedding_util.compute_similarity(embedding1, embedding3)

                print(f"   ✅ Similar queries similarity: {sim_related:.3f}")
                print(f"   ✅ Unrelated queries similarity: {sim_unrelated:.3f}")
                print(f"   ✅ Semantic understanding: {'Good' if sim_related > sim_unrelated else 'Needs improvement'}")

        print("\n🎉 All RAG tests passed! Context Agent is working with semantic search.")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

    finally:
        db.close()

def test_database_connection():
    """Test database connection and table creation"""
    print("\n🔗 Testing database connection...")
    
    try:
        db = SessionLocal()
        
        # Test if context_memory table exists
        from sqlalchemy import text
        result = db.execute(text("SELECT COUNT(*) FROM context_memory")).scalar()
        print(f"✅ context_memory table exists with {result} records")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        print("💡 Make sure to run: docker-compose up -d")
        print("💡 And ensure the context_memory table is created")
        return False

if __name__ == "__main__":
    print("🚀 Context Agent RAG Test Suite")
    print("=" * 60)

    # Test database first
    if test_database_connection():
        # Run RAG context service tests
        test_context_service()
    else:
        print("❌ Database connection failed. Please check your setup.")
        print("💡 Run: python migrate_to_rag.py")
        sys.exit(1)
