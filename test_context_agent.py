#!/usr/bin/env python3
"""
Test script for Context Agent MVP
Tests basic functionality of context capture and retrieval
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.database import SessionLocal
from api.service.context_service import ContextService
from api.domain.context_model import ContextMemory
import uuid

def test_context_service():
    """Test basic context service functionality"""
    print("🧪 Testing Context Agent MVP...")
    
    # Create database session
    db = SessionLocal()
    context_service = ContextService(db)
    
    try:
        # Test 1: Context Capture
        print("\n1️⃣ Testing context capture...")
        customer_id = "test_customer_123"
        
        capture_response = context_service.capture(
            customer_id=customer_id,
            agent_type="manager_agent",
            query="Tem iphones disponíveis?",
            response="Sim, temos vários modelos de iPhone disponíveis."
        )
        
        print(f"✅ Capture result: {capture_response.success}")
        print(f"📝 Context ID: {capture_response.context_id}")
        
        # Test 2: Context Retrieval
        print("\n2️⃣ Testing context retrieval...")
        contexts = context_service.get_context(
            customer_id=customer_id,
            query="Qual o preço dos iphones?"
        )
        
        print(f"✅ Retrieved {len(contexts)} context items")
        for i, ctx in enumerate(contexts, 1):
            print(f"   {i}. Query: {ctx['query'][:50]}...")
            print(f"      Similarity: {ctx['similarity']:.3f}")
        
        # Test 3: Context Injection
        print("\n3️⃣ Testing context injection...")
        base_prompt = "Você é um assistente de vendas. Responda sobre preços de iPhone."
        enhanced_prompt = context_service.inject_context(base_prompt, contexts)
        
        print(f"✅ Enhanced prompt length: {len(enhanced_prompt)} chars")
        print(f"📄 Enhanced prompt preview:")
        print(enhanced_prompt[:200] + "..." if len(enhanced_prompt) > 200 else enhanced_prompt)
        
        # Test 4: Context Stats
        print("\n4️⃣ Testing context statistics...")
        stats = context_service.get_context_stats(customer_id)
        print(f"✅ Context stats: {stats}")
        
        print("\n🎉 All tests passed! Context Agent MVP is working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

def test_database_connection():
    """Test database connection and table creation"""
    print("\n🔗 Testing database connection...")
    
    try:
        db = SessionLocal()
        
        # Test if context_memory table exists
        result = db.execute("SELECT COUNT(*) FROM context_memory").scalar()
        print(f"✅ context_memory table exists with {result} records")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        print("💡 Make sure to run: docker-compose up -d")
        print("💡 And ensure the context_memory table is created")
        return False

if __name__ == "__main__":
    print("🚀 Context Agent MVP Test Suite")
    print("=" * 50)
    
    # Test database first
    if test_database_connection():
        # Run context service tests
        test_context_service()
    else:
        print("❌ Database connection failed. Please check your setup.")
        sys.exit(1)
