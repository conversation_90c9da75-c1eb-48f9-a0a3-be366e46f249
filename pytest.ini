[tool:pytest]
testpaths = core/tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=core
    --cov-report=term-missing
    --cov-report=html
markers =
    unit: Unit tests
    integration: Integration tests
    llm: LLM-related tests
    agent: Agent-related tests
    slow: Slow running tests
    real_api: Real API connection tests 