#!/usr/bin/env python3
"""
Integration test for Context Agent with existing chat service
Tests the full flow with context enhancement
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.database import SessionLocal
from api.service.chat_service import ChatService
from api.service.context_service import ContextService

def test_chat_with_context():
    """Test chat service with context agent integration"""
    print("🧪 Testing Chat Service with Context Agent...")
    
    try:
        # Create chat service
        chat_service = ChatService()
        customer_id = "integration_test_customer"
        
        # Test conversation flow with context
        print("\n1️⃣ First interaction...")
        response1 = chat_service.processar_pergunta(
            pergunta="Tem iphones disponíveis?",
            conversation_id=customer_id
        )
        print(f"✅ Response 1: {response1['resposta'][:100]}...")
        
        print("\n2️⃣ Second interaction (should have context)...")
        response2 = chat_service.processar_pergunta(
            pergunta="Qual o preço deles?",
            conversation_id=customer_id
        )
        print(f"✅ Response 2: {response2['resposta'][:100]}...")
        
        print("\n3️⃣ Third interaction (should have more context)...")
        response3 = chat_service.processar_pergunta(
            pergunta="Tem algum modelo mais barato?",
            conversation_id=customer_id
        )
        print(f"✅ Response 3: {response3['resposta'][:100]}...")
        
        # Check context accumulation
        print("\n4️⃣ Checking context accumulation...")
        db = SessionLocal()
        context_service = ContextService(db)
        
        stats = context_service.get_context_stats(customer_id)
        print(f"✅ Context stats: {stats}")
        
        contexts = context_service.get_context(customer_id, "iphone preço")
        print(f"✅ Available contexts: {len(contexts)}")
        
        for i, ctx in enumerate(contexts, 1):
            print(f"   {i}. {ctx['agent_type']}: {ctx['query'][:50]}...")
        
        db.close()
        
        print("\n🎉 Integration test completed successfully!")
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()

def test_context_enhancement():
    """Test that context actually enhances prompts"""
    print("\n🔍 Testing context enhancement...")
    
    try:
        db = SessionLocal()
        context_service = ContextService(db)
        customer_id = "enhancement_test_customer"
        
        # Add some context
        context_service.capture(
            customer_id=customer_id,
            agent_type="manager_agent",
            query="Estou procurando um smartphone",
            response="Temos várias opções de smartphones disponíveis"
        )
        
        context_service.capture(
            customer_id=customer_id,
            agent_type="specialist_agent",
            query="iPhone 13 preço",
            response="iPhone 13 custa R$ 3.500"
        )
        
        # Test context retrieval and enhancement
        contexts = context_service.get_context(customer_id, "iPhone barato")
        print(f"✅ Retrieved {len(contexts)} relevant contexts")
        
        base_prompt = "Responda sobre opções de iPhone"
        enhanced_prompt = context_service.inject_context(base_prompt, contexts)
        
        print(f"📄 Base prompt: {base_prompt}")
        print(f"📄 Enhanced prompt preview:")
        print(enhanced_prompt[:300] + "..." if len(enhanced_prompt) > 300 else enhanced_prompt)
        
        # Verify enhancement
        has_context = "Previous:" in enhanced_prompt
        print(f"✅ Context injection working: {has_context}")
        
        db.close()
        
    except Exception as e:
        print(f"❌ Enhancement test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 Context Agent Integration Test Suite")
    print("=" * 60)
    
    # Test context enhancement first
    test_context_enhancement()
    
    # Test full chat integration
    test_chat_with_context()
