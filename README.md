# LangChainAgent Project Overview

## Project Description

LangChainAgent is a production-grade, modular conversational AI system for Portuguese customer service, built on the LangChain framework. It features a multi-agent architecture, advanced memory management, and robust LLM provider flexibility, delivering context-aware, intelligent responses to product and support queries.

## Architecture Overview

### Core Components
- **OrchestratorAgent**: Central coordinator, routes queries, manages flow, triggers summarization.
- **QueryAgent**: Analyzes queries (using LLM and memory context) to determine task type, follow-up status, required agents, and specialized queries.
- **ProductSpecialistAgent**: Handles product queries using fuzzy matching, synonym generation (via LLM), and context-aware follow-up handling.
- **ResponseGeneratorAgent**: Formats and generates customer-friendly responses, with different strategies for first interactions and follow-ups.
- **SummarizationAgent**: Generates structured conversation summaries for long-term memory, configurable by privacy and detail level.
- **MemoryManager**: Handles short-term and long-term memory, privacy filtering, and context retrieval.
- **LLMFactory**: Configurable LLM provider with fallback (Google Gemini, Mistral, OpenAI).
- **TokenTracker**: Per-agent and per-session token usage tracking.

## Project Structure

```
LangChainAgent/
├── main.py                 # Main application entry point
├── requirements.txt        # Python dependencies
├── config/                 # Configuration files (agents, LLM, seller)
├── core/                   # Core logic: agents, utils, tests
├── memory/                 # Short-term and long-term memory storage
├── data/                   # Product data files
├── docs/                   # Documentation
```

## Key Features

### 1. Multi-Agent Orchestration
- **OrchestratorAgent**: Analyzes queries, determines required agents, manages flow, and triggers summarization.
- **QueryAgent**: Uses LLM and memory context to classify queries, detect follow-ups, and generate specialized queries for each agent.
- **ProductSpecialistAgent**: Fuzzy string matching (`fuzzywuzzy`), synonym generation (LLM), context-aware ranking, and follow-up handling (ordinal references, product/category matching).
- **ResponseGeneratorAgent**: Formats responses for first interactions and follow-ups, ensuring clarity, honesty, and customer-friendliness.
- **SummarizationAgent**: Produces structured, privacy-aware conversation summaries for long-term memory and analytics.

### 2. Intelligent Query Processing
- **Fuzzy Matching**: Finds relevant products even with imperfect queries.
- **Synonym Generation**: LLM-powered alternative search terms.
- **Context Awareness**: Maintains and leverages STM and LTM for accurate, context-rich responses.
- **Follow-up Handling**: Detects and processes follow-up queries using memory context and ordinal references.

### 3. Memory Management System
- **Short-term Memory (STM)**: Stores current conversation context in JSON files, cleared after conversation.
- **Long-term Memory (LTM)**: Stores structured conversation summaries, with privacy filtering and context retrieval for future queries.
- **Privacy Filtering**: Summaries and context are filtered based on seller-configured privacy levels.

### 4. LLM Provider Flexibility
- **LLMFactory**: Supports Google Gemini, Mistral, and OpenAI, with fallback and YAML-based config.
- **Environment Variables**: All API keys managed securely via environment variables.

### 5. Token Usage Tracking
- **TokenTracker**: Tracks per-agent and per-session token usage, with persistent logging for analysis.

### 6. Configuration System
- **YAML-based**: Agent, LLM, and seller configuration in YAML files.
- **Seller Config**: Controls summarization, privacy, and context settings.

### 7. Test Suite
- **Real LLM-driven tests**: No mocking; all tests use real LLMs and API keys.
- **Coverage**: Agent logic, integration, memory, error handling, and conversation flow.
- **Markers**: Custom pytest markers for clean test output.

### 8. Error Handling
- **LLM Fallback**: Automatic fallback to secondary provider.
- **Config Validation**: Clear errors for missing/invalid configs.
- **Memory Errors**: Graceful handling of file system issues.
- **Agent Failures**: Fallback responses and robust error logging.

### 9. Security
- **No Hardcoded Secrets**: All sensitive data in environment variables.
- **Privacy Filtering**: Memory and summaries filtered by privacy level.

## Agent Details

### OrchestratorAgent
- Central coordinator, routes queries, manages flow, triggers summarization.
- Integrates QueryAgent, ProductSpecialistAgent, ResponseGeneratorAgent, and SummarizationAgent.
- Uses LLM-based and rule-based fallback for query analysis.

### QueryAgent
- Analyzes queries using LLM and memory context.
- Determines task type, follow-up status, required agents, and specialized queries.
- Handles end-of-conversation detection and context notes.

### ProductSpecialistAgent
- Handles product queries using fuzzy matching and synonym generation.
- Ranks products by relevance, supports context-aware follow-ups (ordinal references, product/category matching).
- Returns top results with confidence scores and detail level (basic/detailed).

### ResponseGeneratorAgent
- Formats and generates customer-friendly responses.
- Different strategies for first interactions and follow-ups.
- Honest about missing information, invites further questions.

### SummarizationAgent
- Generates structured, privacy-aware conversation summaries for LTM.
- Configurable by seller for detail and privacy level.
- Summaries include product info, customer intent, resolution, and insights.

## Memory Management
- **STM**: JSON files in `memory/short_term/`, cleared after conversation.
- **LTM**: JSON files in `memory/long_term/`, stores structured summaries, privacy-filtered, and used for context in future queries.
- **Context Retrieval**: Seller-configurable number of past interactions for context.

## Configuration System
- **llm_config.yaml**: LLM provider, model, and API key environment variable.
- **agents.yaml**: Agent roles, prompts, temperature, and token limits.
- **seller_config.yaml**: Summarization, privacy, and context settings.

## Dependencies
- `langchain`, `langchain-core`, `langchain-community`, `langchain[google-genai]`, `langchain[mistralai]`, `langchain[openai]`, `langgraph`, `openai`, `pydantic`, `pyyaml`, `python-dotenv`, `pytest`, `pytest-mock`, `pytest-cov`, `responses`, `fuzzywuzzy`, `scikit-fuzzy`, `numpy`, `scipy`, `networkx`, `python-Levenshtein`

## Usage Example

```python
# Initialize the conversational bot
bot = ConversationalBot()

# Process a customer query
result = bot.process_query(
    customer_id="customer_001",
    query="Quais smartphones vocês têm disponíveis?"
)

# Handle response
if result['success']:
    print(f"Response: {result['response']}")
    print(f"Token Usage: {result['token_usage']}")
else:
    print(f"Error: {result['error']}")
```

## Future Enhancements
- Database-backed memory (replace file-based storage)
- Advanced caching (e.g., Redis)
- Analytics dashboard for token usage and performance
- Multi-language support
- Web interface (REST API, frontend)
- Docker support
- Expanded test suite and documentation

## Conclusion

LangChainAgent is a robust, extensible, and production-ready conversational AI system for Portuguese customer service, featuring:
- Modular, multi-agent design
- Advanced memory and context management
- Flexible LLM provider support
- Real-world test coverage
- Security and privacy best practices

It is ready for further extension and deployment in demanding customer service environments. 
