# LangChainAgent API Layer Architecture Documentation

## Overview

The LangChainAgent API layer provides a comprehensive data access and service layer that integrates with the multi-agent LLM system. This documentation focuses specifically on the `LangChainAgent/api/` directory structure and its coordination with the agent system. The API has evolved into a full-featured service-oriented architecture supporting agent management, memory systems, and tool orchestration.

## Directory Structure

```
LangChainAgent/api/
├── __init__.py
├── config.py                    # Database configuration
├── database.py                  # SQLAlchemy engine and session setup
├── db_session.py               # Context manager for database sessions
├── domain/                     # Domain models (SQLAlchemy ORM)
│   ├── __init__.py
│   ├── base_agent_model.py     # Agent hierarchy and core models
│   ├── product.py              # Product domain model
│   ├── short_session_memory.py # Short-term memory models
│   └── task_model.py           # Task execution model
├── enum/                       # Enumeration definitions
│   ├── param_type_enum.py      # Parameter and data type enums
│   └── role_turn_enum.py       # Conversation role enums
├── schema/                     # Pydantic schemas for API validation
│   ├── __init__.py
│   ├── base_agent_schema.py    # Agent-related schemas
│   ├── product.py              # Product schemas
│   └── response.py             # Response and request schemas
├── service/                    # Business logic layer
│   ├── __init__.py
│   ├── agent_execution_history_service.py
│   ├── chat_service.py         # Main chat orchestration service
│   ├── long_memory_service.py  # Long-term memory management
│   ├── manager_agent_service.py # Manager agent coordination
│   ├── product_service.py      # Product data operations
│   ├── short_memory_service.py # Short-term memory management
│   ├── specialist_agent_service.py # Specialist agent execution
│   ├── tool_service.py         # Tool management and execution
│   └── writer_agent_service.py # Response generation service
└── util/                       # Utility classes
    └── llm_util.py             # LLM interaction utilities
```

## Core Infrastructure Components

### 1. Database Configuration (`config.py`)

**Purpose**: Centralized configuration management using Pydantic settings.

```python
class Settings(BaseSettings):
    DATABASE_URL: str = "postgresql+psycopg2://postgres:postgres@localhost:5432/teste"

    class Config:
        env_file = ".env"
```

**Key Features**:
- Environment-based configuration
- PostgreSQL database connection string
- Support for `.env` file overrides
- Type validation through Pydantic

### 2. Database Engine Setup (`database.py`)

**Purpose**: SQLAlchemy engine and session factory configuration.

```python
engine = create_engine(settings.DATABASE_URL, echo=False)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

**Key Features**:
- SQLAlchemy engine with PostgreSQL support
- Session factory for database connections
- Dependency injection pattern for FastAPI
- Automatic session cleanup

### 3. Session Management (`db_session.py`)

**Purpose**: Context manager for safe database session handling.

```python
@contextmanager
def get_db_session() -> Session:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

**Usage Pattern**:
```python
with get_db_session() as db:
    # Database operations
    pass
```

### 4. LLM Utilities (`util/llm_util.py`)

**Purpose**: Centralized LLM interaction and response parsing utilities.

**Key Methods**:
```python
class LlmUtil:
    def call_llm(self, prompt: str) -> Dict[str, Any]:
        # Calls LLM and parses JSON response

    def call_llm_str(self, prompt: str) -> str:
        # Calls LLM and returns raw string response

    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        # Robust JSON parsing with error handling
```

**Features**:
- Automatic LLM configuration loading
- Fallback LLM support
- JSON response parsing with error recovery
- Markdown code block cleaning
- Common JSON issue fixes

## Domain Layer

### 1. Agent Hierarchy Models (`domain/base_agent_model.py`)

**Purpose**: Complete agent system domain models with polymorphic inheritance.

**Core Models**:

#### BaseAgentModel (Abstract Base)
```python
class BaseAgentModel(Base):
    __tablename__ = 'base_agent'

    id: Mapped[str] = mapped_column(primary_key=True)
    type: Mapped[str] = mapped_column(String(50))  # discriminator
    name: Mapped[str]
    description: Mapped[str]
    prompt_template: Mapped[str]
    max_tokens: Mapped[int] = mapped_column(default=150)
    temperature: Mapped[float] = mapped_column(default=0.2)
    llm_primary_id: Mapped[Optional[str]] = mapped_column(ForeignKey("llm.id"))
    llm_fallback_id: Mapped[Optional[str]] = mapped_column(ForeignKey("llm.id"))
```

#### Specialized Agent Models
- **ManagerAgentModel**: Orchestrates specialist agents
- **SpecialistAgentModel**: Executes specific domain tasks with tools
- **WriterAgentModel**: Generates final responses

#### LLM Configuration Model
```python
class LlmModel(Base):
    id: Mapped[str] = mapped_column(primary_key=True)
    provider: Mapped[str]
    apiKey: Mapped[str]
    modelName: Mapped[str]
    tokenizerModelName: Mapped[str]
    useTransformersTokenizer: Mapped[bool]
```

#### Tool System Models
```python
class Tool(Base):
    id: Mapped[str] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String, unique=True)
    description: Mapped[str]
    goals: Mapped[str]
    parameters: Mapped[List["Parameter"]] = relationship(...)

class Parameter(Base):
    id: Mapped[str] = mapped_column(primary_key=True)
    label: Mapped[str]
    data_type: Mapped[DataType] = mapped_column(Enum(DataType))
    type: Mapped[ParameterType] = mapped_column(Enum(ParameterType))
```

#### Memory System Models
```python
class LongTermMemory(Base):
    __tablename__ = 'long_session_memory'

    id: Mapped[int] = mapped_column(primary_key=True)
    customer: Mapped[str]
    started_at: Mapped[datetime]
    ended_at: Mapped[Optional[datetime]]
    turns: Mapped[List["LongTurn"]] = relationship(...)

class LongTurn(Base):
    __tablename__ = "turn"

    id: Mapped[int] = mapped_column(primary_key=True)
    role: Mapped[RoleTurn] = mapped_column(Enum(RoleTurn))
    content: Mapped[str]
    time: Mapped[Optional[datetime]]
```

### 2. Product Domain Model (`domain/product.py`)

**Purpose**: SQLAlchemy ORM model representing the product entity.

**Database Schema**:
```sql
CREATE TABLE produtos (
    id VARCHAR PRIMARY KEY,
    name VARCHAR NOT NULL,
    brand VARCHAR NOT NULL,
    category VARCHAR NOT NULL,
    description TEXT,
    price FLOAT NOT NULL,
    atributos JSON,
    created_at TIMESTAMP DEFAULT NOW(),
    warranty VARCHAR,
    in_stock BOOLEAN DEFAULT TRUE,
    quantity INTEGER DEFAULT 0,
    rating FLOAT DEFAULT 0.0,
    reviews INTEGER DEFAULT 0,
    images JSON,
    fts_document TEXT,
    idnominal_classificacao TEXT
);
```

**Key Fields**:
- **Basic Info**: `id`, `name`, `brand`, `category`, `description`, `price`
- **Inventory**: `in_stock`, `quantity`
- **Customer Data**: `rating`, `reviews`
- **Media**: `images` (JSON field)
- **Search Optimization**: `fts_document`, `idnominal_classificacao`
- **Flexible Attributes**: `atributos` (JSON field for dynamic properties)

### 3. Memory Models (`domain/short_session_memory.py`)

**Purpose**: Pydantic models for short-term memory management.

```python
class ShortTurn(BaseModel):
    date_time: datetime
    question: str
    answer: str

class ShortTermMemory(BaseModel):
    customer: str
    turns: List[ShortTurn]
```

### 4. Task Execution Model (`domain/task_model.py`)

**Purpose**: Task coordination and execution tracking.

```python
class Task:
    def __init__(self, user_query: str, conversation_id: str):
        self.user_query = user_query
        self.conversation_id = conversation_id
        self.selected_agent = None
        self.selected_tool = None
        self.tool_result = None
        self.final_response = None
```

### 5. Enumeration Definitions (`enum/`)

#### Parameter Types (`enum/param_type_enum.py`)
```python
class DataType(enum.Enum):
    STRING = "STRING"
    NUMBER = "NUMBER"
    DICT = "DICT"
    LIST = "LIST"

class ParameterType(enum.Enum):
    IN = "IN"
    OUT = "OUT"
```

#### Conversation Roles (`enum/role_turn_enum.py`)
```python
class RoleTurn(enum.Enum):
    BOT = "BOT"
    USER = "USER"
```

## Service Layer

The service layer implements the business logic and coordinates between the domain models and the agent system.

### 1. Chat Service (`service/chat_service.py`)

**Purpose**: Main orchestration service for chat interactions.

```python
class ChatService:
    def __init__(self):
        db = SessionLocal()
        self.manager_agent_service = ManagerAgentService(db)
        self.specialist_agent_service = SpecialistAgentService(db)

    def processar_pergunta(self, pergunta: str, conversation_id: str | None) -> Dict[str, str]:
        # Main entry point for processing user questions
```

**Key Features**:
- Coordinates manager and specialist agents
- Handles conversation flow
- Error handling and logging

### 2. Manager Agent Service (`service/manager_agent_service.py`)

**Purpose**: Manages the orchestrator agent that coordinates specialist agents.

**Core Methods**:
```python
class ManagerAgentService:
    def find_by_id(self, manager_agent_id: str) -> ManagerAgentModel | None
    def executa(self, pergunta: str, customer_id: str | None) -> Dict[str, str]
    def construir_prompt(self, prompt_template: str, specialist_agents: list,
                        pergunta: str, short_session_memory: ShortTermMemory | None) -> str
```

**Execution Flow**:
1. Load manager agent configuration
2. Retrieve short-term memory context
3. Build prompt with available specialist agents
4. Call LLM for agent selection and task analysis
5. Execute selected specialist agents
6. Generate final response through writer agent
7. Save conversation turn to memory

### 3. Specialist Agent Service (`service/specialist_agent_service.py`)

**Purpose**: Executes domain-specific agents with their associated tools.

**Key Features**:
- Tool selection and execution
- Domain-specific processing
- Result formatting and validation

### 4. Writer Agent Service (`service/writer_agent_service.py`)

**Purpose**: Generates final user-facing responses.

**Key Features**:
- Response formatting
- Context-aware generation
- User-friendly output

### 5. Memory Management Services

#### Short Memory Service (`service/short_memory_service.py`)
```python
class ShortSessionMemoryService:
    def get_by_customer(self, customer_id: str) -> ShortTermMemory | None
    def save(self, short_memory: ShortTermMemory)
    def save_turn(self, customer_id: str, question: str, answer: str)
```

#### Long Memory Service (`service/long_memory_service.py`)
- Manages persistent conversation history
- Customer preference tracking
- Historical context retrieval

### 6. Tool Service (`service/tool_service.py`)

**Purpose**: Manages tool registration, selection, and execution.

**Key Features**:
- Dynamic tool loading
- Parameter validation
- Execution monitoring

### 7. Product Service (`service/product_service.py`)

**Purpose**: Data access layer providing business logic for product operations.

**Core Methods**:

#### Basic CRUD Operations
```python
def get_by_id(self, product_id: str) -> Product | None
def find_all_by_category(self, category: str) -> list[Product]
```

#### Search Operations
```python
def find_nominal(self, param) -> list[dict]:
    # Searches in name and description using ILIKE

def find_classificacao(self, param) -> list[Product]:
    # Searches in category and brand using ILIKE
```

#### Full-Text Search (PostgreSQL)
```python
def find_idnominal_classificacao(self, termo: str) -> list[dict]:
    # Uses PostgreSQL's full-text search with ranking
    # Returns ranked results with ts_rank scoring
```

**Advanced Search Features**:
- **Portuguese Language Support**: Uses `websearch_to_tsquery('portuguese', termo)`
- **Relevance Ranking**: `ts_rank()` function for result scoring
- **Optimized Queries**: Limit results to top 5 matches
- **Structured Results**: Returns dictionaries with rank scores

### 8. Agent Execution History Service (`service/agent_execution_history_service.py`)

**Purpose**: Tracks and logs agent execution for monitoring and debugging.

**Key Features**:
- Execution logging
- Performance monitoring
- Debug information storage

## Schema Layer

The schema layer provides Pydantic models for data validation, serialization, and API contracts.

### 1. Response Schemas (`schema/response.py`)

**Purpose**: Core request/response models for the chat API.

#### User Query Schema
```python
class UserQuery(BaseModel):
    question: str
    conversation_id: str | None = None
```

#### LLM Response Schema
```python
class LLMResponse(BaseModel):
    # Agent coordination fields
    agent: str | None = None
    tool: str | None = None
    query: str | None = None
    params: Dict[str, Any] = {}

    # Intent analysis fields
    intencao: str | None = None
    required_agents: list[Dict[str, Any]] | None = None

    # Task classification fields
    task_type: str | None = None
    is_follow_up: bool | None = None
    end_of_conversation: bool | None = None
    required_agent: str | None = None
    reasoning: str | None = None
    context_notes: str | None = None
    specialized_queries: Dict[str, Any] = {}
    product_context: Dict[str, Any] = {}
    relevant_products: list[Dict[str, Any]] | None = None

    # Tool execution fields
    tools: list[Dict[str, Any]] | None = None
    search_strategy: str | None = None
    expected_result_type: str | None = None

    # Results
    structured_result: Any | None = None
```

**Key Features**:
- Comprehensive LLM response parsing
- Flexible field structure for different agent types
- Support for complex nested data structures
- Optional fields for different use cases

### 2. Agent Schemas (`schema/base_agent_schema.py`)

**Purpose**: Schemas for agent configuration and management.

**Key Features**:
- Agent configuration validation
- Tool parameter schemas
- Execution result schemas

### 3. Product Schemas (`schema/product.py`)

**Purpose**: Data validation and serialization for product-related operations.

**Schema Hierarchy**:
```python
ProductBase (BaseModel)
├── ProductCreate
├── ProductResponse (with id, created_at)
└── ProductBasicResponse (minimal fields)

ProductSelect (field selector)
```

**Key Schemas**:

#### ProductBasicResponse
```python
class ProductBasicResponse(BaseModel):
    id: str
    name: str
    brand: str
    category: str
    description: str
    price: float
```

#### ProductResponse (Full)
```python
class ProductResponse(ProductBase):
    id: str
    created_at: datetime
    # Includes all ProductBase fields
```

## Integration with LangChain Agents

### 1. Service-Based Agent Architecture

The API layer now provides a complete service-based architecture that replaces the direct LangChain agent integration:

#### Current Architecture Flow
```
User Query → ChatService → ManagerAgentService → SpecialistAgentService → ToolService → Database
                                    ↓                        ↓                    ↓
                            LLM Analysis              Tool Execution      Product Search
                                    ↓                        ↓                    ↓
User Response ← WriterAgentService ← Response Generation ← Structured Results ← SQL Results
```

#### Manager Agent Integration
```python
class ManagerAgentService:
    def executa(self, pergunta: str, customer_id: str | None) -> Dict[str, str]:
        # 1. Load manager agent configuration from database
        manager_agent = self.find_by_id("941e9f44-6709-4f45-b9f9-0ce9d240a30c")

        # 2. Retrieve conversation context
        short_memory = short_memory_service.get_by_customer(customer_id)

        # 3. Build prompt with available specialist agents
        prompt = self.construir_prompt(manager_agent.prompt_template,
                                     manager_agent.specialist_agents,
                                     pergunta, short_memory)

        # 4. Call LLM for analysis
        llm_util = LlmUtil()
        contexto = LLMResponse(**(llm_util.call_llm(prompt)))

        # 5. Execute specialist agents
        for agent in contexto.required_agents:
            specialist_agent = specialist_agent_service.find_by_name(agent['agente'])
            resposta = specialist_agent_service.executa(specialist_agent, contexto)

        # 6. Generate final response
        writer_agent = writer_agent_service.find_by_name("agente_resposta_atendimento")
        resposta_final = writer_agent_service.executa(writer_agent, resposta_dict)

        return {"resposta": resposta_final, "conversation_id": customer_id}
```

#### Specialist Agent Integration
```python
class SpecialistAgentService:
    def executa(self, specialist_agent: SpecialistAgentModel, context: LLMResponse):
        # 1. Load agent tools from database
        tools = specialist_agent.tools

        # 2. Select appropriate tool based on context
        selected_tool = self.select_tool(tools, context)

        # 3. Execute tool with parameters
        tool_service = ToolService(self.db)
        result = tool_service.execute(selected_tool, context.params)

        return result
```

### 2. Database-Driven Configuration

The API layer now manages agent configurations in the database rather than YAML files:

#### Agent Configuration Storage
```python
# Agents are stored in the database with their configurations
class BaseAgentModel(Base):
    id: Mapped[str] = mapped_column(primary_key=True)
    name: Mapped[str]
    description: Mapped[str]
    prompt_template: Mapped[str]
    max_tokens: Mapped[int] = mapped_column(default=150)
    temperature: Mapped[float] = mapped_column(default=0.2)
    llm_primary_id: Mapped[Optional[str]] = mapped_column(ForeignKey("llm.id"))
    llm_fallback_id: Mapped[Optional[str]] = mapped_column(ForeignKey("llm.id"))
```

#### Tool Configuration Storage
```python
# Tools and their parameters are stored in the database
class Tool(Base):
    id: Mapped[str] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String, unique=True)
    description: Mapped[str]
    goals: Mapped[str]
    parameters: Mapped[List["Parameter"]] = relationship(...)
```

### 3. Memory System Integration

#### Short-Term Memory
```python
class ShortSessionMemoryService:
    def get_by_customer(self, customer_id: str) -> ShortTermMemory | None:
        # Retrieve recent conversation context

    def save_turn(self, customer_id: str, question: str, answer: str):
        # Save conversation turn for context
```

#### Long-Term Memory
```python
class LongTermMemory(Base):
    customer: Mapped[str]
    started_at: Mapped[datetime]
    ended_at: Mapped[Optional[datetime]]
    turns: Mapped[List["LongTurn"]] = relationship(...)
```

### 4. Tool System Integration

#### Dynamic Tool Loading
```python
class ToolService:
    def execute(self, tool: Tool, parameters: Dict[str, Any]) -> Dict[str, Any]:
        # 1. Validate parameters against tool schema
        # 2. Execute tool logic
        # 3. Return structured results
```

#### Product Search Tool Integration
```python
# Tools can now access the ProductService directly
class ProductSearchTool:
    def execute(self, query: str) -> Dict[str, Any]:
        with get_db_session() as db:
            product_service = ProductService(db)
            results = product_service.find_idnominal_classificacao(query)
            return {"results": results, "count": len(results)}
```

## API Architecture Patterns

### 1. Service-Oriented Architecture (SOA)

The API layer follows a service-oriented architecture pattern:

#### Service Layer Benefits
- **Separation of Concerns**: Each service handles a specific domain
- **Reusability**: Services can be used by multiple agents
- **Testability**: Individual services can be unit tested
- **Maintainability**: Changes are isolated to specific services

#### Service Dependencies
```
ChatService
├── ManagerAgentService
│   ├── ShortSessionMemoryService
│   ├── AgentExecutionHistoryService
│   └── LlmUtil
├── SpecialistAgentService
│   ├── ToolService
│   └── ProductService
└── WriterAgentService
    └── LlmUtil
```

### 2. Database-First Approach

#### Agent Configuration Management
- Agents are stored in the database with full configuration
- Dynamic loading of agent capabilities
- Runtime configuration updates without code changes

#### Tool Registry Pattern
```python
# Tools are registered in the database with their schemas
class Tool(Base):
    name: Mapped[str] = mapped_column(String, unique=True)
    description: Mapped[str]
    parameters: Mapped[List["Parameter"]] = relationship(...)

# Dynamic tool execution based on database configuration
class ToolService:
    def execute(self, tool_name: str, parameters: Dict) -> Dict:
        tool = self.find_by_name(tool_name)
        return self._execute_tool(tool, parameters)
```

### 3. Memory Management Architecture

#### Dual Memory System
- **Short-Term Memory**: Recent conversation context (in-memory/Redis)
- **Long-Term Memory**: Persistent conversation history (PostgreSQL)

#### Memory Integration Pattern
```python
class ManagerAgentService:
    def executa(self, pergunta: str, customer_id: str):
        # Combine short and long-term memory for context
        short_memory = short_memory_service.get_by_customer(customer_id)
        # long_memory = long_memory_service.get_by_customer(customer_id)

        # Build context-aware prompt
        prompt = self.construir_prompt(template, agents, pergunta, short_memory)
```

### 4. LLM Integration Pattern

#### Centralized LLM Management
```python
class LlmUtil:
    def call_llm(self, prompt: str) -> Dict[str, Any]:
        # 1. Load LLM configuration
        # 2. Handle primary/fallback LLM logic
        # 3. Parse and validate response
        # 4. Return structured data
```

#### Response Parsing Strategy
- Robust JSON extraction from LLM responses
- Error recovery for malformed JSON
- Fallback parsing strategies
- Structured response validation

## FastAPI Integration

### Main Application (`main.py`)

**Purpose**: FastAPI application with enhanced chat capabilities.

**Key Endpoints**:

#### Health Check
```python
@app.get("/")
def index():
    return {"hello": "World"}
```

#### Database Test
```python
@app.get("/test/database")
def test_database(db: Session = Depends(get_db)):
    # Tests database connectivity
```

#### Enhanced Chat Endpoint
```python
@app.post("/chat/mensagem")
def processa_mensagem(pergunta_request: PerguntaRequest, db: Session = Depends(get_db)):
    chat_service = ChatService()
    return chat_service.processar_pergunta(
        pergunta_request.texto,
        pergunta_request.conversation_id
    )
```

**Request Schema**:
```python
class PerguntaRequest(BaseModel):
    cliente_id: str
    texto: str
```

**Enhanced Features**:
- Full agent orchestration through ChatService
- Database-driven agent configuration
- Memory-aware conversation handling
- Comprehensive error handling and logging

## Performance Considerations

### 1. Database Optimization
- **Connection Pooling**: SQLAlchemy engine with connection pool
- **Query Optimization**: Indexed searches on `fts_document` and `idnominal_classificacao`
- **Result Limiting**: Built-in limits (e.g., 5 results) to prevent large result sets

### 2. Search Performance
- **Full-Text Search**: PostgreSQL native FTS for fast text searches
- **Ranking**: `ts_rank()` for relevance-based ordering
- **Language Support**: Portuguese-specific search configuration

### 3. Memory Management
- **Session Cleanup**: Automatic session disposal
- **Context Managers**: Safe resource handling
- **Lazy Loading**: SQLAlchemy lazy loading for related entities

## Current Architecture Benefits

### 1. Database-Driven Agent Management
- **Dynamic Configuration**: Agents can be configured and updated without code changes
- **Scalable Architecture**: New agents and tools can be added through database entries
- **Version Control**: Agent configurations are versioned and tracked
- **Runtime Flexibility**: Agent behavior can be modified based on database settings

### 2. Service-Oriented Design
- **Modular Services**: Each service handles a specific domain (memory, agents, tools)
- **Loose Coupling**: Services communicate through well-defined interfaces
- **Testability**: Individual services can be unit tested in isolation
- **Reusability**: Services can be shared across different agent types

### 3. Comprehensive Memory System
- **Short-Term Context**: Recent conversation turns for immediate context
- **Long-Term History**: Persistent conversation storage for customer insights
- **Memory Integration**: Seamless combination of short and long-term memory
- **Customer Tracking**: Individual customer conversation histories

### 4. Advanced Tool System
- **Dynamic Tool Loading**: Tools are loaded from database configuration
- **Parameter Validation**: Tool parameters are validated against database schemas
- **Execution Tracking**: Tool execution is logged for monitoring and debugging
- **Extensible Architecture**: New tools can be added without code changes

## Future Enhancements

### 1. Enhanced Agent Capabilities
- **Multi-modal Agents**: Support for image and voice processing
- **Agent Learning**: Agents that improve based on interaction history
- **Custom Agent Types**: User-defined agent types with custom behaviors
- **Agent Collaboration**: Multiple agents working together on complex tasks

### 2. Advanced Memory Features
- **Semantic Memory**: Vector-based memory for semantic search
- **Memory Summarization**: Automatic summarization of long conversations
- **Cross-Customer Insights**: Aggregate insights across customer interactions
- **Memory Optimization**: Intelligent memory pruning and archiving

### 3. Tool Ecosystem Expansion
- **External API Tools**: Integration with third-party APIs
- **Custom Tool Development**: Framework for developing custom tools
- **Tool Marketplace**: Shared repository of community-developed tools
- **Tool Analytics**: Performance monitoring and optimization

### 4. Performance and Scalability
- **Caching Layer**: Redis-based caching for frequently accessed data
- **Load Balancing**: Horizontal scaling of agent services
- **Async Processing**: Asynchronous task processing for long-running operations
- **Resource Optimization**: Intelligent resource allocation based on demand

### 5. Monitoring and Analytics
- **Real-time Monitoring**: Live dashboards for system health
- **Conversation Analytics**: Insights into conversation patterns and success rates
- **Agent Performance Metrics**: Detailed metrics on agent effectiveness
- **User Behavior Analysis**: Understanding user interaction patterns

## Configuration Management

### 1. Database Configuration (`config.py`)
```python
class Settings(BaseSettings):
    DATABASE_URL: str = "postgresql+psycopg2://postgres:postgres@localhost:5432/teste"

    class Config:
        env_file = ".env"
```

### 2. LLM Configuration (`config/llm_config.yaml`)
```yaml
llm:
  primary_llm: ${PRIMARY_LLM}
  fallback_llm: ${FALLBACK_LLM}

  llms:
    groq:
      provider: "groq"
      api_key_env_var: "GROQ_API_KEY"
      model_name: "llama-3.1-8b-instant"
    gemini:
      provider: "google_gemini"
      api_key_env_var: "GOOGLE_API_KEY"
      model_name: "gemini-2.0-flash"
    mistral:
      provider: "mistralai"
      api_key_env_var: "HUGGINGFACE_API_KEY"
      model_name: "mistral-medium"
```

### 3. Database-Driven Agent Configuration

Instead of YAML files, agents are now configured in the database:

```sql
-- Agent configuration stored in database
INSERT INTO base_agent (id, type, name, description, prompt_template, max_tokens, temperature)
VALUES (
    '941e9f44-6709-4f45-b9f9-0ce9d240a30c',
    'manager_agent',
    'Coordenador Principal',
    'Agente coordenador que analisa perguntas e seleciona especialistas',
    'Analise a pergunta: {pergunta}\nAgentes disponíveis: {agentes_disponiveis}\nRetorne JSON com agent e tool selecionados.',
    500,
    0.1
);

-- Tool configuration stored in database
INSERT INTO tool (id, name, description, goals)
VALUES (
    'product-search-tool',
    'Busca de Produtos',
    'Ferramenta para buscar produtos no catálogo',
    'Encontrar produtos relevantes baseado na consulta do usuário'
);
```

## Error Handling and Resilience

### 1. Service-Level Error Handling

#### Chat Service Error Handling
```python
class ChatService:
    def processar_pergunta(self, pergunta: str, conversation_id: str | None) -> Dict[str, str]:
        try:
            logger.info(f"Processando pergunta: {pergunta}")
            resposta = self.manager_agent_service.executa(pergunta, conversation_id)
            return resposta
        except Exception as e:
            logger.error(f"Erro no processamento: {str(e)}")
            raise HTTPException(status_code=500, detail="Erro interno")
```

#### Manager Agent Error Handling
```python
class ManagerAgentService:
    def executa(self, pergunta: str, customer_id: str | None) -> Dict[str, str]:
        try:
            # Main execution logic
            return {"resposta": resposta_final, "conversation_id": customer_id}
        except Exception as e:
            logger.error(f"Erro no processamento: {str(e)}")
            traceback.print_exc()
            raise HTTPException(status_code=500, detail="Erro")
```

### 2. Database Error Handling

#### Connection Management
```python
@contextmanager
def get_db_session() -> Session:
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        db.rollback()
        raise
    finally:
        db.close()
```

#### SQLAlchemy Error Handling
```python
class ManagerAgentService:
    def find_by_id(self, manager_agent_id: str) -> ManagerAgentModel | None:
        try:
            stmt = (select(ManagerAgentModel)
                   .options(selectinload(ManagerAgentModel.specialist_agents))
                   .where(ManagerAgentModel.id == manager_agent_id))
            return self.db.execute(stmt).scalar_one_or_none()
        except SQLAlchemyError as e:
            logger.error(f"Erro ao buscar ManagerAgent por ID: {str(e)}")
            raise
```

### 3. LLM Error Handling

#### Robust Response Parsing
```python
class LlmUtil:
    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        try:
            # Clean and parse JSON response
            analysis = json.loads(cleaned_response)
            return analysis
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON: {e}")
            logger.error(f"Raw response: {response}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error parsing response: {e}")
            return None
```

#### Fallback LLM Support
```python
# LLM configuration supports primary and fallback models
llm_config = {
    "primary_llm": "gemini",
    "fallback_llm": "groq"
}

# LlmManager handles fallback logic automatically
llm = llm_factory.get_llm_with_fallback()
```

## Testing Strategy

### 1. Unit Testing

#### Service Layer Testing
```python
# Test individual services in isolation
class TestManagerAgentService:
    def test_find_by_id(self):
        # Test agent retrieval

    def test_construir_prompt(self):
        # Test prompt construction logic

    def test_executa_success(self):
        # Test successful execution flow
```

#### Repository Testing
```python
class TestProductService:
    def test_get_by_id(self):
        # Test product retrieval by ID

    def test_find_idnominal_classificacao(self):
        # Test full-text search functionality
```

#### Schema Validation Testing
```python
class TestResponseSchemas:
    def test_llm_response_validation(self):
        # Test LLMResponse schema validation

    def test_user_query_validation(self):
        # Test UserQuery schema validation
```

### 2. Integration Testing

#### Service Integration Tests
```python
class TestChatServiceIntegration:
    def test_full_conversation_flow(self):
        # Test complete conversation from user query to response

    def test_memory_integration(self):
        # Test memory system integration
```

#### Database Integration Tests
```python
class TestDatabaseIntegration:
    def test_agent_configuration_loading(self):
        # Test loading agents from database

    def test_tool_execution_with_database(self):
        # Test tool execution with database operations
```

### 3. Mock Testing

#### Database Mocking
```python
@pytest.fixture
def mock_db_session():
    with patch('api.db_session.get_db_session') as mock:
        yield mock

@pytest.fixture
def mock_manager_agent():
    return ManagerAgentModel(
        id="test-manager",
        name="Test Manager",
        description="Test manager agent",
        prompt_template="Test prompt: {pergunta}"
    )
```

#### LLM Mocking
```python
@pytest.fixture
def mock_llm_util():
    with patch('api.util.llm_util.LlmUtil') as mock:
        mock.return_value.call_llm.return_value = {
            "agent": "test_agent",
            "tool": "test_tool",
            "required_agents": [{"agente": "specialist_test"}]
        }
        yield mock
```

### 4. Performance Testing

#### Load Testing
```python
class TestPerformance:
    def test_concurrent_conversations(self):
        # Test system under concurrent load

    def test_memory_usage(self):
        # Test memory consumption patterns

    def test_database_performance(self):
        # Test database query performance
```

## API Integration Patterns

### 1. Current State (JSON-based)
```python
# ProductSpecialistAgent currently uses JSON files
class ProductSpecialistAgent(BaseAgent):
    def __init__(self, llm, data_path: str = "../../data/"):
        self.products = self._load_products()  # Loads from JSON

    def _load_products(self):
        with open(f"{self.data_path}products.json", 'r') as f:
            return json.load(f)
```

### 2. Future State (Database-integrated)
```python
# Enhanced ProductSpecialistAgent with database integration
class ProductSpecialistAgent(BaseAgent):
    def __init__(self, llm, data_source_type: str = "json"):
        self.data_source_type = data_source_type
        if data_source_type == "postgresql":
            self.product_service = self._init_database_service()
        else:
            self.products = self._load_products()

    def _init_database_service(self):
        # Initialize database connection
        return ProductService

    def search_products(self, query: str):
        if self.data_source_type == "postgresql":
            with get_db_session() as db:
                service = ProductService(db)
                return service.find_idnominal_classificacao(query)
        else:
            return self._fuzzy_search_json(query)
```

### 3. Hybrid Approach
```python
# Combine database and in-memory search for optimal performance
class HybridProductService:
    def __init__(self):
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes

    def search_products(self, query: str):
        # Check cache first
        cache_key = f"search:{query}"
        if cache_key in self.cache:
            return self.cache[cache_key]

        # Database search
        with get_db_session() as db:
            service = ProductService(db)
            results = service.find_idnominal_classificacao(query)

        # Cache results
        self.cache[cache_key] = results
        return results
```

## Agent Tool Integration

### Database-Enabled Tools
```python
class DatabaseProductSearchTool(ProductTool):
    """Enhanced product search tool using database backend."""

    def __init__(self, agent_instance):
        super().__init__(
            name="database_product_search",
            description="Search products using PostgreSQL full-text search",
            agent_instance=agent_instance
        )

    def execute(self, query: str, search_type: str = "fulltext") -> Dict[str, Any]:
        try:
            with get_db_session() as db:
                service = ProductService(db)

                if search_type == "fulltext":
                    results = service.find_idnominal_classificacao(query)
                elif search_type == "nominal":
                    results = service.find_nominal(query)
                elif search_type == "category":
                    results = service.find_classificacao(query)
                else:
                    results = service.find_idnominal_classificacao(query)

                return {
                    "success": True,
                    "results": results,
                    "count": len(results),
                    "search_type": search_type
                }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "results": []
            }
```

## Data Migration Strategy

### 1. JSON to Database Migration
```python
# Migration script to populate database from JSON files
def migrate_json_to_database():
    with open("data/products.json", 'r') as f:
        products_data = json.load(f)

    with get_db_session() as db:
        for product_data in products_data:
            product = Product(**product_data)
            db.add(product)
        db.commit()
```

### 2. Search Index Optimization
```sql
-- Create full-text search indexes
CREATE INDEX idx_produtos_fts ON produtos USING gin(to_tsvector('portuguese', fts_document));
CREATE INDEX idx_produtos_idnominal ON produtos USING gin(to_tsvector('portuguese', idnominal_classificacao));

-- Create regular indexes for common queries
CREATE INDEX idx_produtos_category ON produtos(category);
CREATE INDEX idx_produtos_brand ON produtos(brand);
CREATE INDEX idx_produtos_price ON produtos(price);
```

## Summary

The LangChainAgent API layer has evolved into a comprehensive, service-oriented architecture that provides:

### Key Architectural Components

1. **Service Layer**: Modular services handling specific domains (chat, agents, memory, tools)
2. **Domain Models**: Complete SQLAlchemy models for agents, tools, memory, and products
3. **Schema Layer**: Pydantic models for validation and serialization
4. **Utility Layer**: Centralized LLM interaction and common utilities

### Core Capabilities

1. **Database-Driven Agent Management**: Agents and tools configured in database
2. **Dual Memory System**: Short-term and long-term memory integration
3. **Dynamic Tool System**: Runtime tool loading and execution
4. **Robust Error Handling**: Comprehensive error handling and fallback mechanisms
5. **LLM Integration**: Centralized LLM management with fallback support

### Integration Patterns

1. **Service Orchestration**: ChatService coordinates all agent interactions
2. **Memory-Aware Processing**: Context from previous conversations informs responses
3. **Tool-Based Execution**: Agents execute tasks through configurable tools
4. **Database-First Configuration**: All system configuration stored in database

### Development Benefits

1. **Maintainability**: Clear separation of concerns and modular design
2. **Testability**: Individual components can be tested in isolation
3. **Scalability**: Service-oriented architecture supports horizontal scaling
4. **Flexibility**: Database-driven configuration allows runtime modifications
5. **Extensibility**: New agents and tools can be added without code changes

This architecture provides a solid foundation for building sophisticated multi-agent conversational AI systems with comprehensive memory management, dynamic tool execution, and robust error handling. The database-driven approach ensures the system can evolve and scale while maintaining consistency and reliability.

## Migration Notes

### From Previous Architecture

The API layer has migrated from:
- **File-based configuration** → **Database-driven configuration**
- **Direct LangChain integration** → **Service-oriented architecture**
- **Simple repository pattern** → **Comprehensive service layer**
- **Basic memory management** → **Dual memory system**
- **Static tool loading** → **Dynamic tool system**

### Backward Compatibility

While the core architecture has changed significantly, the system maintains compatibility through:
- **Consistent API endpoints**: External interfaces remain stable
- **Configuration migration**: Tools to migrate from YAML to database configuration
- **Gradual adoption**: Services can be adopted incrementally
- **Fallback mechanisms**: Robust error handling ensures system stability

This comprehensive documentation provides a detailed overview of the current LangChainAgent API layer architecture, focusing specifically on its evolved structure, service-oriented design, and coordination with the multi-agent system. The API layer now serves as a robust, scalable foundation that can support advanced conversational AI features and continued system evolution.
