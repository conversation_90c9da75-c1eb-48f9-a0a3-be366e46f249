services:
  banco_de_dados:
    container_name: 'postgres'
    image: 'pgvector/pgvector:pg16'
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - '5432:5432'
    volumes:
      - ./volumes/postgres:/var/lib/postgresql/data
      - ./init:/docker-entrypoint-initdb.d

  redis:
    image: redis:7.2
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - ./volumes/redis:/data
