from contextlib import redirect_stdout, redirect_stderr
from io import String<PERSON>
from tkinter.font import names
from uuid import uuid4

from api.db_session import get_db_session
from api.domain.base_agent_model import SpecialistAgentModel
from api.service.chat_service import ChatService


def main():
    # # Cria banco de dados
    # Base.metadata.create_all(bind=engine)
    #
    # with get_db_session() as session:
    #     novo_agente = SpecialistAgentModel(
    #         id=str(uuid4()),
    #         name="agente_especialista_produto",
    #         type="specialist_agent",
    #         role="especialista",
    #         system_prompt="",
    #         max_tokens=200,
    #         temperature=0.3
    #     )
    #     session.add(novo_agente)
    #     session.commit()

    # bot = ChatBotAPIWrapper()
    chat_service = ChatService()
    print("=== Simple CLI ChatBot ===")
    customer_id = input("Enter customer ID: ").strip()
    while True:
        query = input("You: ").strip()
        if query.lower() in ("exit", "quit"):  # Allow user to exit
            print("Exiting chat. Goodbye!")
            break
        # Suppress all output except the response
        with String<PERSON>() as buf, redirect_stdout(buf), redirect_stderr(buf):
            response = chat_service.processar_pergunta(query, customer_id)
        print(f"Bot: {response}\n")


if __name__ == "__main__":
    main()
