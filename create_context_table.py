#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the context_memory table
Run this once to set up the context agent database table
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.database import engine
from api.domain.context_model import Base
from sqlalchemy import text

def create_context_table():
    """Create the context_memory table"""
    print("🔧 Creating context_memory table...")
    
    try:
        # Create all tables defined in the models
        Base.metadata.create_all(bind=engine)
        print("✅ context_memory table created successfully!")
        
        # Verify the table was created
        with engine.connect() as conn:
            result = conn.execute(text("SELECT COUNT(*) FROM context_memory")).scalar()
            print(f"✅ Table verified - contains {result} records")
            
        return True
        
    except Exception as e:
        print(f"❌ Error creating table: {e}")
        return False

def check_table_exists():
    """Check if context_memory table already exists"""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT COUNT(*) FROM context_memory")).scalar()
            print(f"✅ context_memory table already exists with {result} records")
            return True
    except Exception:
        print("ℹ️  context_memory table does not exist yet")
        return False

if __name__ == "__main__":
    print("🚀 Context Agent Table Setup")
    print("=" * 40)
    
    # Check if table already exists
    if not check_table_exists():
        # Create the table
        if create_context_table():
            print("\n🎉 Setup complete! You can now run the context agent tests.")
        else:
            print("\n❌ Setup failed. Please check your database connection.")
            sys.exit(1)
    else:
        print("\n✅ Table already exists. No action needed.")
