seller:
  name: "TechStore"
  description: "Technology retail store specializing in smartphones, laptops, and accessories"
  
  summary_settings:
    detail_level: "medium"  # low|medium|high
    privacy_level: "standard"  # minimal|standard|detailed
    context_interactions: 3  # Number of last interactions to retrieve for context
    include_personal_data: false
    include_pricing: true
    include_product_details: true
    max_summary_length: 200  # Maximum characters for summary text
    max_products_for_llm: 5  # If product list exceeds this, pre-parse before sending to LLM
    
  conversation_settings:
    auto_summarize: true  # Automatically summarize all conversations
    save_context: true  # Save conversation context for future interactions
    follow_up_detection: true  # Detect if follow-up is needed
    
  privacy_settings:
    data_retention_days: 365  # How long to keep conversation data
    anonymize_customer_data: false  # Whether to anonymize customer information
    export_restrictions: ["personal_data", "pricing"]  # Data that cannot be exported
    
  business_context:
    primary_products: ["smartphones", "laptops", "tablets", "accessories"]
    customer_segments: ["individual", "business", "student"]
    common_intents: ["product_research", "purchase_decision", "support_request", "price_inquiry"] 