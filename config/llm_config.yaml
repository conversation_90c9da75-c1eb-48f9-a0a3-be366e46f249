llm:
  primary_llm: ${PRIMARY_LLM}
  fallback_llm: gemini

  use_transformers_tokenizer: true

  llms:
    groq:
      provider: "groq"
      api_key_env_var: "GROQ_API_KEY"
      #deepseek-r1-distill-llama-70b
      #llama-3.1-8b-instant
      model_name: "llama-3.1-8b-instant"
      tokenizer_model_name: "gpt2"
    gemini:
      provider: "google_gemini"
      api_key_env_var: "GOOGLE_API_KEY"
      model_name: "gemini-2.0-flash"
      tokenizer_model_name: "gpt2"
    mistral:
      provider: "mistralai"
      api_key_env_var: "HUGGINGFACE_API_KEY"
      model_name: "mistral-medium"
      tokenizer_model_name: "mistralai/Mistral-7B-v0.1"