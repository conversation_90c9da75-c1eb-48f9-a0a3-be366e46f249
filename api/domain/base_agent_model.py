import uuid
from datetime import datetime
from typing import Optional, List

from sqlalchemy import String, ForeignKey, Table, Column, Enum, DateTime, JSON, UUID
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship

from api.enum.role_turn_enum import RoleTurn


class Base(DeclarativeBase):
    pass


manager_specialist_association = Table(
    "manager_specialist",
    Base.metadata,
    Column("manager_id", ForeignKey("manager_agent.id"), primary_key=True),
    Column("specialist_id", ForeignKey("specialist_agent.id"), primary_key=True)
)

specialist_tool_association = Table(
    "specialist_tool",
    Base.metadata,
    Column("specialist_id", ForeignKey("specialist_agent.id"), primary_key=True),
    Column("tool_id", UUID(as_uuid=True), ForeignKey("tool.id"), primary_key=True)
)


class BaseAgentModel(Base):
    __tablename__ = 'base_agent'

    id: Mapped[str] = mapped_column(primary_key=True)
    type: Mapped[str] = mapped_column(String(50))  # discriminator
    name: Mapped[str]
    description: Mapped[str]
    prompt_template: Mapped[str]
    max_tokens: Mapped[int] = mapped_column(default=150)
    temperature: Mapped[float] = mapped_column(default=0.2)
    llm_primary_id: Mapped[Optional[str]] = mapped_column(ForeignKey("llm.id"), nullable=True)
    llm_fallback_id: Mapped[Optional[str]] = mapped_column(ForeignKey("llm.id"), nullable=True)

    llm_primary: Mapped[Optional["LlmModel"]] = relationship(
        foreign_keys=[llm_primary_id],
        lazy="joined"
    )

    llm_fallback: Mapped[Optional["LlmModel"]] = relationship(
        foreign_keys=[llm_fallback_id],
        lazy="joined"
    )

    __mapper_args__ = {
        "polymorphic_on": type,
        "polymorphic_identity": "base_agent",
    }


class LlmModel(Base):
    __tablename__ = 'llm'

    id: Mapped[str] = mapped_column(primary_key=True)
    provider: Mapped[str]
    apiKey: Mapped[str]
    modelName: Mapped[str]
    tokenizerModelName: Mapped[str]
    useTransformersTokenizer: Mapped[bool]


class ManagerAgentModel(BaseAgentModel):
    __tablename__ = 'manager_agent'

    id: Mapped[str] = mapped_column(ForeignKey("base_agent.id"), primary_key=True)

    specialist_agents: Mapped[List["SpecialistAgentModel"]] = relationship(
        secondary=manager_specialist_association
    )

    __mapper_args__ = {
        "polymorphic_identity": "manager_agent",
    }


class SpecialistAgentModel(BaseAgentModel):
    __tablename__ = 'specialist_agent'

    id: Mapped[str] = mapped_column(ForeignKey("base_agent.id"), primary_key=True)

    tools: Mapped[List["Tool"]] = relationship(
        secondary=specialist_tool_association
    )

    __mapper_args__ = {
        "polymorphic_identity": "specialist_agent",
    }


class WriterAgentModel(BaseAgentModel):
    __tablename__ = 'writer_agent'

    id: Mapped[str] = mapped_column(ForeignKey("base_agent.id"), primary_key=True)

    __mapper_args__ = {
        "polymorphic_identity": "writer_agent",
    }


class Tool(Base):
    __tablename__ = 'tool'

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )

    name: Mapped[str] = mapped_column(String, unique=True)
    description: Mapped[str] = mapped_column()
    when_to_use: Mapped[str] = mapped_column()
    parameters_in: Mapped[dict] = mapped_column(JSON)
    parameters_out: Mapped[dict] = mapped_column(JSON)


class LongTermMemory(Base):
    __tablename__ = 'long_session_memory'

    id: Mapped[int] = mapped_column(primary_key=True)
    customer: Mapped[str]
    started_at: Mapped[datetime] = mapped_column(DateTime())
    ended_at: Mapped[Optional[datetime]] = mapped_column(DateTime())

    # Define a relação reversa com Turn
    turns: Mapped[List["LongTurn"]] = relationship(
        back_populates="session", cascade="all, delete-orphan")


class LongTurn(Base):
    __tablename__ = "turn"

    id: Mapped[int] = mapped_column(primary_key=True)
    role: Mapped[RoleTurn] = mapped_column(Enum(RoleTurn, native_enum=False), nullable=False)
    content: Mapped[str]
    time: Mapped[Optional[datetime]] = mapped_column(DateTime())

    session_id: Mapped[str] = mapped_column(ForeignKey("long_session_memory.id"), nullable=False)
    session: Mapped["LongTermMemory"] = relationship(back_populates="turns")


class AgentExecutionHistory(Base):
    __tablename__ = "agent_execution_history"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    agent_name: Mapped[str] = mapped_column()
    input: Mapped[str] = mapped_column()
    output: Mapped[str] = mapped_column()
