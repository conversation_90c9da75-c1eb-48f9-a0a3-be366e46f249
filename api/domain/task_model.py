class Task:
    def __init__(self, user_query: str, conversation_id: str):
        self.user_query = user_query  # Pergunta original do usuário
        self.conversation_id = conversation_id
        self.selected_agent = None  # Agente especialista selecionado pelo Coordenador
        self.selected_tool = None  # Ferramenta selecionada pelo Especialista
        self.tool_result = None  # Resultado da execução da ferramenta
        self.final_response = None  #
