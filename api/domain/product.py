from datetime import datetime

from sqlalchemy import Column, String, Float, <PERSON>olean, Integer, JSON, DateTime, Text

from api.database import Base


class Product(Base):
    __tablename__ = "produtos"

    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    brand = Column(String, nullable=False)
    category = Column(String, nullable=False)
    description = Column(Text)
    price = Column(Float, nullable=False)
    atributos = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    warranty = Column(String)
    in_stock = Column(Boolean, default=True)
    quantity = Column(Integer, default=0)
    rating = Column(Float, default=0.0)
    reviews = Column(Integer, default=0)
    images = Column(JSON, nullable=True)
    identificacao_nominal_tsv = Column(Text)
    classificacao_tsv = Column(Text)
    atributos_tsv = Column(Text)
