from sqlalchemy import Column, String, Text, DateTime, Integer, Float
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
from typing import Optional
from sqlalchemy.orm import Mapped, mapped_column
from pgvector.sqlalchemy import Vector

# Import the existing Base from base_agent_model to maintain consistency
from api.domain.base_agent_model import Base


class ContextMemory(Base):
    """
    Simple context storage for MVP approach
    Stores agent execution data and provides RAG-based context retrieval
    """
    __tablename__ = 'context_memory'

    # Basic fields
    id: Mapped[str] = mapped_column(String, primary_key=True)
    customer_id: Mapped[str] = mapped_column(String, index=True, nullable=False)
    timestamp: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)

    # Agent execution info
    agent_type: Mapped[str] = mapped_column(String, nullable=False)  # manager, specialist, writer
    query_text: Mapped[str] = mapped_column(Text, nullable=False)    # User query or agent input
    response_text: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # Agent response or output

    # Vector embedding using pgvector (384 dimensions for sentence-transformers/all-MiniLM-L6-v2)
    embedding: Mapped[Optional[Vector]] = mapped_column(Vector(384), nullable=True)

    # Simple metadata
    relevance_score: Mapped[float] = mapped_column(Float, default=0.0)
    tokens_used: Mapped[int] = mapped_column(Integer, default=0)

    def __repr__(self):
        return f"<ContextMemory(id={self.id}, customer_id={self.customer_id}, agent_type={self.agent_type})>"
