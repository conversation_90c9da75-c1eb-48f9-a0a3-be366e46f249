from pydantic import BaseModel
from typing import Dict, Any
from datetime import datetime


class ProductBase(BaseModel):
    name: str
    brand: str
    category: str
    description: str
    price: float
    atributos: Dict[str, Any]
    warranty: str
    in_stock: bool
    quantity: int
    rating: float
    reviews: int
    images: Dict[str, Any]


class ProductSelect():
    id: str
    name: str
    brand: str
    category: str
    description: str


class ProductCreate(ProductBase):
    pass


class ProductResponse(ProductBase):
    id: str
    created_at: datetime

    class Config:
        from_attributes = True


class ProductBasicResponse(BaseModel):
    id: str
    name: str
    brand: str
    category: str
    description: str
    price: float

    class Config:
        from_attributes = False
