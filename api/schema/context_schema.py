from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime


class ContextRequest(BaseModel):
    """Simple request for context operations"""
    customer_id: str
    query: str
    agent_type: str = "general"


class ContextItem(BaseModel):
    """Simple context item response"""
    id: str
    agent_type: str
    query: str
    response: Optional[str] = None
    timestamp: datetime
    similarity: float


class ContextResponse(BaseModel):
    """Simple context response"""
    contexts: List[ContextItem]
    enhanced_prompt: str


class CaptureRequest(BaseModel):
    """Simple capture request"""
    customer_id: str
    agent_type: str
    query: str
    response: Optional[str] = None


class CaptureResponse(BaseModel):
    """Simple capture response"""
    success: bool
    context_id: str
    message: str = "Context captured successfully"
