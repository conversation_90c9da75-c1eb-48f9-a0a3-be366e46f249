from typing import Dict, Any

from pydantic import BaseModel, validator, field_validator


class UserQuery(BaseModel):
    question: str
    conversation_id: str | None = None

    # @field_validator("question")
    # def validate_question(self, v):
    #     if len(v) > 500:
    #         raise ValueError("Pergunta muito longa")
    #     return v.strip()


class LLMResponse(BaseModel):
    agent: str | None = None
    tool: str | None = None
    query: str | None = None
    params: Dict[str, Any] = {}

    intencao: str | None = None
    required_agents: list[Dict[str, Any]] | None = None

    task_type: str | None = None,
    is_follow_up: bool | None = None,
    end_of_conversation: bool | None = None,
    required_agent: str | None = None,
    reasoning: str | None = None,
    context_notes: str | None = None,
    specialized_queries: Dict[str, Any] = {},
    product_context: Dict[str, Any] = {},
    relevant_products: list[Dict[str, Any]] | None = None

    tools: list[Dict[str, Any]] | None = None
    search_strategy: str | None = None
    expected_result_type: str | None = None

    structured_result: Any | None = None
