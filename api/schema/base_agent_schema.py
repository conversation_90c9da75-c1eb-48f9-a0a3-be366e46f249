from pydantic import BaseModel, Field
from typing import Optional, List


class BaseAgentResponse(BaseModel):
    id: str
    type: str
    role: str
    system_prompt: str
    max_tokens: int = Field(default=150)
    temperature: float = Field(default=0.2)
    llm_primary_id: Optional[str] = None
    llm_fallback_id: Optional[str] = None

    model_config = {
        "from_attributes": True
    }


class ManagerAgentResponse(BaseAgentResponse):
    specialist_agents: List["SpecialistAgentResponse"] = Field(default_factory=list)

    model_config = {
        "from_attributes": True
    }


class QueryAgentResponse(BaseAgentResponse):
    orchestrator_agent_id: Optional[str] = None

    model_config = {
        "from_attributes": True
    }


class SpecialistAgentResponse(BaseAgentResponse):
    model_config = {
        "from_attributes": True
    }


class OrchestratorAgentResponse(BaseAgentResponse):
    query_agent_id: Optional[str] = None
    specialist_agents: List[str] = Field(default_factory=list)

    model_config = {
        "from_attributes": True
    }
