import logging
from typing import List, Optional
import numpy as np
from sentence_transformers import SentenceTransformer
import threading

logger = logging.getLogger(__name__)


class EmbeddingUtil:
    """
    Utility class for generating embeddings using sentence-transformers
    Optimized for RAG context retrieval with caching and thread safety
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """Singleton pattern for model loading efficiency"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(EmbeddingUtil, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not getattr(self, '_initialized', False):
            self._initialize_model()
            self._initialized = True
    
    def _initialize_model(self):
        """Initialize the sentence transformer model"""
        try:
            # Using all-MiniLM-L6-v2: Fast, good quality, 384 dimensions
            # Perfect balance for RAG applications
            self.model_name = 'sentence-transformers/all-MiniLM-L6-v2'
            self.model = SentenceTransformer(self.model_name)
            self.embedding_dim = 384
            
            logger.info(f"Embedding model loaded: {self.model_name} (dim: {self.embedding_dim})")
            
        except Exception as e:
            logger.error(f"Failed to load embedding model: {e}")
            # Fallback to a smaller model if the main one fails
            try:
                self.model_name = 'all-MiniLM-L6-v2'
                self.model = SentenceTransformer(self.model_name)
                self.embedding_dim = 384
                logger.info(f"Fallback embedding model loaded: {self.model_name}")
            except Exception as fallback_error:
                logger.error(f"Fallback model also failed: {fallback_error}")
                self.model = None
                self.embedding_dim = 384
    
    def generate_embedding(self, text: str) -> Optional[List[float]]:
        """
        Generate embedding for a single text
        
        Args:
            text: Input text to embed
            
        Returns:
            List of floats representing the embedding, or None if failed
        """
        if not self.model or not text or not text.strip():
            return None
            
        try:
            # Clean and prepare text
            clean_text = text.strip()
            if len(clean_text) > 512:  # Truncate very long texts
                clean_text = clean_text[:512]
            
            # Generate embedding
            embedding = self.model.encode(clean_text, convert_to_numpy=True)
            
            # Convert to list for JSON serialization
            return embedding.tolist()
            
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            return None
    
    def generate_embeddings_batch(self, texts: List[str]) -> List[Optional[List[float]]]:
        """
        Generate embeddings for multiple texts efficiently
        
        Args:
            texts: List of input texts
            
        Returns:
            List of embeddings (or None for failed ones)
        """
        if not self.model or not texts:
            return [None] * len(texts)
        
        try:
            # Clean texts
            clean_texts = []
            for text in texts:
                if text and text.strip():
                    clean_text = text.strip()
                    if len(clean_text) > 512:
                        clean_text = clean_text[:512]
                    clean_texts.append(clean_text)
                else:
                    clean_texts.append("")
            
            # Generate embeddings in batch (more efficient)
            embeddings = self.model.encode(clean_texts, convert_to_numpy=True)
            
            # Convert to list format
            result = []
            for i, embedding in enumerate(embeddings):
                if clean_texts[i]:  # Only return embedding if text was valid
                    result.append(embedding.tolist())
                else:
                    result.append(None)
            
            return result
            
        except Exception as e:
            logger.error(f"Error generating batch embeddings: {e}")
            return [None] * len(texts)
    
    def compute_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        Compute cosine similarity between two embeddings
        
        Args:
            embedding1: First embedding
            embedding2: Second embedding
            
        Returns:
            Similarity score between 0 and 1
        """
        try:
            # Convert to numpy arrays
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # Compute cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            
            # Ensure result is between 0 and 1
            return max(0.0, min(1.0, (similarity + 1) / 2))
            
        except Exception as e:
            logger.error(f"Error computing similarity: {e}")
            return 0.0
    
    def is_available(self) -> bool:
        """Check if the embedding model is available"""
        return self.model is not None
    
    def get_model_info(self) -> dict:
        """Get information about the loaded model"""
        return {
            "model_name": getattr(self, 'model_name', 'None'),
            "embedding_dim": self.embedding_dim,
            "available": self.is_available()
        }
