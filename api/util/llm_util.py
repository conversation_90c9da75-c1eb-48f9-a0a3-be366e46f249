import json
import os
import re
from typing import Any, Dict

from core.utils.config_loader import ConfigLoader
from core.utils.llm_manager import LlmManager


class LlmUtil:

    def call_llm(self, prompt: str) -> Dict[str, Any]:
        """Call LLM and track tokens"""
        try:
            config_loader = ConfigLoader()
            llm_config = config_loader.load_config("config/llm_config.yaml")["llm"]
            # Expand env vars in primary_llm and fallback_llm
            for key in ["primary_llm", "fallback_llm"]:
                val = llm_config.get(key)
                if isinstance(val, str) and val.startswith("${") and val.endswith("}"):
                    env_key = val[2:-1]
                    llm_config[key] = os.environ.get(env_key, val)
            agents_config = config_loader.load_config("config/agents.yaml")["agents"]
            llm_factory = LlmManager(llm_config)
            llm = llm_factory.get_llm_with_fallback()
            from langchain_core.messages import HumanMessage
            # Always use invoke method for LLMs (chat and completion)
            response = llm.invoke([HumanMessage(content=prompt)])
            # Extract content from response
            if hasattr(response, 'content'):
                response_text = response.content
            else:
                response_text = str(response)

            return self._parse_llm_response(response_text)
        except Exception as e:
            print(f"Error calling LLM: {e}")
            raise

    def call_llm_str(self, prompt: str) -> str:
        """Call LLM and track tokens"""
        try:
            config_loader = ConfigLoader()
            llm_config = config_loader.load_config("config/llm_config.yaml")["llm"]
            # Expand env vars in primary_llm and fallback_llm
            for key in ["primary_llm", "fallback_llm"]:
                val = llm_config.get(key)
                if isinstance(val, str) and val.startswith("${") and val.endswith("}"):
                    env_key = val[2:-1]
                    llm_config[key] = os.environ.get(env_key, val)
            agents_config = config_loader.load_config("config/agents.yaml")["agents"]
            llm_factory = LlmManager(llm_config)
            llm = llm_factory.get_llm_with_fallback()
            from langchain_core.messages import HumanMessage
            # Always use invoke method for LLMs (chat and completion)
            response = llm.invoke([HumanMessage(content=prompt)])
            # Extract content from response
            if hasattr(response, 'content'):
                response_text = response.content
            else:
                response_text = str(response)

            return response_text
        except Exception as e:
            print(f"Error calling LLM: {e}")
            raise

    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        """Parse LLM response with robust JSON extraction"""
        try:
            # Clean the response - remove markdown code blocks
            cleaned_response = response.strip()

            # Remove markdown code blocks if present
            if cleaned_response.startswith('```json'):
                cleaned_response = cleaned_response.replace('```json', '').replace('```', '').strip()
            elif cleaned_response.startswith('```'):
                cleaned_response = cleaned_response.replace('```', '').strip()

            # Try to find JSON in the response using regex as fallback
            json_match = re.search(r'\{.*\}', cleaned_response, re.DOTALL)
            if json_match:
                cleaned_response = json_match.group()

            # Fix common JSON issues before parsing
            cleaned_response = self._fix_json_issues(cleaned_response)

            # Parse the JSON
            analysis = json.loads(cleaned_response)

            return analysis

        except json.JSONDecodeError as e:
            print(f"[QueryAgent] Error parsing JSON: {e}")
            print(f"[QueryAgent] Raw response: {response}")
            return None
        except Exception as e:
            print(f"[QueryAgent] Unexpected error parsing response: {e}")
            return None

    def _fix_json_issues(self, json_str: str) -> str:
        """Fix common JSON issues that cause parsing failures."""
        # Fix unquoted null-like values
        json_str = re.sub(r':\s*Nenhum\b', ': null', json_str)
        json_str = re.sub(r':\s*N/A\b', ': null', json_str)
        json_str = re.sub(r':\s*None\b', ': null', json_str)
        json_str = re.sub(r':\s*undefined\b', ': null', json_str)

        # Fix unquoted boolean values
        json_str = re.sub(r':\s*True\b', ': true', json_str)
        json_str = re.sub(r':\s*False\b', ': false', json_str)

        # Fix trailing commas
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*]', ']', json_str)

        return json_str
