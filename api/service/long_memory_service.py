from sqlalchemy.orm import Session

from api.domain.base_agent_model import LongTermMemory, LongTurn


class LongTermMemoryRepository:
    def __init__(self, db: Session):
        self.db = db

    def save(self, customer_id: str, summary: str, turns: list[dict]):
        try:
            ltm = LongTermMemory(
                customer_id=customer_id,
                summary=summary,
                turns=[
                    LongTurn(question=t['question'], answer=t['answer']) for t in turns
                ]
            )
            self.db.add(ltm)
            self.db.commit()
            return ltm
        except Exception as e:
            self.db.rollback()
            raise RuntimeError(f"Erro ao salvar memória longa: {str(e)}")

    def get_by_customer(self, customer_id: str) -> LongTermMemory | None:
        return (
            self.db.query(LongTermMemory)
            .filter(LongTermMemory.customer_id == customer_id)
            .first()
        )
