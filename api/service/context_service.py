import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from uuid import uuid4
from sqlalchemy.orm import Session
from sqlalchemy import text

from api.domain.context_model import ContextMemory
from api.schema.context_schema import ContextItem, ContextResponse, CaptureResponse
from api.util.embedding_util import EmbeddingUtil

logger = logging.getLogger(__name__)


class ContextService:
    """
    RAG-Enhanced Context Service

    Provides semantic context capture and retrieval using sentence-transformers
    and pgvector for high-quality similarity search.
    """

    def __init__(self, db: Session):
        self.db = db
        self.max_context_items = 5  # Increased for better context
        self.similarity_threshold = 0.7  # Semantic similarity threshold

        # Initialize embedding utility
        self.embedding_util = EmbeddingUtil()
        self.use_embeddings = self.embedding_util.is_available()

        if self.use_embeddings:
            logger.info("Context Service initialized with RAG (semantic search)")
        else:
            logger.warning("Context Service falling back to text matching (embeddings unavailable)")
            self.similarity_threshold = 0.1  # Lower threshold for text matching

    def capture(self, customer_id: str, agent_type: str, query: str, response: str = None) -> CaptureResponse:
        """
        RAG-enhanced capture with embedding generation
        Generates semantic embeddings for better retrieval
        """
        try:
            context_id = str(uuid4())

            # Generate embedding for the query (and response if available)
            text_to_embed = query
            if response:
                text_to_embed = f"{query} {response}"

            embedding = None
            if self.use_embeddings:
                embedding = self.embedding_util.generate_embedding(text_to_embed)
                if embedding is None:
                    logger.warning(f"Failed to generate embedding for context {context_id}")

            # Create context record
            context = ContextMemory(
                id=context_id,
                customer_id=customer_id,
                timestamp=datetime.utcnow(),
                agent_type=agent_type,
                query_text=query,
                response_text=response,
                embedding=embedding,
                relevance_score=0.0,
                tokens_used=len(query.split()) + (len(response.split()) if response else 0)
            )

            self.db.add(context)
            self.db.commit()

            logger.info(f"Context captured with {'semantic' if embedding else 'text'} indexing: {context_id}")

            return CaptureResponse(
                success=True,
                context_id=context_id,
                message="Context captured with semantic indexing" if embedding else "Context captured"
            )

        except Exception as e:
            self.db.rollback()
            logger.error(f"Context capture error: {e}")
            return CaptureResponse(
                success=False,
                context_id="",
                message=f"Error capturing context: {str(e)}"
            )

    def get_context(self, customer_id: str, query: str) -> List[Dict[str, Any]]:
        """
        RAG-enhanced context retrieval using semantic similarity
        Returns most semantically relevant context items
        """
        try:
            if self.use_embeddings:
                return self._get_context_semantic(customer_id, query)
            else:
                return self._get_context_text_fallback(customer_id, query)

        except Exception as e:
            logger.error(f"Context retrieval error: {e}")
            return []

    def _get_context_semantic(self, customer_id: str, query: str) -> List[Dict[str, Any]]:
        """Semantic similarity search using pgvector"""
        try:
            # Generate embedding for the query
            query_embedding = self.embedding_util.generate_embedding(query)
            if not query_embedding:
                logger.warning("Failed to generate query embedding, falling back to text search")
                return self._get_context_text_fallback(customer_id, query)

            # Use pgvector cosine similarity search
            sql = text("""
                SELECT id, agent_type, query_text, response_text, timestamp,
                       1 - (embedding <=> :query_embedding) as similarity
                FROM context_memory
                WHERE customer_id = :customer_id
                  AND embedding IS NOT NULL
                  AND 1 - (embedding <=> :query_embedding) > :threshold
                ORDER BY embedding <=> :query_embedding
                LIMIT :limit
            """)

            result = self.db.execute(sql, {
                "customer_id": customer_id,
                "query_embedding": str(query_embedding),
                "threshold": self.similarity_threshold,
                "limit": self.max_context_items
            })

            contexts = []
            for row in result:
                contexts.append({
                    "id": row.id,
                    "agent_type": row.agent_type,
                    "query": row.query_text,
                    "response": row.response_text,
                    "timestamp": row.timestamp,
                    "similarity": float(row.similarity)
                })

            logger.debug(f"Found {len(contexts)} semantic matches for query: {query[:50]}...")
            return contexts

        except Exception as e:
            logger.error(f"Semantic search error: {e}")
            return self._get_context_text_fallback(customer_id, query)

    def _get_context_text_fallback(self, customer_id: str, query: str) -> List[Dict[str, Any]]:
        """Fallback text-based similarity search"""
        try:
            query_words = set(query.lower().split())

            # Get recent contexts for this customer
            sql = text("""
                SELECT id, agent_type, query_text, response_text, timestamp
                FROM context_memory
                WHERE customer_id = :customer_id
                  AND query_text IS NOT NULL
                ORDER BY timestamp DESC
                LIMIT 20
            """)

            result = self.db.execute(sql, {"customer_id": customer_id})

            contexts = []
            for row in result:
                # Simple similarity calculation based on word overlap
                context_words = set(row.query_text.lower().split())
                if row.response_text:
                    context_words.update(row.response_text.lower().split())

                # Calculate Jaccard similarity
                intersection = len(query_words.intersection(context_words))
                union = len(query_words.union(context_words))
                similarity = intersection / union if union > 0 else 0.0

                # Only include contexts with some relevance
                if similarity > self.similarity_threshold:
                    contexts.append({
                        "id": row.id,
                        "agent_type": row.agent_type,
                        "query": row.query_text,
                        "response": row.response_text,
                        "timestamp": row.timestamp,
                        "similarity": similarity
                    })

            # Sort by similarity and return top results
            contexts.sort(key=lambda x: x["similarity"], reverse=True)
            return contexts[:self.max_context_items]

        except Exception as e:
            logger.error(f"Text fallback search error: {e}")
            return []

    def inject_context(self, base_prompt: str, contexts: List[Dict[str, Any]]) -> str:
        """
        Enhanced context injection with semantic relevance
        Provides richer context formatting based on similarity scores
        """
        if not contexts:
            return base_prompt

        # Build enhanced context section
        context_lines = []
        for i, ctx in enumerate(contexts[:3], 1):  # Top 3 contexts
            similarity_indicator = "🔥" if ctx['similarity'] > 0.8 else "✨" if ctx['similarity'] > 0.6 else "💡"

            if ctx.get('response'):
                # Truncate long responses intelligently
                response = ctx['response']
                if len(response) > 150:
                    # Try to find a good breaking point
                    truncated = response[:150]
                    last_sentence = truncated.rfind('.')
                    if last_sentence > 100:
                        response = response[:last_sentence + 1]
                    else:
                        response = truncated + "..."

                context_lines.append(f"[{i}] {similarity_indicator} {ctx['query']} → {response}")
            else:
                context_lines.append(f"[{i}] {similarity_indicator} {ctx['query']}")

        context_section = "\n".join(context_lines)

        # Enhanced injection with relevance information
        enhanced_prompt = f"""Relevant context from previous interactions:
{context_section}

Current request: {base_prompt}

Note: Use the context above to provide more personalized and consistent responses."""

        return enhanced_prompt

    def get_context_stats(self, customer_id: str) -> Dict[str, Any]:
        """
        Get simple statistics about stored context for a customer
        """
        try:
            sql = text("""
                SELECT 
                    COUNT(*) as total_contexts,
                    COUNT(DISTINCT agent_type) as agent_types_used,
                    MAX(timestamp) as last_interaction
                FROM context_memory
                WHERE customer_id = :customer_id
            """)
            
            result = self.db.execute(sql, {"customer_id": customer_id}).first()
            
            return {
                "total_contexts": result.total_contexts or 0,
                "agent_types_used": result.agent_types_used or 0,
                "last_interaction": result.last_interaction,
                "context_available": (result.total_contexts or 0) > 0
            }
            
        except Exception as e:
            logger.error(f"Error getting context stats: {e}")
            return {
                "total_contexts": 0,
                "agent_types_used": 0,
                "last_interaction": None,
                "context_available": False
            }
