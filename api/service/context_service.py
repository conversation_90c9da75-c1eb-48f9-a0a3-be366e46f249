import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from uuid import uuid4
from sqlalchemy.orm import Session
from sqlalchemy import text

from api.domain.context_model import ContextMemory
from api.schema.context_schema import ContextItem, ContextResponse, CaptureResponse

logger = logging.getLogger(__name__)


class ContextService:
    """
    MVP Context Service - Simple and Fast
    
    Provides context capture and retrieval without LLM overhead.
    Uses simple similarity matching for MVP implementation.
    """

    def __init__(self, db: Session):
        self.db = db
        self.max_context_items = 3  # Keep it simple for MVP
        
        # For MVP, we'll use simple text matching instead of embeddings
        # This can be upgraded to sentence-transformers later
        self.use_embeddings = False

    def capture(self, customer_id: str, agent_type: str, query: str, response: str = None) -> CaptureResponse:
        """
        Simple capture - just store in database
        Fast operation with minimal overhead (< 5ms target)
        """
        try:
            context_id = str(uuid4())
            
            # Create context record
            context = ContextMemory(
                id=context_id,
                customer_id=customer_id,
                timestamp=datetime.utcnow(),
                agent_type=agent_type,
                query_text=query,
                response_text=response,
                embedding_json=None,  # Will be generated later if needed
                relevance_score=0.0,
                tokens_used=len(query.split()) + (len(response.split()) if response else 0)
            )

            self.db.add(context)
            self.db.commit()
            
            logger.info(f"Context captured: {context_id} for customer {customer_id}")
            
            return CaptureResponse(
                success=True,
                context_id=context_id,
                message="Context captured successfully"
            )

        except Exception as e:
            self.db.rollback()
            logger.error(f"Context capture error: {e}")
            return CaptureResponse(
                success=False,
                context_id="",
                message=f"Error capturing context: {str(e)}"
            )

    def get_context(self, customer_id: str, query: str) -> List[Dict[str, Any]]:
        """
        Simple context retrieval using text similarity
        Returns top 3 most relevant context items
        Fast operation (< 50ms target)
        """
        try:
            # For MVP: Simple keyword-based matching
            # This can be upgraded to vector similarity later
            query_words = set(query.lower().split())
            
            # Get recent contexts for this customer
            sql = text("""
                SELECT id, agent_type, query_text, response_text, timestamp
                FROM context_memory
                WHERE customer_id = :customer_id
                  AND query_text IS NOT NULL
                ORDER BY timestamp DESC
                LIMIT 10
            """)

            result = self.db.execute(sql, {"customer_id": customer_id})
            
            contexts = []
            for row in result:
                # Simple similarity calculation based on word overlap
                context_words = set(row.query_text.lower().split())
                if row.response_text:
                    context_words.update(row.response_text.lower().split())
                
                # Calculate Jaccard similarity
                intersection = len(query_words.intersection(context_words))
                union = len(query_words.union(context_words))
                similarity = intersection / union if union > 0 else 0.0
                
                # Only include contexts with some relevance
                if similarity > 0.1:
                    contexts.append({
                        "id": row.id,
                        "agent_type": row.agent_type,
                        "query": row.query_text,
                        "response": row.response_text,
                        "timestamp": row.timestamp,
                        "similarity": similarity
                    })

            # Sort by similarity and return top results
            contexts.sort(key=lambda x: x["similarity"], reverse=True)
            return contexts[:self.max_context_items]

        except Exception as e:
            logger.error(f"Context retrieval error: {e}")
            return []

    def inject_context(self, base_prompt: str, contexts: List[Dict[str, Any]]) -> str:
        """
        Simple template-based context injection
        No LLM needed - just string formatting
        Fast operation (< 2ms target)
        """
        if not contexts:
            return base_prompt

        # Build simple context section
        context_lines = []
        for i, ctx in enumerate(contexts[:2], 1):  # Top 2 contexts for MVP
            if ctx.get('response'):
                context_lines.append(f"[{i}] Previous: {ctx['query']} → {ctx['response'][:100]}...")
            else:
                context_lines.append(f"[{i}] Previous: {ctx['query']}")

        context_section = "\n".join(context_lines)

        # Simple injection at the beginning of the prompt
        enhanced_prompt = f"""Context from previous interactions:
{context_section}

Current request: {base_prompt}"""

        return enhanced_prompt

    def get_context_stats(self, customer_id: str) -> Dict[str, Any]:
        """
        Get simple statistics about stored context for a customer
        """
        try:
            sql = text("""
                SELECT 
                    COUNT(*) as total_contexts,
                    COUNT(DISTINCT agent_type) as agent_types_used,
                    MAX(timestamp) as last_interaction
                FROM context_memory
                WHERE customer_id = :customer_id
            """)
            
            result = self.db.execute(sql, {"customer_id": customer_id}).first()
            
            return {
                "total_contexts": result.total_contexts or 0,
                "agent_types_used": result.agent_types_used or 0,
                "last_interaction": result.last_interaction,
                "context_available": (result.total_contexts or 0) > 0
            }
            
        except Exception as e:
            logger.error(f"Error getting context stats: {e}")
            return {
                "total_contexts": 0,
                "agent_types_used": 0,
                "last_interaction": None,
                "context_available": False
            }
