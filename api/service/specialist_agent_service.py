import json
import logging
from typing import Dict, Callable

from sqlalchemy import select, func, literal_column
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from api.domain.base_agent_model import SpecialistAgentModel, Tool, AgentExecutionHistory
from api.domain.product import Product
from api.schema.response import LLMResponse
from api.service.agent_execution_history_service import AgentExecutionHistoryService
from api.service.tool_service import ToolService
from api.service.context_service import ContextService
from api.util.llm_util import LlmUtil

logger = logging.getLogger(__name__)


class SpecialistAgentService:

    def __init__(self, db: Session):
        self.db = db
        self.ferramentas: Dict[str, Callable] = {
            "sql_query": self.find_idnominal_classificacao
        }

    def find_by_name(self, specialist_agent_name: str) -> SpecialistAgentModel | None:
        try:
            stmt = (select(SpecialistAgentModel)
                    .where(SpecialistAgentModel.name == specialist_agent_name))
            return self.db.execute(stmt).scalar_one_or_none()
        except SQLAlchemyError as e:
            logger.error(f"Erro ao buscar ManagerAgent por ID: {str(e)}")
            raise

    def find_idnominal_classificacao(self, termo: str) -> list[dict]:
        try:
            search_query = func.websearch_to_tsquery('portuguese', termo).label("search_query")
            stmt = (select(
                Product.id,
                func.ts_rank(Product.idnominal_classificacao, search_query).label("rank"),
                Product.name,
                Product.brand,
                Product.category,
                Product.description,
                Product.price
            )
                    .select_from(Product)
                    .add_columns(search_query)  # Para fazer cross join implícito com search_query
                    .where(Product.idnominal_classificacao.op('@@')(search_query))
                    .order_by(literal_column("rank").desc())
                    .limit(5))

            resultado = self.db.execute(stmt).mappings().all()
            return [dict(row) for row in resultado]
        except Exception as e:
            logger.error(f"Erro na busca de produtos: {str(e)}")
            raise

    # async def executar_consulta_sql(self, query: str, params: Dict[str, Any]) -> str:
    #     try:
    #         with SessionLocal() as db:
    #             result = db.execute(text(query), params).fetchall()
    #             logger.info(f"Consulta SQL executada: {query}")
    #             return str(result)
    #     except Exception as e:
    #         logger.error(f"Erro na consulta SQL: {str(e)}")
    #         raise HTTPException(status_code=500, detail="Erro ao executar consulta")

    def tools_to_json(self, tools: list[Tool]) -> str:

        partes = []
        for i, tool in enumerate(tools, 1):
            texto = f"""
               Ferramenta #{i}:
               - Nome: {tool.name}
               - Descrição: {tool.description} \n {tool.when_to_use}
               - Parâmetro(s) de entrada: {json.dumps(tool.parameters_in, ensure_ascii=False, indent=2)}
               """
            partes.append(texto.strip())
        return "\n\n".join(partes)

    def construir_prompt(self, prompt_template: str,
                         tools: list[Tool],
                         contexto: LLMResponse) -> str:
        # Filtrar contexto relevante (ex.: palavras-chave relacionadas)
        # stm_summary = " ".join([turno.get("pergunta", "") for turno in contexto["stm"]])[:200]
        # ltm_summary = contexto["ltm"].get("categorias_preferidas", "")[:100]

        # return f"""
        # Pergunta: {pergunta}
        # Contexto recente: {stm_summary}
        # Preferências do usuário: {ltm_summary}
        # Agentes disponíveis: {}
        # """
        tools_prompt = self.tools_to_json(tools)

        contexto_prompt = contexto.intencao
        prompt = (prompt_template.replace("{query}", contexto.query)
                  .replace("{tools_description}", tools_prompt)
                  .replace("{query_intent}", contexto_prompt))
        return prompt

    def executa(self, specialist_agent: SpecialistAgentModel, contexto: LLMResponse) -> dict:
        try:
            # Initialize context service for MVP context agent integration
            context_service = ContextService(self.db)

            # Get context for specialist agent (if customer_id is available)
            customer_id = getattr(contexto, 'customer_id', None)
            if customer_id:
                specialist_contexts = context_service.get_context(customer_id, contexto.query)

                # Build base prompt
                base_prompt = self.construir_prompt(specialist_agent.prompt_template,
                                                   specialist_agent.tools,
                                                   contexto)

                # Enhance prompt with context
                prompt = context_service.inject_context(base_prompt, specialist_contexts)
            else:
                # Fallback to original behavior if no customer_id
                prompt = self.construir_prompt(specialist_agent.prompt_template,
                                               specialist_agent.tools,
                                               contexto)

            llm_util = LlmUtil()
            llm_response = llm_util.call_llm(prompt)

            # Handle case where LLM returns None
            if llm_response is None:
                logger.error("LLM returned None response")
                # Create a minimal fallback response
                llm_response = {
                    "tools": [{"name": "find_product_by_identificacao_nominal", "params": {"identificacao_nominal": contexto.query}}],
                    "search_strategy": "basic",
                    "expected_result_type": "product_list"
                }

            contexto_novo = LLMResponse(**llm_response)

            agent_execution_history = AgentExecutionHistory(
                agent_name=specialist_agent.name,
                input=prompt,
                output=contexto_novo.model_dump_json()
            )
            agent_execution_history_service = AgentExecutionHistoryService(self.db)
            agent_execution_history_service.save(agent_execution_history)

            print(contexto_novo)

            tool_service = ToolService(self.db)
            for item in contexto_novo.tools:
                resultado_tool = tool_service.executa(item)

            contexto_novo.query = contexto.query
            contexto_novo.intencao = contexto.intencao
            contexto_novo.structured_result = resultado_tool

            # Capture specialist agent execution in context (MVP approach)
            if customer_id:
                context_service.capture(
                    customer_id=customer_id,
                    agent_type="specialist_agent",
                    query=contexto.query,
                    response=str(resultado_tool)
                )

            return contexto_novo
        except Exception as e:
            logger.error(f"Erro na execução do especialista: {str(e)}")
            raise

#     def find_by(self, column: ProductSelect, value) -> list[ProductBasicResponse]:
#         try:
#             criterio = lambda column, value: getattr(column).ilike(value)
#             return self.db.query(Product).where(criterio).all()
#         except Exception as e:
#             logger.error(f"Erro ao buscar produtos por categoria: {str(e)}")
#             raise
#
#     def find_all_by_category(self, category: str) -> list[Product]:
#         try:
#             return self.db.query(Product).filter(Product.category == category).all()
#         except Exception as e:
#             logger.error(f"Erro ao buscar produtos por categoria: {str(e)}")
#             raise
#
#     def find_nominal(self, param) -> list[dict]:
#         try:
#             query_stmt = select(Product).where(or_(
#                 Product.name.ilike('%' + param + '%'),
#                 Product.description.ilike('%' + param + '%')
#             ))
#             resultado = self.db.execute(query_stmt).mappings().all()
#             return [dict(row) for row in resultado]
#         except Exception as e:
#             logger.error(f"Erro ao buscar produtos por categoria: {str(e)}")
#             raise
#
#     def find_classificacao(self, param) -> list[Product]:
#         try:
#             query_stmt = select(Product).where(or_(
#                 Product.category.ilike('%' + param + '%'),
#                 Product.brand.ilike('%' + param + '%')
#             ))
#             return self.db.execute(query_stmt).scalars().all()
#         except Exception as e:
#             logger.error(f"Erro ao buscar produtos por categoria: {str(e)}")
#             raise
#
#     # def buscar_artigos_por_texto(session: Session, termo: str):
#     #     query = select(Artigo).where(
#     #         func.to_tsvector('portuguese', Artigo.conteudo).op('@@')(
#     #             func.to_tsquery('portuguese', termo)
#     #         )
#     #     )
#     #     resultado = session.execute(query)
#     #     return resultado.scalars().all()
#
#     def find_idnominal_classificacao(self, termo: str) -> list[dict]:
#         try:
#             search_query = func.websearch_to_tsquery('portuguese', termo).label("search_query")
#             stmt = (select(
#                 Product.id,
#                 func.ts_rank(Product.idnominal_classificacao, search_query).label("rank"),
#                 Product.name,
#                 Product.brand,
#                 Product.category,
#                 Product.description,
#                 Product.price
#             )
#                     .select_from(Product)
#                     .add_columns(search_query)  # Para fazer cross join implícito com search_query
#                     .where(Product.idnominal_classificacao.op('@@')(search_query))
#                     .order_by(literal_column("rank").desc())
#                     .limit(5))
#
#             resultado = self.db.execute(stmt).mappings().all()
#             return [dict(row) for row in resultado]
#         except Exception as e:
#             logger.error(f"Erro na busca de produtos: {str(e)}")
#             raise
#
# # def search_products_by_text(db: Session, query: str) -> list[Product]:
# #     try:
# #         # Versão mais segura da busca
# #         sql = text("""
# #         SELECT * FROM products
# #         WHERE to_tsvector('portuguese',
# #             COALESCE(name, '') || ' ' ||
# #             COALESCE(description, '') || ' ' ||
# #             COALESCE(category, '')
# #         ) @@ plainto_tsquery('portuguese', :query)
# #     """)
# #         result = db.execute(sql, {"query": query})
# #         return result.fetchall()
# #
# #     except Exception as e:
# #         logger.error(f"Erro na busca de produtos: {str(e)}")
# #         raise
# #
# # def create_product(db: Session, product_data: ProductCreate) -> Product:
# #     try:
# #         # Usar model_dump() em vez de dict() para Pydantic v2
# #         product_dict = product_data.model_dump()
# #         product = Product(**product_dict)
# #         db.add(product)
# #         db.commit()
# #         db.refresh(product)
# #         return product
# #     except Exception as e:
# #         logger.error(f"Erro ao criar produto: {str(e)}")
# #         db.rollback()
# #         raise
