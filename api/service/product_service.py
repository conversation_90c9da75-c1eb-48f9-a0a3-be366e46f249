import logging
from typing import Any

from sqlalchemy import select, func, literal_column
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from api.domain.product import Product

logger = logging.getLogger(__name__)


class ProductService:
    def __init__(self, db: Session):
        self.db = db

    # CREATE TABLE public.produtos (
    # 	id text NOT NULL,
    # 	"name" text NULL,
    # 	brand text NULL,
    # 	category text NULL,
    # 	description text NULL,
    # 	price numeric NULL,
    # 	atributos jsonb NULL,
    # 	full_text text NULL,
    # 	created_at timestamp DEFAULT now() NULL,
    # 	warranty text NULL,
    # 	in_stock bool NULL,
    # 	quantity int4 NULL,
    # 	rating numeric NULL,
    # 	reviews int4 NULL,
    # 	images _text NULL,
    # 	identificacao_nominal_tsv tsvector NULL,
    # 	classificacao_tsv tsvector NULL,
    # 	atributos_tsv tsvector NULL,
    # 	CONSTRAINT produtos_pkey PRIMARY KEY (id)
    # );

    def find_product_by_id(self, product_id: dict) -> list[dict[str, Any]]:
        try:
            stmt = (select(Product.id,
                           Product.name,
                           Product.brand,
                           Product.category,
                           Product.description,
                           Product.price)
                    .select_from(Product)
                    .where(Product.id == product_id['produto_id'].lower()))
            resultado = self.db.execute(stmt).mappings().all()
            return [dict(row) for row in resultado]
        except SQLAlchemyError as e:
            logger.error(f"Erro ao buscar ManagerAgent por ID: {str(e)}")
            raise

    def find_product_by_identificacao_nominal(self, parameters: dict) -> list[dict[str, Any]]:
        try:
            search_query = (func.websearch_to_tsquery('portuguese',
                                                      parameters['identificacao_nominal'])
                            .label("search_query"))
            stmt = (select(
                Product.id,
                func.ts_rank(Product.identificacao_nominal_tsv, search_query).label("rank"),
                Product.name,
                Product.brand,
                Product.category,
                Product.description,
                Product.price
            )
                    .select_from(Product)
                    .add_columns(search_query)  # Para fazer cross join implícito com search_query
                    .where(Product.identificacao_nominal_tsv.op('@@')(search_query))
                    .order_by(literal_column("rank").desc())
                    .limit(5))

            resultado = self.db.execute(stmt).mappings().all()
            return [dict(row) for row in resultado]
        except Exception as e:
            logger.error(f"Erro na busca de produtos: {str(e)}")
            raise

    def find_product_by_classificacao(self, parameters: dict) -> list[dict[str, Any]]:
        try:
            search_query = (func.websearch_to_tsquery('portuguese',
                                                      parameters['classificacao'])
                            .label("search_query"))
            stmt = (select(
                Product.id,
                func.ts_rank(Product.classificacao_tsv, search_query).label("rank"),
                Product.name,
                Product.brand,
                Product.category,
                Product.description,
                Product.price
            )
                    .select_from(Product)
                    .add_columns(search_query)  # Para fazer cross join implícito com search_query
                    .where(Product.classificacao_tsv.op('@@')(search_query))
                    .order_by(literal_column("rank").desc())
                    .limit(5))

            resultado = self.db.execute(stmt).mappings().all()
            return [dict(row) for row in resultado]
        except Exception as e:
            logger.error(f"Erro na busca de produtos: {str(e)}")
            raise

    def find_product_by_atributos(self, parameters: dict) -> list[dict[str, Any]]:
        try:
            search_query = (func.websearch_to_tsquery('portuguese', parameters['atributo'])
                            .label("search_query"))
            stmt = (select(
                Product.id,
                func.ts_rank(Product.atributos_tsv, search_query).label("rank"),
                Product.name,
                Product.brand,
                Product.category,
                Product.description,
                Product.price
            )
                    .select_from(Product)
                    .add_columns(search_query)  # Para fazer cross join implícito com search_query
                    .where(Product.atributos_tsv.op('@@')(search_query))
                    .order_by(literal_column("rank").desc())
                    .limit(5))

            resultado = self.db.execute(stmt).mappings().all()
            return [dict(row) for row in resultado]
        except Exception as e:
            logger.error(f"Erro na busca de produtos: {str(e)}")
            raise

    def find_product_by_between(self, parameters: dict) -> list[dict[str, Any]]:
        try:
            stmt = (select(Product.id,
                           Product.name,
                           Product.brand,
                           Product.category,
                           Product.description,
                           Product.price)
                    .where(Product.price.between(parameters['preco_minimo'], parameters['preco_maximo']))
                    .limit(5))
            resultado = self.db.execute(stmt).mappings().all()
            return [dict(row) for row in resultado]
        except Exception as e:
            logger.error(f"Erro na busca de produtos: {str(e)}")
            raise
