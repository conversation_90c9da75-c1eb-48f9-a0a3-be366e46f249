import json
import logging
from typing import Dict, Callable, Any

from sqlalchemy import select, func, literal_column
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from api.domain.base_agent_model import SpecialistAgentModel, Tool, WriterAgentModel, AgentExecutionHistory
from api.domain.product import Product
from api.schema.response import LLMResponse
from api.service.agent_execution_history_service import AgentExecutionHistoryService
from api.service.tool_service import ToolService
from api.util.llm_util import LlmUtil

logger = logging.getLogger(__name__)


class WriterAgentService:

    def __init__(self, db: Session):
        self.db = db

    def find_by_name(self, writer_agent_name: str) -> WriterAgentModel | None:
        try:
            stmt = (select(WriterAgentModel)
                    .where(WriterAgentModel.name == writer_agent_name))
            return self.db.execute(stmt).scalar_one_or_none()
        except SQLAlchemyError as e:
            logger.error(f"Erro ao buscar ManagerAgent por ID: {str(e)}")
            raise

    def tools_to_json(self, tools: list[Tool]) -> str:
        return json.dumps([
            {
                # "id": tool.id,
                "name": tool.name,
                "description": tool.description,
                "goals": tool.goals,
                "parameters": [
                    {
                        "id": p.id,
                        "label": p.label,
                        "data_type": p.data_type.value,
                        "type": p.type.value
                    } for p in tool.parameters
                ]
            } for tool in tools
        ], indent=2)

    def products_to_json(self, products: list[dict]) -> str:
        return json.dumps([
            {
                "id": product['id'],
                "name": product['name'],
                "description": product['description']
            } for product in products
        ], indent=2)

    def construir_prompt(self, prompt_template: str, contexto: dict) -> str:
        # Filtrar contexto relevante (ex.: palavras-chave relacionadas)
        # stm_summary = " ".join([turno.get("pergunta", "") for turno in contexto["stm"]])[:200]
        # ltm_summary = contexto["ltm"].get("categorias_preferidas", "")[:100]

        # return f"""
        # Pergunta: {pergunta}
        # Contexto recente: {stm_summary}
        # Preferências do usuário: {ltm_summary}
        # Agentes disponíveis: {}
        # """

        # contexto_prompt = contexto.intencao
        products = self.products_to_json(contexto['structured_result'])
        prompt = (prompt_template.replace("{query}", contexto['query'])
                  .replace("{intent}", contexto['intent'])
                  .replace("{structured_result}", products))
        return prompt

    def executa(self, writer_agent: WriterAgentModel, contexto: dict) -> str:
        try:
            prompt = self.construir_prompt(writer_agent.prompt_template,
                                           contexto)

            llm_util = LlmUtil()
            resposta = llm_util.call_llm_str(prompt)

            agent_execution_history = AgentExecutionHistory(
                agent_name=writer_agent.name,
                input=prompt,
                output=resposta
            )
            agent_execution_history_service = AgentExecutionHistoryService(self.db)
            agent_execution_history_service.save(agent_execution_history)

            return resposta
        except Exception as e:
            logger.error(f"Erro na execução do especialista: {str(e)}")
            raise
