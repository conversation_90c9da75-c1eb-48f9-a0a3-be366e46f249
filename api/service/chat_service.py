import logging
import traceback
from typing import Dict

from fastapi import HTTPException

from api.database import SessionLocal
from api.service.manager_agent_service import ManagerAgentService
from api.service.specialist_agent_service import SpecialistAgentService

logger = logging.getLogger(__name__)


class ChatService:
    def __init__(self):
        db = SessionLocal()
        self.manager_agent_service = ManagerAgentService(db)
        self.specialist_agent_service = SpecialistAgentService(db)

    def processar_pergunta(self, pergunta: str, conversation_id: str | None) -> Dict[str, str]:

        try:
            logger.info(f"Processando pergunta: {pergunta}")
            resposta = self.manager_agent_service.executa(pergunta, conversation_id)
            return resposta

        except HTTPException as e:
            # Propaga o erro HTTP original, não encapsula de novo!
            raise

        except Exception as e:
            tb = traceback.format_exc()
            raise HTTPException(status_code=500, detail=f"Erro interno: {str(e)}\nTraceback:\n{tb}")

    # def buscar_contexto(self, conversation_id: str) -> Dict[str, Any]:
    #     try:
    #         # STM: Buscar contexto recente no Redis (últimos 5 turnos)
    #         stm_key = f"conversa:{conversation_id}:stm"
    #         stm_data = await redis_client.lrange(stm_key, 0, 4)  # Últimos 5 turnos
    #         stm = [json.loads(turno) for turno in stm_data] if stm_data else []
    #
    #         # LTM: Buscar preferências ou histórico no PostgreSQL
    #         with SessionLocal() as db:
    #             query = text("SELECT preferencias FROM conversas WHERE id = :id")
    #             ltm_data = db.execute(query, {"id": conversation_id}).scalar()
    #             ltm = json.loads(ltm_data) if ltm_data else {}
    #
    #         return {"stm": stm, "ltm": ltm}
    #     except Exception as e:
    #         logger.error(f"Erro ao buscar contexto: {str(e)}")
    #         return {"stm": [], "ltm": {}}

# try:
#         logger.info(f"Enviando prompt para LLM: {prompt[:50]}...")
#         if "dados" in prompt.lower():
#             return {
#                 "agent": "especialista_dados",
#                 "tool": "sql_query",
#                 "query": "SELECT * FROM usuarios WHERE ativo = :ativo",
#                 "params": {"ativo": True}
#             }
#         return {"error": "Nenhuma ação identificada"}
#     except Exception as e:
#         logger.error(f"Erro na chamada à LLM: {str(e)}")
#         raise HTTPException(status_code=500, detail="Erro ao processar LLM")
