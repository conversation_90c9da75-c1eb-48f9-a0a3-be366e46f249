import json
import logging
from typing import Callable, Any

from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from api.domain.base_agent_model import Tool
from api.service.product_service import ProductService

logger = logging.getLogger(__name__)


class ToolService:

    def __init__(self, db: Session):
        self.db = db
        self.product_service = ProductService(self.db)
        self.metodos_executores: dict[str, Callable] = {
            "find_product_by_id": self.product_service.find_product_by_id,
            "find_product_by_identificacao_nominal": self.product_service.find_product_by_identificacao_nominal,
            "find_product_by_classificacao": self.product_service.find_product_by_classificacao,
            "find_product_by_atributos": self.product_service.find_product_by_atributos,
            "find_product_by_between": self.product_service.find_product_by_between
        }

    def find_by_name(self, tool_name: str) -> Tool | None:
        try:
            stmt = (select(Tool)
                    .where(Tool.name == tool_name))
            return self.db.execute(stmt).scalar_one_or_none()
        except SQLAlchemyError as e:
            logger.error(f"Erro ao buscar ManagerAgent por ID: {str(e)}")
            raise

    # async def executar_consulta_sql(self, query: str, params: Dict[str, Any]) -> str:
    #     try:
    #         with SessionLocal() as db:
    #             result = db.execute(text(query), params).fetchall()
    #             logger.info(f"Consulta SQL executada: {query}")
    #             return str(result)
    #     except Exception as e:
    #         logger.error(f"Erro na consulta SQL: {str(e)}")
    #         raise HTTPException(status_code=500, detail="Erro ao executar consulta")



    def executa_metodo(self, handler: str, parametros: dict):
        metodo = self.metodos_executores.get(handler)
        if not metodo:
            raise ValueError(f"Tool '{handler}' não registrada.")
        return metodo(parametros)

    def executa(self, tool) -> Any:
        try:
            resultado_metodo = self.executa_metodo(tool['name'], tool['params'])
            return resultado_metodo
        except Exception as e:
            logger.error(f"Erro na execução do especialista: {str(e)}")
            raise
