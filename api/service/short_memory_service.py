import json
import logging
from datetime import datetime
from typing import Optional

from redis import Redis

from api.domain.short_session_memory import ShortTermMemory, ShortTurn

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class ShortSessionMemoryService:
    def __init__(self):
        self.redis_client = Redis(host='localhost', port=6379, db=0)
        self.SHORT_TERM_MEMORY_KEY: str = "short_term_memory"

    def save(self, short_term_memory: ShortTermMemory):
        try:
            key = f"{self.SHORT_TERM_MEMORY_KEY}:{short_term_memory.customer}"
            value = short_term_memory.model_dump_json()

            self.redis_client.set(key, value)
            self.redis_client.expire(key, 3600)

            logger.info(f"save redis:{value}")

        except Exception as e:
            logger.error(f"Erro ao salvar sessão curta: {str(e)}")

    def save_turn(self, customer_id: str, question: str, answer: str):
        try:
            key = f"{self.SHORT_TERM_MEMORY_KEY}:{customer_id}:turns"

            new_turn = ShortTurn(
                date_time=datetime.now(),
                question=question,
                answer=answer
            )

            # Salva o novo turno no início
            self.redis_client.lpush(key, new_turn.model_dump_json())

            # Mantém só os 5 últimos turnos
            self.redis_client.ltrim(key, 0, 4)

            # Expira após 1 hora
            self.redis_client.expire(key, 3600)

        except Exception as e:
            logger.error(f"Erro ao salvar turno: {str(e)}")

    def get_last_turns(self, customer_id: str) -> list[ShortTurn]:
        try:
            key = f"{self.SHORT_TERM_MEMORY_KEY}:{customer_id}:turns"
            raw_turns = self.redis_client.lrange(key, 0, 4)

            return [ShortTurn(**json.loads(t)) for t in raw_turns] if raw_turns else []

        except Exception as e:
            logger.error(f"Erro ao recuperar turnos: {str(e)}")
            return []

    def get_by_customer(self, customer_id: str) -> Optional[ShortTermMemory]:
        try:
            key = f"{self.SHORT_TERM_MEMORY_KEY}:{customer_id}"
            value = self.redis_client.get(key)

            if value:
                return ShortTermMemory.model_validate_json(value)
            else:
                return None

        except Exception as e:
            logger.error(f"Erro ao recuperar sessão curta: {str(e)}")
            return None
