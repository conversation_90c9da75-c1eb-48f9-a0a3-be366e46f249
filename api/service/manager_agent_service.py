import logging
import traceback
from typing import Dict
from uuid import uuid4

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session, selectinload

from api.domain.base_agent_model import ManagerAgentModel, SpecialistAgentModel, AgentExecutionHistory
from api.domain.short_session_memory import ShortTermMemory
from api.schema.response import LLMResponse
from api.service.agent_execution_history_service import AgentExecutionHistoryService
from api.service.short_memory_service import ShortSessionMemoryService
from api.service.specialist_agent_service import SpecialistAgentService
from api.service.writer_agent_service import WriterAgentService
from api.service.context_service import ContextService
from api.util.llm_util import LlmUtil

logger = logging.getLogger(__name__)


class ManagerAgentService:
    def __init__(self, db: Session):
        self.db = db

    def find_by_id(self, manager_agent_id: str) -> ManagerAgentModel | None:
        try:
            stmt = (select(ManagerAgentModel)
                    .options(selectinload(ManagerAgentModel.specialist_agents))
                    .where(ManagerAgentModel.id == manager_agent_id))
            return self.db.execute(stmt).scalar_one_or_none()
        except SQLAlchemyError as e:
            logger.error(f"Erro ao buscar ManagerAgent por ID: {str(e)}")
            raise

    def executa(self, pergunta: str, customer_id: str | None) -> Dict[str, str]:

        try:
            logger.info(f"Processando pergunta: {pergunta}")

            manager_agent = self.find_by_id("941e9f44-6709-4f45-b9f9-0ce9d240a30c")

            customer_id = customer_id or str(uuid4())

            # Initialize context service for MVP context agent integration
            context_service = ContextService(self.db)

            # Get context from previous interactions (MVP approach)
            contexts = context_service.get_context(customer_id, pergunta)

            # Buscar STM (Redis) e LTM (PostgreSQL)
            short_memory_service = ShortSessionMemoryService()
            short_memory = short_memory_service.get_by_customer(customer_id)

            # Build base prompt
            base_prompt = self.construir_prompt(manager_agent.prompt_template,
                                               manager_agent.specialist_agents,
                                               pergunta,
                                               short_memory)

            # Enhance prompt with context (MVP context injection)
            prompt = context_service.inject_context(base_prompt, contexts)

            llm_util = LlmUtil()
            llm_response = llm_util.call_llm(prompt)

            # Handle case where LLM returns None
            if llm_response is None:
                logger.error("LLM returned None response")
                # Create a minimal fallback response
                llm_response = {
                    "intencao": "Buscar produtos",
                    "required_agents": [{"agente": "agente_especialista_produto", "motivo": "Buscar produtos solicitados"}],
                    "is_follow_up": False,
                    "end_of_conversation": False,
                    "reasoning": "Fallback response due to LLM error"
                }

            contexto = LLMResponse(**llm_response)

            # Capture manager agent execution in context (MVP approach)
            try:
                context_service.capture(
                    customer_id=customer_id,
                    agent_type="manager_agent",
                    query=pergunta,
                    response=str(contexto.model_dump_json())
                )
            except Exception as context_error:
                logger.warning(f"Context capture failed: {context_error}")

            agent_execution_history = AgentExecutionHistory(
                agent_name=manager_agent.name,
                input=prompt,
                output=contexto.model_dump_json()
            )
            agent_execution_history_service = AgentExecutionHistoryService(self.db)
            agent_execution_history_service.save(agent_execution_history)

            contexto.query = pergunta

            if not short_memory:
                short_memory = ShortTermMemory(customer=customer_id, turns=[])
                short_memory_service.save(short_memory)

            # Executar especialista
            specialist_agent_service = SpecialistAgentService(self.db)

            # Initialize resposta with default values
            resposta = contexto

            for agent in contexto.required_agents:
                specialist_agent = specialist_agent_service.find_by_name(agent['agente'])
                resposta = specialist_agent_service.executa(specialist_agent, contexto)

            resposta_dict: dict = {}
            resposta_dict['query'] = resposta.query
            resposta_dict['intent'] = resposta.intencao
            resposta_dict['structured_result'] = getattr(resposta, 'structured_result', None)

            writer_agent_service = WriterAgentService(self.db)
            writer_agent = writer_agent_service.find_by_name("agente_resposta_atendimento")
            resposta_final = writer_agent_service.executa(writer_agent, resposta_dict)

            short_memory_service.save_turn(customer_id, pergunta, resposta_final)

            # Retornar resposta formatada
            return {
                # "resposta": self.agente_resposta.formatar_resposta(resposta),
                "resposta": resposta_final,
                "conversation_id": customer_id
            }
            # logger.warning(f"Agente não encontrado: {contexto.agent}")
            # return {"resposta": "Desculpe, não posso responder a essa pergunta.", "conversation_id": conversation_id}

        except Exception as e:
            logger.error(f"Erro no processamento: {str(e)}")
            traceback.print_exc()
            raise RuntimeError("Erro ao executar o agente") from e

    def construir_prompt(self, prompt_template: str, specialist_agents: list[SpecialistAgentModel],
                         pergunta: str, short_session_memory: ShortTermMemory | None) -> str:
        # Filtrar contexto relevante (ex.: palavras-chave relacionadas)
        # stm_summary = " ".join([turno.get("pergunta", "") for turno in contexto["stm"]])[:200]
        # ltm_summary = contexto["ltm"].get("categorias_preferidas", "")[:100]
        if short_session_memory is not None:
            short_session_str = short_session_memory.model_dump_json(indent=2)
            prompt_template = prompt_template.replace(f"{short_session_memory}", short_session_str)
        specialist_agents_prompt = ", ".join([f"{spec.name} ({spec.description})" for spec in specialist_agents])

        # return f"""
        # Pergunta: {pergunta}
        # Contexto recente: {stm_summary}
        # Preferências do usuário: {ltm_summary}
        # Agentes disponíveis: {}
        # """
        prompt = (prompt_template
                  .replace("{pergunta}", pergunta)
                  .replace("{agentes_disponiveis}", specialist_agents_prompt))

        return prompt
